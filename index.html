<!doctype html>
<!doctype html>
<html lang="{{lang}}">
  <head>
    <!-- app-head -->

    <!--app-head-->

    <meta charset="UTF-8" />
    <link
      rel="icon"
      type="image/svg+xml"
      href="/biela_favicon_light.svg"
      media="(prefers-color-scheme: light)"
    />
    <link
      rel="icon"
      type="image/svg+xml"
      href="/biela_favicon_dark.svg"
      media="(prefers-color-scheme: dark)"
    />
    <link
      rel="icon"
      type="image/svg+xml"
      href="/biela_favicon_light.svg"
      media="(prefers-color-scheme: light)"
    />
    <link
      rel="icon"
      type="image/svg+xml"
      href="/biela_favicon_dark.svg"
      media="(prefers-color-scheme: dark)"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Preconnects for fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Google Fonts async loading -->
    <link
      href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;700&display=swap"
      rel="stylesheet"
      media="print"
      onload="this.media='all'"
    />
    <noscript>
      <link
        href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;700&display=swap"
        rel="stylesheet"
      />
    </noscript>

    <!-- Preload self-hosted font -->
    <link
      rel="preload"
      as="font"
      href="/fonts/Rexton/Rexton-Light.woff2"
      type="font/woff2"
      crossorigin="anonymous"
    />
    <!-- Preload thumbnail font -->
    <link rel="preload" as="image" href="https://storage-dev.biela.dev/affiliate/resources/1751109705242-thumbail-first-video.webp" />
    <!-- Defer local CSS by loading with media="print" -->
    <link
      rel="stylesheet"
      href="/src/index.css"
      media="print"
      onload="this.media='all'"
    />
    <noscript>
      <link rel="stylesheet" href="/src/index.css" />
    </noscript>

    <!-- Global site tag (gtag.js) -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-CSZ44DYPWS"
    ></script>
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());
    </script>

    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l !== "dataLayer" ? "&l=" + l : "";
        var gtmId = "%VITE_GOOGLE_TAG_MANAGER_ID%" || "GTM-5BXQMVKQ";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + gtmId + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer");
    </script>
    <!-- End Google Tag Manager -->

    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Biela.dev",
        "applicationCategory": "WebApplication",
        "applicationSubCategory": "AI Website Builder",
        "operatingSystem": "Web",
        "browserRequirements": "Requires modern web browser",
        "softwareVersion": "1.0.0",
        "url": "https://biela.dev",
        "screenshot": "https://biela.dev/assets/img/screenshots/homepage.png",
        "datePublished": "2024-12-01",
        "dateModified": "2025-05-01",
        "offers": {
          "@type": "Offer",
          "price": "0.00",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock"
        },
        "description": "Biela is an AI-powered web application that allows users to effortlessly create professional websites with minimal input. Ideal for entrepreneurs, educators, and developers who want to build and publish websites fast without coding.",
        "author": {
          "@type": "Organization",
          "name": "TeachMeCode Institute",
          "url": "https://teachmecode.ae"
        },
        "publisher": {
          "@type": "Organization",
          "name": "TeachMeCode Institute",
          "url": "https://teachmecode.ae"
        }
      }
    </script>
  </head>
  <body style="overflow: visible !important">
    <div id="root"><!--app-html--></div>

    <!-- GTM Yield Hijack Script (as you had it) -->
    <script>
      (function () {
        const pendingResolvers = new Set();
        function resolvePendingPromises() {
          document.removeEventListener(
            "visibilitychange",
            resolvePendingPromises
          );
          for (const resolve of pendingResolvers) resolve();
          pendingResolvers.clear();
        }

        function yieldToMain(options = {}) {
          let markName;
          options.mark = options.mark || "void";

          if (window.yieldCounter) {
            const eventName =
              options.event[0]?.[1] || options.event[0]?.event || "";
            yieldCounter[options.mark] = yieldCounter[options.mark] || 0;
            markName = [
              "yieldToMain",
              options.mark,
              ++yieldCounter[options.mark],
              eventName,
            ].join("-");
            performance.mark(markName + "-start");
          }

          const setMarkMeasure = () => {
            if (!markName) return;
            performance.mark(markName + "-end");
            performance.measure(markName, {
              start: markName + "-start",
              end: markName + "-end",
              detail: {
                devtools: {
                  dataType: "track-entry",
                  track: "GTM",
                  trackGroup: "Yielding",
                  color: "secondary-light",
                },
              },
            });
          };

          const promise = new Promise((resolve) => {
            pendingResolvers.add(resolve);
            if (document.visibilityState === "visible") {
              document.addEventListener(
                "visibilitychange",
                resolvePendingPromises
              );
              const schedulerPromise =
                "scheduler" in window && "yield" in scheduler
                  ? scheduler.yield()
                  : new Promise((resolve) => setTimeout(resolve, 0));
              schedulerPromise.then(() => {
                pendingResolvers.delete(resolve);
                resolve();
              });
            } else {
              resolvePendingPromises();
            }
          });

          if (options.priority === "user-blocking") {
            setMarkMeasure();
            return Promise.resolve();
          }

          return promise.then(setMarkMeasure);
        }

        const gtmPriorityEventMatches = [/^add_to_cart$/];

        function isPriorityEvent(e) {
          const eventName = e[0]?.[1] || e[0]?.event || "";
          return (
            typeof eventName === "string" &&
            gtmPriorityEventMatches.some((regex) => regex.test(eventName))
          );
        }

        function yieldDataLayerPush() {
          if (!window.dataLayer || typeof window.dataLayer.push !== "function")
            return;
          window.dataLayer.originalPush = window.dataLayer.push;
          window.dataLayer.push = function () {
            const e = Array.prototype.slice.call(arguments, 0);
            yieldToMain({
              mark: "dataLayer",
              priority: isPriorityEvent(e) ? "user-blocking" : "background",
              event: e,
            }).then(() => {
              window.dataLayer.originalPush.apply(window.dataLayer, e);
            });
          };
        }

        const yieldObserver = new MutationObserver(() => {
          if (document.readyState === "complete")
            return yieldObserver.disconnect();
          if (window.dataLayer && dataLayer.push !== Array.prototype.push) {
            yieldObserver.disconnect();
            yieldDataLayerPush();
          }
        });

        yieldObserver.observe(document.documentElement, {
          childList: true,
          subtree: true,
        });
      })();
    </script>

    <!-- App Entry -->
    <script type="module" src="/src/main.jsx"></script>

    <!-- GTM noscript fallback -->
    <noscript>
      <iframe
        height="0"
        src="https://www.googletagmanager.com/ns.html?id=%VITE_GOOGLE_TAG_MANAGER_ID%"
        style="display: none; visibility: hidden"
        width="0"
      ></iframe>
    </noscript>

    <!-- Lazy load Zendesk Widget after page load -->
    <script>
      window.addEventListener("load", () => {
        setTimeout(() => {
          const zendeskScript = document.createElement("script");
          zendeskScript.id = "ze-snippet";
          zendeskScript.src =
            "https://static.zdassets.com/ekr/snippet.js?key=9e8ddf01-c97a-41dd-8605-91c06f5fbe2f";
          zendeskScript.async = true;
          document.body.appendChild(zendeskScript);
        }, 2000);
      });
    </script>
  </body>
</html>
