import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { AnimatePresence, motion } from "framer-motion";
import { FaCheck, FaChevronDown, FaGlobe } from "react-icons/fa";

function LanguageSwitcher({ large = false, withBorder = false }) {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  const languages = [
    { code: "en", name: "English", flag: "/flags/en.png" },
    { code: "es", name: "<PERSON>spa<PERSON><PERSON>", flag: "/flags/es.png" },
    { code: "fr", name: "Français", flag: "/flags/fr.png" },
    { code: "de", name: "<PERSON>uts<PERSON>", flag: "/flags/de.png" },
    { code: "it", name: "Italiano", flag: "/flags/it.png" },
    { code: "pt", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", flag: "/flags/pt.png" },
    { code: "ru", name: "Русский", flag: "/flags/ru.png" },
    { code: "ro", name: "Română", flag: "/flags/ro.png" },
    { code: "zh", name: "中文", flag: "/flags/zh.png" },
    { code: "ja", name: "日本語", flag: "/flags/ja.png" },
    { code: "ko", name: "한국어", flag: "/flags/ko.png" },
    { code: "ar", name: "العربية", flag: "/flags/ar.png" },
    { code: "az", name: "Azərbaycan", flag: "/flags/az.png" },
    { code: "in", name: " हिन्दी", flag: "/flags/in.png" },
  ];

  const currentLanguage =
    languages.find((lang) => lang.code === i18n.language) || languages[0];

  const setUserLanguageCookie = (langCode) => {
    document.cookie = `user-language-cookie=${langCode}; path=/; max-age=31536000`;
  };

  const getUserLanguageCookie = () => {
    const match = document.cookie.match(
      /(^|;) ?user-language-cookie=([^;]*)(;|$)/,
    );
    return match ? match[2] : null;
  };

  const changeLanguage = (langCode) => {
    const currentPath = window.location.pathname;
    const hash = window.location.hash;

    const languagePrefixRegex = /^\/([a-z]{2})(?:\/|$)/;
    const isLanguagePrefix = languagePrefixRegex.test(currentPath);

    let pathWithoutLanguage;
    if (isLanguagePrefix) {
      const firstSegment = currentPath.split("/")[1];
      if (languages.some((lang) => lang.code === firstSegment)) {
        pathWithoutLanguage = currentPath.replace(
          languagePrefixRegex,
          (_match, _langCode) => "/",
        );
      } else {
        pathWithoutLanguage = currentPath;
      }
    } else {
      pathWithoutLanguage = currentPath;
    }

    let newPath = `/${langCode}${pathWithoutLanguage === "/" ? "" : pathWithoutLanguage}`;
    if (hash) {
      newPath = newPath.replace(/\/$/, "") + hash;
    }

    setUserLanguageCookie(langCode);
    window.history.pushState(null, "", newPath);
    i18n.changeLanguage(langCode);
    setIsOpen(false);
  };

  useEffect(() => {
    const pathLang = window.location.pathname.split("/")[1];
    const isLangInUrl = languages.find((lang) => lang.code === pathLang);
    const cookieLang = getUserLanguageCookie();

    if (isLangInUrl) {
      if (i18n.language !== pathLang) {
        i18n.changeLanguage(pathLang);
      }
    } else if (cookieLang && i18n.language !== cookieLang) {
      i18n.changeLanguage(cookieLang).then(() => {
        // dacă nu există limbă în URL, facem redirect cu prefixul limbii din cookie
        const newUrl = `/${cookieLang}${window.location.pathname}${window.location.hash}`;
        window.location.replace(newUrl);
      });
    }
  }, [i18n.language]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        title={"Language Selector"}
        onClick={() => setIsOpen(!isOpen)}
        className={`text-sm px-4 py-2 rounded-md sm:w-[150px] relative text-white bg-[#19263d] flex gap-2 items-center hover:bg-white/20 transition-colors cursor-pointer`}
      >
        <FaGlobe className="text-sm" />
        <span
          className={`max-sm:hidden flex-1 flex flex-row gap-2  items-center font-light text-sm ${large && "w-full text-left"}`}
        >
          {currentLanguage.name}
        </span>
        <FaChevronDown
          className={`text-xs transition-transform ${isOpen ? "rotate-180" : ""}`}
        />
      </button>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.1 }}
            className={`absolute right-0 mt-2 w-[150px] bg-gray-900 rounded-lg border border-gray-800 overflow-x-hidden vertical-scroll overflow-y-auto z-50 ${large && "w-full"}`}
          >
            <div className="py-1 max-h-[300px]">
              {languages.map((language) => (
                <button
                  title={language.name}
                  key={language.code}
                  onClick={() => changeLanguage(language.code)}
                  className="w-full text-left pl-[16px] cursor-pointer pr-[15px] py-2 text-white/80 hover:bg-gray-800/50 transition-colors flex items-center justify-between"
                >
                  <span className="font-light text-sm flex flex-row gap-2 items-center">
                    {language.name}
                  </span>
                  {language.code === i18n.language && (
                    <FaCheck className="text-green-400 text-sm" />
                  )}
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default LanguageSwitcher;
