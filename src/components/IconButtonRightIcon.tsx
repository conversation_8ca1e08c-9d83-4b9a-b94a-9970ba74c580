import React from "react";

const variantClasses = {
  primary: "bg-transparent",
};

const IconButton = ({
  icon,
  onClick,
  variant = "secondary",
  tooltip,
  text,
  ...rest
}) => {
  const buttonClasses = variantClasses[variant];

  const extraStyles =
    variant === "download"
      ? {
          backgroundImage:
            "linear-gradient(45deg, rgba(255,255,255,0.05) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.05) 50%, rgba(255,255,255,0.05) 75%, transparent 75%, transparent)",
          backgroundSize: "8px 8px",
        }
      : {};

  return (
    <button
      title={tooltip || text}
      className={`${buttonClasses} group cursor-pointer`}
      onClick={onClick}
      aria-label={tooltip || text}
      style={extraStyles}
      {...rest}
    >
      <span className="flex items-center justify-center">
        {text && <span className="mr-2 font-normal text-[16px]">{text}</span>}
        {icon}
      </span>
    </button>
  );
};

export default IconButton;
