import React, { useState } from "react";
import { motion } from "framer-motion";
import HeroHeading from "./hero/HeroHeading";
import PromptInput from "./hero/PromptInput";
import SuggestionScroller from "./hero/SuggestionScroller";
import AnimationStyles from "./hero/AnimationStyles";

function HeroSection() {
  const [prompt, setPrompt] = useState("");

  // Function to scroll to the demo section
  const scrollToDemo = () => {
    const demoSection = document.querySelector("#demo");
    if (demoSection) {
      demoSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section
      id="hero-section"
      className="pt-[100px] md:pt-[152px] relative flex flex-col justify-center "
    >
      <div className="container relative z-10 text-center max-w-6xl min-h-80 mx-auto px-4">
        {/* Hero Heading */}
        <HeroHeading />
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="max-w-4xl mx-auto z-[20]"
        >
          <PromptInput prompt={prompt} setPrompt={setPrompt} />
        </motion.div>
      </div>
      <div className="container mx-auto px-6">
        <SuggestionScroller setPrompt={setPrompt} />
      </div>
      <AnimationStyles />
    </section>
  );
}

export default HeroSection;
