import React, { useRef, useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  FaUsers,
  FaRocket,
  FaTools,
  FaHammer,
  FaChevronLeft,
  FaChevronRight,
  FaCloudDownloadAlt,
  FaShare,
} from "react-icons/fa";
import { FaEarthEurope } from "react-icons/fa6";
import { useTranslation } from "react-i18next";

const slideConfigs = [
  {
    titleKey: "slides.launchProject",
    embedUrl: "https://www.youtube.com/embed/V3TL82ChXxI?si=gupbfdGzjNoiosEh",
    Icon: FaRocket,
  },
  {
    titleKey: "slides.deployProject",
    embedUrl: "https://www.youtube.com/embed/XkUCFrC3-II?si=cg_L3SUbBzt9Zahm",
    Icon: FaCloudDownloadAlt,
  },
  // AFTER
  {
    titleKey: "slides.downloadAndImport",
    embedUrl: "https://www.youtube.com/embed/4FpN1Jx4tqg",
    Icon: FaCloudDownloadAlt,
  },
  {
    titleKey: "slides.shareProject",
    embedUrl: "https://www.youtube.com/embed/2pcT8VmgJvQ",
    Icon: FaShare,
  },
  {
    titleKey: "slides.newHistoryFeature",
    embedUrl: "https://www.youtube.com/embed/xvfATqMIbMo",
    Icon: FaEarthEurope,
  },
  {
    titleKey: "slides.fixPreview",
    embedUrl: "https://www.youtube.com/embed/_gluNR5JNA4",
    Icon: FaTools,
  },
  {
    titleKey: "slides.signUp",
    embedUrl: "https://www.youtube.com/embed/CBdwDPDw6KY",
    Icon: FaUsers,
  },
  {
    titleKey: "slides.buildWebsite",
    embedUrl: "https://www.youtube.com/embed/P_aeMJhsYMY",
    Icon: FaHammer,
  },
];

const HowToUseBiela = () => {
  const { t } = useTranslation();
  const carouselRef = useRef(null);
  const [width, setWidth] = useState(0);

  useEffect(() => {
    if (carouselRef.current) {
      const scrollWidth = carouselRef.current.scrollWidth;
      const offsetWidth = carouselRef.current.offsetWidth;
      setWidth(scrollWidth - offsetWidth);
    }
  }, []);

  const scrollBy = (distance) => {
    if (carouselRef.current) {
      carouselRef.current.scrollBy({ left: distance, behavior: "smooth" });
    }
  };

  return (
    <section
      id="how-to-use-biela"
      className="py-16 relative overflow-x-hidden overflow-y-visible"
    >
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 relative z-10 max-w-[1644px] sm:max-w-[1660px] lg:max-w-[1676px]">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-10"
        >
          <div className="inline-flex items-center gap-2 glass-card px-6 py-2 mb-4">
            <FaRocket className="text-accent" size={26} />
            <span className="text-gray-400 font-light text-[13px] rexton-light">
              {t("howToUseBiela.subtitle")}
            </span>
          </div>
          <h2 className="text-4xl font-light mb-3 rexton-light">
            {t("howToUseBiela.title")}
          </h2>
          <p className="text-gray-400 font-light max-w-6xl text-[14px] mx-auto rexton-light">
            {t("howToUseBiela.description")}
          </p>
        </motion.div>

        <div className="relative overflow-visible pb-6">
          <motion.button
            title={"Chevron Left"}
            onClick={() => scrollBy(-400)}
            whileHover={{ scale: 1.1 }}
            className="cursor-pointer absolute left-0 top-1/2 -translate-y-1/2 bg-navy-800/50 hover:bg-navy-800/70 text-white p-2 rounded-full z-20 border border-transparent hover:border-green-500"
          >
            <FaChevronLeft size={20} />
          </motion.button>
          <div
            ref={carouselRef}
            className="flex gap-6 overflow-x-scroll overflow-y-visible scroll-smooth max-w-[1536px] mx-[50px] hide-scrollbar cursor-grab"
          >
            <motion.div
              drag="x"
              dragConstraints={{ right: 0, left: -width }}
              whileTap={{ cursor: "grabbing" }}
              className="flex gap-6"
            >
              {slideConfigs.map(({ titleKey, embedUrl, Icon }, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  whileHover={{ zIndex: 1 }}
                  className="hover\:scale-105 glass-card p-8 relative flex-shrink-0 w-[400px] bg-gradient-to-br from-[#1A1D2E]/50 to-[#161926]/50 rounded-lg overflow-hidden origin-center"
                >
                  <div className="absolute -bottom-16 -right-16 text-[200px] opacity-5">
                    <Icon />
                  </div>
                  <div className="relative z-10 flex flex-col h-full justify-between">
                    <div className="flex gap-3 items-start mb-3">
                      <Icon className="text-accent min-w-[24px]" size={24} />
                      <h3 className="text-[16px] font-light rexton-light">
                        {t(titleKey)}
                      </h3>
                    </div>
                    <iframe
                      src={embedUrl}
                      title={t(titleKey)}
                      className="w-full h-64 rounded-lg"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
          <motion.button
            title={"Chevron Right"}
            onClick={() => scrollBy(400)}
            whileHover={{ scale: 1.1 }}
            className="cursor-pointer absolute right-0 top-1/2 -translate-y-1/2 bg-navy-800/50 hover:bg-navy-800/70 text-white p-2 rounded-full z-20 hover:border-green-500 border border-transparent"
          >
            <FaChevronRight size={20} />
          </motion.button>
        </div>
      </div>
    </section>
  );
};

export default HowToUseBiela;
