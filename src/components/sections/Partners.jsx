import React from "react";
import { motion } from "framer-motion";
import { FaHandshake, FaTrophy } from "react-icons/fa";
import { useTranslation } from "react-i18next";

function Partners() {
  const { t } = useTranslation();

  const partners = [
    {
      name: "TeachMeCode",
      logo: "/teachmecode.svg",
      color: "from-[#00A4EF]/20 to-[#7FBA00]/20",
    },
    {
      name: "UOWD",
      logo: "/uowd.svg",
      color: "from-[#C5A572]/20 to-[#D4AF37]/20",
    },
    {
      name: "UOB",
      logo: "/uob.svg",
      color: "from-[#1F70C1]/20 to-[#1F70C1]/20",
    },
    {
      name: "<PERSON>",
      logo: "/maximus.svg",
      color: "from-purple-500/20 to-blue-500/20",
    },
    {
      name: "Log<PERSON>",
      logo: "/logx.svg",
      color: "from-[#4285F4]/20 to-[#34A853]/20",
    },
    {
      name: "Unity",
      logo: "/unity.svg",
      color: "from-[#003087]/20 to-[#009cde]/20",
    },
    {
      name: "HP",
      logo: "/hewlett-packard.svg",
      color: "from-[#000000]/20 to-[#333333]/20",
    },
    /* {
      name: "Paypal",
      logo: "/hewlett-packard.svg",
      color: "from-[#000000]/20 to-[#333333]/20",
    },*/
    {
      name: "Getty Images",
      logo: "/getty-images.svg",
      color: "from-[#0096D6]/20 to-[#0096D6]/20",
    },
    {
      name: "Cloudflare",
      logo: "/cloudflare.svg",
      color: "from-[#FF9900]/20 to-[#232F3E]/20",
    },
    {
      name: "PWC",
      logo: "/pwc.svg",
      color: "from-accent/20 to-accent-purple/20",
    },
  ];

  return (
    <section id="partners" className="pt-[72px] pb-32 relative overflow-hidden">
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center flex flex-col items-center mb-10 space-y-4"
        >
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            style={{ letterSpacing: "-4.5px" }}
            className="text-md md:text-[30px] font-light mx-auto rexton-light"
          >
            {t("partners.title")}
          </motion.h1>
          <motion.p
            className="text-sm md:text-xl manrope-light font-light mb-[48px]"
            style={{ color: "rgb(212, 212, 212)" }}
          >
            {t("partners.subtitle")}
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-5 min-[1300px]:!grid-cols-10 gap-6">
          {partners.map((partner, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="flex justify-center items-center"
            >
              <img
                loading="lazy"
                src={partner.logo}
                alt={partner.name}
                height={50}
                width={90}
                className={`h-[50px] w-auto max-w-[110px] text-[#A3A3A3] hover:text-white transition-colors 
                    ${partner.name === "Cloudflare" ? "h-[90px]" : "h-12"} ${partner.name === "TeachMeCode" ? "max-w-[150px]" : "h-12"} ${partner.name === "PWC" ? "max-h-[40px]" : "h-12"} ${partner.name === "LogX" ? "max-h-[40px]" : "h-12"}`}
              />
            </motion.div>
          ))}
        </div>

        {/* <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6 }}
          className="text-center mt-8"
        >
          <p className="text-gray-400 font-light">
            {t("partners.joinNetwork")}
          </p>
        </motion.div>*/}
      </div>
    </section>
  );
}

export default Partners;
