import { useTranslation } from "react-i18next";
import React from "react";
import { BillingToggle } from "./BillingToggle.jsx";
import { PlansList } from "./PlansList.jsx";
import ThankYouModal from "./ThankYouModal.jsx";
import { usePaymentFlow } from "./utils/usePaymentFlow.js";
import { useBillingData } from "./utils/useBillingData.js";
import { useScreenSize } from "./utils/useScreenSize.js";
import { motion } from "framer-motion";
import { ShortEvents } from "../../short-events.js";
import CalloutBar from "../../CalloutBar.jsx";

function Billing() {
  const { t } = useTranslation();
  const { isMobile } = useScreenSize();
  const { billingCycle, setBillingCycle, stripePlans, plansLoading } =
    useBillingData();

  const { showThankYouModal, setShowThankYouModal, receiptData } =
    usePaymentFlow({ plansLoading });

  return (
    <section
      id="pricing"
      className="container mx-auto px-4 pt-[72px] sm:px-8 max-w-7xl rounded-xl relative "
    >
      <div className="text-white space-y-12">
        <div className="text-center space-y-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            style={{ letterSpacing: "-4.5px" }}
            className="text-md md:text-[30px] font-light mx-auto rexton-light"
          >
            {t("levelOfAmbition", "What's your level of ambition?")}
          </motion.h2>
          <motion.p
            className="text-sm md:text-xl manrope-light font-light mb-[48px]"
            style={{ color: "rgb(212, 212, 212)" }}
          >
            {t("startGrowScale", "Start. Grow. Scale.")}
          </motion.p>
        </div>
        <div className="flex items-center justify-center gap-4 mb-[52px] h-fit sm:flex-row flex-col">
          <BillingToggle
            stripePlans={stripePlans}
            billingCycle={billingCycle}
            setBillingCycle={setBillingCycle}
          />
        </div>
        <CalloutBar />

        <PlansList
          plans={stripePlans}
          loading={plansLoading}
          isMobile={isMobile}
          billingCycle={billingCycle}
        />

        <ThankYouModal
          isOpen={showThankYouModal}
          onClose={() => setShowThankYouModal(false)}
          receiptData={receiptData}
        />
      </div>
    </section>
  );
}

export default Billing;
