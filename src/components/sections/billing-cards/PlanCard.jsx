import { motion } from "framer-motion";
import { FaGift, FaInfinity, FaRobot } from "react-icons/fa";
import { AVAILABLE_ICONS, formatNumber, translationKeys } from "./helper.jsx";
import { useTranslation } from "react-i18next";
import { ShortEvents } from "../../short-events.js";

export const PlanCard = ({ plan, index, billingCycle, onSubscribe }) => {
  const { t } = useTranslation();
  const { isUser } = ShortEvents();
  const getIcon = (iconName) => {
    const found = AVAILABLE_ICONS.find((item) => item.name === iconName);
    if (found) {
      const IconComponent = found.icon;
      return (
        <IconComponent
          className={plan?.isPopular ? "text-green-400" : "text-gray-400"}
        />
      );
    }
    return null;
  };

  const periodMultiplier = billingCycle === "yearly" ? 12 : 1;
  const monthlyTokens = +plan.tokenAllocation;
  const pricePerMillion = (
    (plan.price / (monthlyTokens * periodMultiplier)) *
    1_000_000
  ).toFixed(2);

  function getMonthlyPriceWithoutDiscount(discountedTotal) {
    const fullTotal = discountedTotal / 0.9;
    const monthlyPrice = fullTotal / 12;
    return Math.ceil(monthlyPrice); // rotunjit în sus la cel mai apropiat întreg
  }

  const isUnlimitedPlan = index === 2;

  return (
    <motion.div
      className={`relative  backdrop-blur-sm rounded-2xl border transition-all duration-300 p-8 ${plan?.isPopular ? "bg-[#19243b] border-[#4ade80]" : "bg-[#19263d] border-[#1c2635]"}`}
    >
      {plan?.isPopular && (
        <div className="absolute -top-3 right-6">
          <span className="bg-[#4ade80] text-black text-xs font-medium px-3 py-1 rounded-full">
            {t("billings.mostPopular")}
          </span>
        </div>
      )}
      <div className={"flex items-center gap-3 mb-8"}>
        {getIcon(plan.icon)}
        <h3
          className="text-white"
          style={{
            fontFamily: "Manrope, sans-serif",
            fontSize: "24px",
            fontWeight: "300",
            fontFeatureSettings: "normal",
            fontVariationSettings: "normal",
          }}
        >
          {plan.name}
        </h3>
      </div>

      <div className="mb-8">
        <div className="flex items-baseline gap-2 mb-2">
          <span
            className="text-white"
            style={{
              fontFamily: "Manrope, sans-serif",
              fontSize: "36px",
              fontWeight: "300",
              lineHeight: "1",
            }}
          >
            $ {plan.price.toLocaleString("en-US")}
          </span>
          <span
            className="text-gray-400"
            style={{
              fontFamily: "Manrope, sans-serif",
              fontSize: "16px",
              fontWeight: "300",
            }}
          >
            /
            {billingCycle === "monthly"
              ? t("billings.month")
              : t("billings.year")}
          </span>
        </div>

        {billingCycle !== "monthly" && (
          <div className="flex items-center gap-2">
            <span
              className="text-gray-500 line-through"
              style={{
                fontFamily: "Manrope, sans-serif",
                fontSize: "14px",
                fontWeight: "300",
              }}
            >
              ${getMonthlyPriceWithoutDiscount(plan.price)}
            </span>
            <span
              className="text-green-400"
              style={{
                fontFamily: "Manrope, sans-serif",
                fontSize: "14px",
                fontWeight: "300",
              }}
            >
              {t("atPriceOf")}${" "}
              {(plan.price / 12).toLocaleString("en-US", {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}{" "}
              /{t("billings.month")}
            </span>
          </div>
        )}
      </div>

      <div className="mb-8">
        <div className="flex items-center gap-2 text-green-400 mb-4">
          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
          {isUnlimitedPlan ? (
            <span
              style={{
                fontFamily: "Manrope, sans-serif",
                fontSize: "14px",
                fontWeight: "400",
              }}
            >
              {t("billings.unlimited", "UNLIMITED")}{" "}
              {t("planCard.tokens", "Tokens")}{" "}
              {billingCycle === "monthly"
                ? t("planCard.perMonth", "per month")
                : t("planCard.perYear", "per year")}
            </span>
          ) : (
            <span
              style={{
                fontFamily: "Manrope, sans-serif",
                fontSize: "14px",
                fontWeight: "400",
              }}
            >
              {formatNumber(plan?.tokenAllocation)}{" "}
              {t("planCard.tokens", "Tokens")}{" "}
              {billingCycle === "monthly"
                ? t("planCard.perMonth", "per month")
                : t("planCard.perYear", "per year")}
            </span>
          )}
        </div>
        <div className="space-y-3">
          <div
            key={index}
            className="text-gray-300"
            style={{
              fontFamily: "Manrope, sans-serif",
              fontSize: "14px",
              fontWeight: "300",
            }}
          >
            {isUnlimitedPlan
              ? t("planCard.dedicatedCto", "Dedicated CTO - 24 Hours")
              : t("planCard.perMillionTokens", {
                  defaultValue: "$ {{price}} / 1M tokens",
                  price: pricePerMillion,
                })}
          </div>
        </div>
      </div>

      {isUser ? (
        <button
          title={
            plan.currentPlan
              ? t("billings.currentPlan")
              : t("billings.upgradePlan")
          }
          onClick={() =>
            !plan.currentPlan &&
            onSubscribe(plan.stripePlanId, plan.stripePriceId)
          }
          className={`w-full cursor-pointer flex-1 py-3 rounded-xl transition-all duration-300 ${
            plan.currentPlan
              ? "bg-gray-700 text-gray-400 cursor-not-allowed"
              : plan?.isPopular
                ? "bg-green-500 hover:bg-green-600 text-dark-900"
                : "bg-blue-500 hover:bg-[#23a8ea] text-white hover:text-black"
          }`}
          style={{
            fontFamily: "Manrope, sans-serif",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          {plan.currentPlan
            ? t("billings.currentPlan")
            : t("billings.upgradePlan")}
        </button>
      ) : (
        <div className="flex w-full gap-2">
          <a
            href={import.meta.env.VITE_IDE_URL + "/register"}
            style={{
              fontFamily: "Manrope, sans-serif",
              fontSize: "14px",
              fontWeight: "500",
            }}
            className={`w-full text-center cursor-pointer flex-1 py-3 rounded-xl transition-all duration-300 ${
              plan.currentPlan
                ? "bg-gray-700 text-gray-400 cursor-not-allowed"
                : plan?.isPopular
                  ? "bg-[#4ade80] hover:text-white hover:bg-green-600 text-black"
                  : "bg-blue-500 hover:bg-blue-600 text-white"
            }`}
          >
            {t("startFreeNoCreditCard", "Start Free - No Credit Card")}
          </a>
        </div>
      )}
    </motion.div>
  );
};
