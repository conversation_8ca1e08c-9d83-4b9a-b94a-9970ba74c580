import { motion } from "framer-motion";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

export const BillingToggle = ({
  stripePlans,
  billingCycle,
  setBillingCycle,
}) => {
  const { t } = useTranslation();

  const monthlyRef = useRef(null);
  const yearlyRef = useRef(null);
  const [sliderStyle, setSliderStyle] = useState({ left: 0, width: 0 });

  useEffect(() => {
    const ref =
      billingCycle === "monthly" ? monthlyRef.current : yearlyRef.current;
    if (ref) {
      const rect = ref.getBoundingClientRect();
      const parentRect = ref.parentElement?.getBoundingClientRect();
      if (rect && parentRect) {
        setSliderStyle({
          left: rect.left - parentRect.left,
          width: rect.width,
        });
      }
    }
  }, [billingCycle]);

  useEffect(() => {
    const raf = requestAnimationFrame(() => {
      const ref =
        billingCycle === "monthly" ? monthlyRef.current : yearlyRef.current;
      if (ref) {
        const rect = ref.getBoundingClientRect();
        const parentRect = ref.parentElement?.getBoundingClientRect();
        if (rect && parentRect) {
          setSliderStyle({
            left: rect.left - parentRect.left,
            width: rect.width,
          });
        }
      }
    });
    return () => cancelAnimationFrame(raf);
  }, [stripePlans?.length, t]);

  if (stripePlans?.length === 0)
    return <div className={"min-h-[66px] mb-12"}></div>;
  return (
    <div className="flex items-center justify-center mb-0">
      <div className="bg-[#1c2634] backdrop-blur-sm rounded-full p-1 border border-[#232d3a]">
        <div className="flex">
          <button
            title={t("planCard.monthly", "Monthly")}
            onClick={() => setBillingCycle("monthly")}
            className={`px-6 py-2 rounded-full transition-all duration-300 cursor-pointer ${
              billingCycle === "monthly"
                ? "bg-[#3a4550] text-white shadow-lg"
                : "text-gray-400 hover:text-white"
            }`}
            style={{
              fontFamily: "Manrope, sans-serif",
              fontSize: "14px",
              fontWeight: "300",
            }}
          >
            {t("planCard.monthly", "Monthly")}
          </button>
          <button
            title={t("planCard.yearly", "Yearly")}
            onClick={() => setBillingCycle("yearly")}
            className={`px-6 py-2 rounded-full transition-all duration-300 relative cursor-pointer ${
              billingCycle === "yearly"
                ? "bg-green-500 text-white shadow-lg"
                : "text-gray-400 hover:text-white"
            }`}
            style={{
              fontFamily: "Manrope, sans-serif",
              fontSize: "14px",
              fontWeight: "300",
            }}
          >
            {t("planCard.yearly", "Yearly")}
            <span className="absolute -top-3 left-[70%] text-black whitespace-nowrap bg-green-400 text-dark-900 text-xs px-2 py-0.5 rounded-full font-medium">
              {t("planCard.save", "SAVE 10%")}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};
