import React, { useState, useEffect, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Pop<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { useTranslation } from "react-i18next";
import { formatNumber } from "./helper.jsx";

const Confetti = memo(() => {
  const confettiPieces = Array.from({ length: 100 }).map((_, i) => {
    const colors = ["#4ade80", "#22c55e", "#16a34a", "#ffffff", "#d4d4d8"];
    return {
      id: i,
      x: Math.random() * 100,
      y: -10 - Math.random() * 20,
      size: 5 + Math.random() * 10,
      color: colors[Math.floor(Math.random() * colors.length)],
      rotation: Math.random() * 360,
      delay: Math.random() * 0.5,
      duration: 3 + Math.random() * 3,
    };
  });

  return (
    <div
      className="fixed inset-0 pointer-events-none overflow-hidden"
      style={{ zIndex: 1000 }}
    >
      {confettiPieces.map((piece) => (
        <motion.div
          key={piece.id}
          initial={{
            x: `${piece.x}vw`,
            y: `${piece.y}vh`,
            rotate: piece.rotation,
            opacity: 1,
          }}
          animate={{
            y: "120vh",
            x: `${piece.x + (Math.random() * 20 - 10)}vw`,
            rotate: piece.rotation + (Math.random() > 0.5 ? 180 : -180),
            opacity: 0,
          }}
          transition={{
            duration: piece.duration,
            ease: [0.1, 0.4, 0.6, 1],
            delay: piece.delay,
          }}
          style={{
            position: "absolute",
            width: piece.size,
            height: piece.size * (0.6 + Math.random() * 0.8),
            backgroundColor: piece.color,
            borderRadius: Math.random() > 0.5 ? "50%" : "0%",
            willChange: "transform",
          }}
        />
      ))}
    </div>
  );
});

const OrderDetails = memo(({ receiptData }) => {
  const { t } = useTranslation();

  return (
    <div className="bg-[#0F1F3F] p-5 rounded-xl border border-green-500/20 mb-6">
      <h3 className="text-lg text-gray-300 mb-4 font-light flex items-center gap-1">
        <PartyPopper className="w-4 h-4 text-green-400" />
        <span>{t("billings.purchaseDetails")}</span>
      </h3>

      <div className="space-y-3">
        <div className="flex justify-between"></div>
        <div className="flex justify-between">
          <span className="text-gray-400">{t("billings.dateLabel")}</span>
          <span className="text-white">{receiptData.date}</span>
        </div>
        {receiptData?.planName && (
          <div className="flex justify-between">
            <span className="text-gray-400">{t("billings.planLabel")}</span>
            <span className="text-white">{receiptData.planName}</span>
          </div>
        )}
        <div className="flex justify-between">
          <span className="text-gray-400">{t("billings.amountLabel")}</span>
          <span className="text-white">
            ${receiptData.amount.toLocaleString("en-US")}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-400">{t("billings.tokensUpperCase")}</span>
          <span className="text-white flex items-center gap-1">
            {formatNumber(receiptData.tokens)}
            <Sparkles className="w-4 h-4 text-green-400" />
          </span>
        </div>
      </div>
    </div>
  );
});

const WhatsNextSection = memo(() => {
  const { t } = useTranslation();

  return (
    <div className="mb-6">
      <h3 className="text-lg text-gray-300 mb-3 font-light flex items-center gap-1">
        <Star className="w-4 h-4 text-green-400" />
        <span>{t("billings.whatsNext")}</span>
      </h3>
      <ul className="space-y-2 text-gray-300 font-light">
        <li className="flex items-start gap-2">
          <div className="w-5 h-5 rounded-full bg-green-500/20 flex items-center justify-center mt-0.5">
            <CheckCircle className="w-3 h-3 text-green-500" />
          </div>
          <span>{t("billings.yourTokensAvailable")}</span>
        </li>
        <li className="flex items-start gap-2">
          <div className="w-5 h-5 rounded-full bg-green-500/20 flex items-center justify-center mt-0.5">
            <CheckCircle className="w-3 h-3 text-green-500" />
          </div>
          <span>{t("billings.purchaseDetailsStored")}</span>
        </li>
        <li className="flex items-start gap-2">
          <div className="w-5 h-5 rounded-full bg-green-500/20 flex items-center justify-center mt-0.5">
            <CheckCircle className="w-3 h-3 text-green-500" />
          </div>
          <span>{t("billings.viewPurchaseHistory")}</span>
        </li>
      </ul>
    </div>
  );
});

export default function ThankYouModal({ isOpen = true, onClose, receiptData }) {
  const [showConfetti, setShowConfetti] = useState(false);
  const [hasPushedPurchaseEvent, setHasPushedPurchaseEvent] = useState(false);

  const { t } = useTranslation();
  let data = receiptData;

  const date = new Date(receiptData?.date);

  data = {
    ...data,
    date: date.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    }),
  };

  useEffect(() => {
    let confettiTimer;
    let copyTimer;

    if (isOpen && receiptData && !hasPushedPurchaseEvent) {
      setHasPushedPurchaseEvent(true);
      setShowConfetti(true);

      window.dataLayer = window.dataLayer || [];

      const parsedAmount = parseFloat(receiptData.amount);

      window.dataLayer.push({
        event: "purchase",
        ecommerce: {
          transaction_id: receiptData.paymentId,
          value: parsedAmount,
          currency: receiptData.currency || "",
          coupon: "",
          items: [
            {
              item_id:
                receiptData.planName?.toLowerCase().replace(/\s+/g, "_") ||
                "extra_tokens",
              item_name: receiptData.planName || "Extra tokens",
              coupon: "",
              index: 0,
              item_category: "Package",
              price: parsedAmount,
              quantity: 1,
            },
          ],
        },
      });

      confettiTimer = setTimeout(() => {
        setShowConfetti(false);
      }, 4000);

      return () => {
        clearTimeout(confettiTimer);
      };
    }

    return () => {
      if (copyTimer) clearTimeout(copyTimer);
      if (confettiTimer) clearTimeout(confettiTimer);
    };
  }, [isOpen, receiptData, hasPushedPurchaseEvent]);

  const fadeInAnimation = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.2 },
  };

  const modalAnimation = {
    initial: { scale: 0.95, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.95, opacity: 0 },
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  };

  const contentAnimation = {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.4 },
  };

  return (
    <>
      <AnimatePresence>{showConfetti && <Confetti />}</AnimatePresence>

      <AnimatePresence>
        {isOpen && (
          <>
            <motion.div
              {...fadeInAnimation}
              className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 m-0"
              onClick={onClose}
            />

            <motion.div
              {...modalAnimation}
              className="fixed inset-0 flex items-center justify-center px-4 z-50"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="w-full max-w-md bg-gradient-to-b from-[#0A1730] to-[#081020] rounded-2xl shadow-2xl border border-green-500/20 overflow-hidden">
                <motion.div
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: 1 }}
                  transition={{ duration: 0.3 }}
                  className="h-2 bg-gradient-to-r from-green-400 via-green-500 to-green-400 origin-left"
                />

                <div className="p-6">
                  <motion.div
                    {...contentAnimation}
                    className="text-center mb-6"
                  >
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-500/20 mb-4">
                      <CheckCircle className="w-10 h-10 text-green-500" />
                    </div>
                    <h2 className="text-3xl font-light text-white mb-2">
                      {t("billings.thankYou")}
                    </h2>
                    <p className="text-gray-300 font-light">
                      {t("billings.purchaseSuccessful")}
                    </p>
                  </motion.div>

                  <motion.div {...contentAnimation}>
                    <OrderDetails receiptData={data} />
                  </motion.div>

                  <motion.div {...contentAnimation}>
                    <WhatsNextSection />
                  </motion.div>

                  <motion.div
                    {...contentAnimation}
                    className="flex flex-col sm:flex-row gap-3"
                  >
                    <button
                      title={t("billings.startCoding")}
                      onClick={onClose}
                      className="w-full px-5 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium transition-all flex items-center justify-center gap-2 hover:cursor-pointer"
                    >
                      <span>{t("billings.startCoding")}</span>
                    </button>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
}
