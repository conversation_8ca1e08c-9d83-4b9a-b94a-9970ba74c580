import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  FaRocket,
  FaCrown,
  FaGem,
  FaRobot,
  FaBolt,
  FaCheck,
  FaArrowRight,
  FaCreditCard,
  FaServer,
  FaCode,
} from "react-icons/fa";

function Pricing() {
  const [showDetails, setShowDetails] = useState(false);

  const plans = [
    {
      id: "free",
      icon: <FaRocket />,
      title: "Free Trial",
      price: "0",
      description: "Start with 5 AI-generated pages—no credit card needed!",
      color: "bg-gradient-to-br from-[#1A1D2E]/50 to-[#161926]/50",
      iconColor: "text-blue-400",
      features: [
        "5 AI-generated pages",
        "Basic customization",
        "Preview mode",
        "Community support",
        "24-hour hosting preview",
      ],
      buttonText: "Start Free Trial",
      buttonDisabled: false,
    },
    {
      id: "pay-as-you-go",
      icon: <FaCreditCard />,
      title: "Pay-as-you-go",
      price: "Only pay for what you use",
      description: "Perfect for small projects and personal websites",
      color: "bg-gradient-to-br from-[#1A1D2E]/80 to-[#161926]/80",
      iconColor: "text-accent",
      features: [
        "Unlimited AI-generated pages",
        "Full customization options",
        "Domain connection",
        "SEO optimization",
        "Email support",
        "30-day hosting included",
      ],
      buttonText: "Choose This Plan",
      buttonDisabled: false,
      popular: true,
    },
    {
      id: "credit-packs",
      icon: <FaGem />,
      title: "Credit Packs",
      price: "Discounted rates",
      description: "For agencies & high-volume users",
      color: "bg-gradient-to-br from-[#1A1D2E]/50 to-[#161926]/50",
      iconColor: "text-purple-400",
      features: [
        "Bulk discounts",
        "Team collaboration",
        "White-label options",
        "Priority support",
        "Advanced analytics",
        "1-year hosting included",
      ],
      buttonText: "Get Credit Packs",
      buttonDisabled: false,
    },
    {
      id: "enterprise",
      icon: <FaServer />,
      title: "Enterprise API",
      price: "Custom pricing",
      description: "For businesses that need scale & automation",
      color: "bg-gradient-to-br from-[#1A1D2E]/50 to-[#161926]/50",
      iconColor: "text-amber-400",
      features: [
        "API access",
        "Custom integrations",
        "Dedicated account manager",
        "SLA guarantees",
        "Custom branding",
        "Enterprise-grade security",
      ],
      buttonText: "Contact Sales",
      buttonDisabled: false,
    },
  ];

  return (
    <section id="pricing" className="section-wrapper py-16">
      <div className="section-content">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center gap-2 glass-card px-6 py-2 mb-4">
            <FaBolt className="text-accent" />
            <span className="text-gray-400 font-light">Pricing Plans</span>
          </div>
          <h2 className="text-4xl font-light mb-3">
            Simple, Transparent Pricing – No Subscriptions Required!
          </h2>
          <p className="text-gray-400 text-xl font-light max-w-2xl mx-auto">
            Choose the plan that works best for you, with no hidden fees or
            long-term commitments
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="relative h-full"
            >
              <motion.div
                whileHover={{
                  scale: 1.02,
                  transition: { duration: 0.2 },
                }}
                className={`h-full rounded-2xl p-6 relative overflow-hidden ${plan.color} 
                  ${plan.popular ? "border-2 border-accent" : "border border-white/5"}
                  transition-all duration-300 group hover:bg-gradient-to-br hover:from-[#1A1D2E]/80 hover:to-[#161926]/80
                  flex flex-col`}
              >
                {plan.popular && (
                  <div className="absolute top-4 right-4">
                    <span className="bg-accent text-white text-xs px-3 py-1 rounded-full">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="flex items-center gap-4 mb-4">
                  <motion.div
                    className={`text-2xl ${plan.iconColor} transition-transform duration-300 group-hover:scale-110`}
                    animate={{ rotate: [0, 15, -15, 0] }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse",
                      ease: "easeInOut",
                    }}
                  >
                    {plan.icon}
                  </motion.div>
                  <h3 className="text-xl font-light">{plan.title}</h3>
                </div>

                <div className="mb-4">
                  <div className="text-2xl font-light">{plan.price}</div>
                  <p className="text-gray-400 text-sm">{plan.description}</p>
                </div>

                <div className="space-y-3 mb-6 flex-grow">
                  {plan.features.map((feature, idx) => (
                    <motion.div
                      key={idx}
                      className="flex items-center gap-3 group-hover:translate-x-1 transition-transform duration-300"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: idx * 0.1 }}
                    >
                      <FaCheck className="text-accent text-sm" />
                      <span className="font-light text-sm">{feature}</span>
                    </motion.div>
                  ))}
                </div>

                <motion.button
                  title={plan.buttonText}
                  disabled={plan.buttonDisabled}
                  whileHover={!plan.buttonDisabled ? { scale: 1.02 } : {}}
                  whileTap={!plan.buttonDisabled ? { scale: 0.98 } : {}}
                  className={`w-full py-3 rounded-lg font-light transition-all mt-auto ${
                    plan.buttonDisabled
                      ? "bg-gray-700/50 text-gray-400 cursor-not-allowed"
                      : "bg-accent hover:bg-accent/90 text-white"
                  }`}
                >
                  {plan.buttonText}
                </motion.button>
              </motion.div>
            </motion.div>
          ))}
        </div>

        {/* Compare Plans Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
          className="text-center mt-10"
        >
          <motion.button
            title={showDetails ? "Hide Details" : "Compare Plans"}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setShowDetails(!showDetails)}
            className="bg-navy-700/50 hover:bg-navy-700/70 text-white px-6 py-3 rounded-lg transition-all font-light inline-flex items-center gap-2"
          >
            <span>{showDetails ? "Hide Details" : "Compare Plans"}</span>
            <FaArrowRight
              className={`transition-transform duration-300 ${showDetails ? "rotate-90" : ""}`}
            />
          </motion.button>
        </motion.div>

        {/* Detailed Comparison (Expandable) */}
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-12 overflow-hidden"
          >
            <div className="glass-card p-6">
              <h3 className="text-xl font-light mb-6 text-center">
                Detailed Plan Comparison
              </h3>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="py-3 px-4 text-left">Feature</th>
                      {plans.map((plan) => (
                        <th key={plan.id} className="py-3 px-4 text-center">
                          {plan.title}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-800">
                      <td className="py-3 px-4 text-left">
                        AI-Generated Pages
                      </td>
                      <td className="py-3 px-4 text-center">5 pages</td>
                      <td className="py-3 px-4 text-center">Unlimited</td>
                      <td className="py-3 px-4 text-center">Unlimited</td>
                      <td className="py-3 px-4 text-center">Unlimited</td>
                    </tr>
                    <tr className="border-b border-gray-800">
                      <td className="py-3 px-4 text-left">Customization</td>
                      <td className="py-3 px-4 text-center">Basic</td>
                      <td className="py-3 px-4 text-center">Full</td>
                      <td className="py-3 px-4 text-center">Full</td>
                      <td className="py-3 px-4 text-center">Full + Custom</td>
                    </tr>
                    <tr className="border-b border-gray-800">
                      <td className="py-3 px-4 text-left">Hosting</td>
                      <td className="py-3 px-4 text-center">24-hour preview</td>
                      <td className="py-3 px-4 text-center">30 days</td>
                      <td className="py-3 px-4 text-center">1 year</td>
                      <td className="py-3 px-4 text-center">Custom</td>
                    </tr>
                    <tr className="border-b border-gray-800">
                      <td className="py-3 px-4 text-left">Support</td>
                      <td className="py-3 px-4 text-center">Community</td>
                      <td className="py-3 px-4 text-center">Email</td>
                      <td className="py-3 px-4 text-center">Priority</td>
                      <td className="py-3 px-4 text-center">Dedicated</td>
                    </tr>
                    <tr className="border-b border-gray-800">
                      <td className="py-3 px-4 text-left">
                        Team Collaboration
                      </td>
                      <td className="py-3 px-4 text-center">
                        <FaCheck className="text-red-500 mx-auto" />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <FaCheck className="text-red-500 mx-auto" />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <FaCheck className="text-accent mx-auto" />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <FaCheck className="text-accent mx-auto" />
                      </td>
                    </tr>
                    <tr className="border-b border-gray-800">
                      <td className="py-3 px-4 text-left">API Access</td>
                      <td className="py-3 px-4 text-center">
                        <FaCheck className="text-red-500 mx-auto" />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <FaCheck className="text-red-500 mx-auto" />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <FaCheck className="text-red-500 mx-auto" />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <FaCheck className="text-accent mx-auto" />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </motion.div>
        )}

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6 }}
          className="mt-16 text-center"
        >
          <h3 className="text-2xl font-light mb-6">
            Frequently Asked Questions
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <div className="glass-card p-6 text-left">
              <h4 className="text-lg font-light mb-2 text-accent">
                Can I upgrade my plan later?
              </h4>
              <p className="text-gray-400 font-light">
                Yes, you can upgrade to any plan at any time. Your unused
                credits will be applied to your new plan.
              </p>
            </div>

            <div className="glass-card p-6 text-left">
              <h4 className="text-lg font-light mb-2 text-accent">
                Do I need a credit card for the free trial?
              </h4>
              <p className="text-gray-400 font-light">
                No, you can start your free trial without providing any payment
                information.
              </p>
            </div>

            <div className="glass-card p-6 text-left">
              <h4 className="text-lg font-light mb-2 text-accent">
                What happens when my hosting period ends?
              </h4>
              <p className="text-gray-400 font-light">
                You can extend your hosting by purchasing additional hosting
                credits or upgrading your plan.
              </p>
            </div>

            <div className="glass-card p-6 text-left">
              <h4 className="text-lg font-light mb-2 text-accent">
                Can I use my own domain name?
              </h4>
              <p className="text-gray-400 font-light">
                Yes, all paid plans allow you to connect your own custom domain
                name to your website.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

export default Pricing;
