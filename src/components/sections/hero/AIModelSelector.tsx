import React, { useState, useEffect, useRef } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import {
  AI_MODELS,
  AI_MODEL_STORAGE_KEY,
  BIG_CONTEXT_WINDOW,
} from "../../../utils/constants";
import styles from "../../../AIModelSelector.module.scss";

interface Plan {
  id: string;
  monthly: {
    name: string;
    price: number;
    features: string[];
    [key: string]: any;
  } | null;
  yearly: {
    name: string;
    price: number;
    features: string[];
    [key: string]: any;
  } | null;
  [key: string]: any;
}

interface UpgradePackage {
  name: string;
  price: number | null;
  billingCycle: string;
}

const safeGetItem = (key: string, defaultValue: string): string => {
  try {
    const value = localStorage.getItem(key);
    return value !== null ? value : defaultValue;
  } catch (error) {
    console.warn(`Error reading from localStorage: ${error}`);
    return defaultValue;
  }
};

const safeSetItem = (key: string, value: string): void => {
  try {
    localStorage.setItem(key, value);
  } catch (error) {
    console.warn(`Error writing to localStorage: ${error}`);
  }
};

interface AIModelSelectorProps {
  isCompact?: boolean;
}

const AIModelSelector: React.FC<AIModelSelectorProps> = ({
  isCompact = false,
}) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedModelId, setSelectedModelId] = useState(AI_MODELS[0].value);
  const [hasLargeContextFeature, setHasLargeContextFeature] = useState(false);
  const [upgradePackage, setUpgradePackage] = useState<UpgradePackage | null>(
    null,
  );
  const dropdownRef = useRef<HTMLDivElement>(null);
  const selectedOptionRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [buttonWidth, setButtonWidth] = useState<number | undefined>(undefined);

  const fetchCurrentUserPlan = async (): Promise<Plan | null> => {
    const response = await fetch(
      `${import.meta.env.VITE_API_URL}/plans/get-current-user-plan`,
      {
        method: "GET",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
      },
    );

    if (!response.ok) {
      throw new Error("Failed to fetch current user plan");
    }

    return response.json();
  };

  async function fetchPlans() {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/plans`, {
        method: "GET",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch plans. Status: ${response.status}`);
      }

      const data = await response.json();

      return data;
    } catch (error) {
      console.error("Error fetching plans:", error);
      throw error;
    }
  }

  const findUpgradePackage = async () => {
    try {
      const plans: Plan[] = await fetchPlans();

      const eligiblePlans = plans.filter((plan) => {
        const monthlyFeatures = plan.monthly?.features || [];
        const yearlyFeatures = plan.yearly?.features || [];

        return (
          monthlyFeatures.includes(BIG_CONTEXT_WINDOW) ||
          yearlyFeatures.includes(BIG_CONTEXT_WINDOW)
        );
      });

      if (eligiblePlans.length === 0) {
        setUpgradePackage({ name: "", price: null, billingCycle: "" });
        return;
      }

      const sortedPlans = [...eligiblePlans].sort((a, b) => {
        if (
          a.monthly?.features.includes(BIG_CONTEXT_WINDOW) &&
          b.monthly?.features.includes(BIG_CONTEXT_WINDOW)
        ) {
          return a.monthly.price - b.monthly.price;
        }

        if (a.monthly?.features.includes(BIG_CONTEXT_WINDOW)) {
          return -1;
        }

        if (b.monthly?.features.includes(BIG_CONTEXT_WINDOW)) {
          return 1;
        }

        return (a.yearly?.price || Infinity) - (b.yearly?.price || Infinity);
      });

      const cheapestPlan = sortedPlans[0];

      if (cheapestPlan.monthly?.features.includes(BIG_CONTEXT_WINDOW)) {
        setUpgradePackage({
          name: cheapestPlan.monthly.name,
          price: cheapestPlan.monthly.price,
          billingCycle: "monthly",
        });
      } else if (cheapestPlan.yearly?.features.includes(BIG_CONTEXT_WINDOW)) {
        setUpgradePackage({
          name: cheapestPlan.yearly.name,
          price: cheapestPlan.yearly.price,
          billingCycle: "yearly",
        });
      } else {
        setUpgradePackage({ name: "", price: null, billingCycle: "" });
      }
    } catch (error) {
      setUpgradePackage({ name: "", price: null, billingCycle: "" });
    }
  };

  useEffect(() => {
    const loadInitialData = async () => {
      await Promise.allSettled([getUserPlan(), findUpgradePackage()]);
    };

    loadInitialData();
  }, []);

  const getUserPlan = async () => {
    try {
      let userPlan;

      try {
        userPlan = await fetchCurrentUserPlan();
      } catch (error) {
        setHasLargeContextFeature(false);

        return;
      }

      if (!userPlan) {
        setHasLargeContextFeature(false);

        return;
      }

      const hasFeature = userPlan.features?.includes(BIG_CONTEXT_WINDOW);

      setHasLargeContextFeature(hasFeature || false);
    } catch (error) {
      setHasLargeContextFeature(false);
    }
  };

  useEffect(() => {
    const handleModelChangeEvent = (
      event: CustomEvent<{ modelValue: string; source?: string }>,
    ) => {
      if (
        event.detail &&
        event.detail.modelValue &&
        event.detail.source !== "AIModelSelector"
      ) {
        selectModel(event.detail.modelValue);
      }
    };

    window.addEventListener(
      "biela:changeAIModel",
      handleModelChangeEvent as EventListener,
    );

    return () => {
      window.removeEventListener(
        "biela:changeAIModel",
        handleModelChangeEvent as EventListener,
      );
    };
  }, []);

  useEffect(() => {
    const savedModelId = safeGetItem(AI_MODEL_STORAGE_KEY, AI_MODELS[0].value);
    setSelectedModelId(savedModelId);
  }, []);

  useEffect(() => {
    safeSetItem(AI_MODEL_STORAGE_KEY, selectedModelId);
  }, [selectedModelId]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isOpen && selectedOptionRef.current && scrollContainerRef.current) {
      requestAnimationFrame(() => {
        if (selectedOptionRef.current && scrollContainerRef.current) {
          scrollContainerRef.current.scrollTop =
            selectedOptionRef.current.offsetTop -
            scrollContainerRef.current.offsetTop -
            16;
        }
      });
    }
  }, [isOpen]);

  useEffect(() => {
    if (!isCompact && buttonRef.current) {
      setButtonWidth(buttonRef.current.offsetWidth);
    }
  }, [selectedModelId, isCompact]);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const selectModel = (modelValue: string) => {
    setSelectedModelId(modelValue);
    setIsOpen(false);

    const event = new CustomEvent("biela:changeAIModel", {
      detail: { modelValue, source: "AIModelSelector" },
    });
    window.dispatchEvent(event);
  };

  const getUpgradeText = () => {
    if (!upgradePackage || !upgradePackage.name) {
      return t("UpgradePlan", "a premium plan");
    }

    return upgradePackage.name;
  };

  const currentModel =
    AI_MODELS.find((model) => model.value === selectedModelId) || AI_MODELS[0];
  const animationProps = isCompact
    ? {
        initial: { opacity: 0, y: -5 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -5 },
      }
    : {
        initial: { opacity: 0, y: 5 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: 5 },
      };

  return (
    <div className={isCompact ? "" : styles.aiSelector_full} ref={dropdownRef}>
      {!isCompact && (
        <div className={styles.aiSelector_label}>
          {t("AIModel", "AI Model")}:
        </div>
      )}

      <div className={styles.aiSelector_buttonContainer}>
        <button
          title={"Select Ai Model"}
          ref={buttonRef}
          onClick={toggleDropdown}
          className={
            isCompact
              ? "action-button p-1.5 rounded-md transition-all duration-200 relative group bg-transparent"
              : styles.aiSelector_buttonFull
          }
          style={
            !isCompact && buttonWidth
              ? { width: `${buttonWidth}px` }
              : undefined
          }
        >
          {isCompact ? (
            <>
              <div className="relative w-4 h-4 flex items-center justify-center">
                <span
                  style={{
                    color: currentModel.color,
                    fontSize: "1.1rem",
                  }}
                  className="text-white/50 group-hover:text-white/70 transition-colors"
                >
                  {currentModel.icon}
                </span>
              </div>
              <span className="tooltip">{t("AIModel", "AI Model")}</span>
            </>
          ) : (
            <>
              <div
                className={styles.aiSelector_flexRow}
                style={{ flexGrow: 1 }}
              >
                <div
                  className={
                    styles.aiSelector_iconWrapper +
                    " " +
                    styles.aiSelector_iconFull
                  }
                >
                  <span
                    className={styles.aiSelector_icon}
                    style={{ color: currentModel.color }}
                  >
                    {currentModel.icon}
                  </span>
                </div>

                <span className={styles.aiSelector_name}>
                  {currentModel.name}
                </span>
              </div>

              <motion.span
                animate={{ rotate: isOpen ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className={styles.aiSelector_arrow}
              >
                ▼
              </motion.span>
            </>
          )}
        </button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              {...animationProps}
              transition={{ duration: 0.15 }}
              className={`${styles.aiSelector_dropdown} ${isCompact ? styles.aiSelector_dropdownAbove : styles.aiSelector_dropdownBelow}`}
            >
              <div
                ref={scrollContainerRef}
                className={styles.aiSelector_scrollArea}
              >
                {AI_MODELS.map((model) => (
                  <div
                    key={model.id}
                    ref={
                      selectedModelId === model.value ? selectedOptionRef : null
                    }
                    onClick={() => {
                      if (
                        !(model.isContextAlternative && !hasLargeContextFeature)
                      ) {
                        selectModel(model.value);
                      }
                    }}
                    className={`${styles.aiSelector_optionCard} ${
                      selectedModelId === model.value
                        ? styles.aiSelector_optionSelected
                        : styles.aiSelector_optionDefault
                    } ${model.isContextAlternative && !hasLargeContextFeature ? styles.aiSelector_optionDisabled : ""}`}
                    style={{
                      borderLeftColor: model.color,
                      ...(model.isContextAlternative &&
                        !hasLargeContextFeature &&
                        ({
                          "--locked-badge-color": model.color,
                        } as React.CSSProperties)),
                    }}
                  >
                    {model.isContextAlternative && !hasLargeContextFeature && (
                      <div className={styles.aiSelector_premiumCorner}>
                        <div className={styles.aiSelector_premiumRibbon}>
                          <span className={styles.aiSelector_premiumLabel}>
                            <svg
                              width="14"
                              height="14"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              className={styles.aiSelector_premiumIcon}
                            >
                              <path
                                d="M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                            {t("PremiumBadge", "PREMIUM").toUpperCase()}
                          </span>
                        </div>
                      </div>
                    )}

                    <div className={styles.aiSelector_optionHeader}>
                      <div
                        className={styles.aiSelector_modelIcon}
                        style={{ backgroundColor: `${model.color}20` }}
                      >
                        <span
                          className={styles.aiSelector_modelIconInner}
                          style={{ color: model.color }}
                        >
                          {model.icon}
                        </span>
                      </div>

                      <div className={styles.aiSelector_modelInfo}>
                        <div className={styles.aiSelector_modelHeader}>
                          <h4 className={styles.aiSelector_modelName}>
                            {model.name}
                            {model.isContextAlternative &&
                              !hasLargeContextFeature && (
                                <span
                                  className={styles.aiSelector_lockIcon}
                                  style={{ color: model.color }}
                                >
                                  <svg
                                    width="14"
                                    height="14"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                    <path
                                      d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                  </svg>
                                </span>
                              )}
                          </h4>
                          {selectedModelId === model.value && (
                            <span
                              className={styles.aiSelector_activeTag}
                              style={{
                                backgroundColor: `${model.color}30`,
                                color: model.color,
                              }}
                            >
                              {t("Active", "Active")}
                            </span>
                          )}
                        </div>
                        <div>
                          <p className={styles.aiSelector_modelDesc}>
                            {t(
                              model.description.key,
                              model.description.defaultValue,
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AIModelSelector;
