import React, { memo } from "react";
import { cubicBezier, motion } from "framer-motion";
import { useTranslation } from "react-i18next";

interface ChatCodeSliderOption<T> {
  value: T;
  text: string;
}

export interface ChatCodeSliderOptions<T> {
  left: ChatCodeSliderOption<T>;
  right: ChatCodeSliderOption<T>;
}

export const ChatCodeSlider = ({
  selected,
  options,
  setChatCodeSelected,
  disableLayoutAnimation = false,
  modelColor,
  changeTabToCode, // added
  changeTabToChat, // added
}) => {
  const isLeftSelected = selected === options.left.value;
  const { t } = useTranslation();
  return (
    <div className="flex">
      <div
        style={{
          backgroundColor: "rgba(255, 255, 255, 0.03)",
          borderRadius: "0.75rem",
          padding: "4px",
        }}
        className="flex items-center flex-1 gap-1 show-prompt rounded-md overflow-hidden border-0 max-h-[40px]"
      >
        <ChatCodeSliderButton
          variant="code"
          selected={selected === "code"}
          setChatCodeSelected={() => {
            changeTabToCode?.();
            setChatCodeSelected?.(options.right.value);
          }}
          disableLayoutAnimation={disableLayoutAnimation}
          modelColor={modelColor}
        >
          <div className="flex items-center gap-2">
            <svg
              className="w-4 h-4"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
              />
            </svg>
            <span className="text-[15px] font-normal tracking-[0.4px]">
              {t(`translation.${options.right.value}`, options.right.text)}
            </span>
          </div>
        </ChatCodeSliderButton>

        <ChatCodeSliderButton
          variant="chat" // chat mode button
          selected={selected === "chat"}
          setChatCodeSelected={() => {
            changeTabToChat?.();
            setChatCodeSelected?.(options.left.value);
          }}
          disableLayoutAnimation={disableLayoutAnimation}
        >
          <div className="flex items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              aria-hidden="true"
              data-slot="icon"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
              ></path>
            </svg>
            <span className="text-[15px] font-normal tracking-[0.4px]">
              {t(`translation.${options.left.value}`, options.left.text)}
            </span>
          </div>
        </ChatCodeSliderButton>
      </div>
    </div>
  );
};

interface ChatCodeSliderButtonProps {
  selected: boolean;
  variant: "chat" | "code";
  children: JSX.Element;
  setChatCodeSelected: () => void;
  disableLayoutAnimation?: boolean;
  modelColor?: string;
}

const ChatCodeSliderButton = memo(
  ({
    selected,
    variant,
    children,
    setChatCodeSelected,
    disableLayoutAnimation = false,
    modelColor,
  }: ChatCodeSliderButtonProps) => {
    const baseClasses =
      "text-sm px-3 py-1.5 rounded-md relative hover:bg-white/5 transition-all flex-1 ease-in-out duration-300";

    const selectedClasses =
      variant === "chat"
        ? "text-[#000] bg-[#800080]"
        : "text-[#010101] bg-[#4ADE80]";
    const notSelectedClasses =
      "text-white/70 text-biela-elements-item-contentActive bg-transparent";

    return (
      <button
        title={"Button Mode " + variant}
        onClick={setChatCodeSelected}
        className={`${baseClasses} ${selected ? selectedClasses : notSelectedClasses} cursor-pointer`}
      >
        <span className="relative z-10 flex gap-2 justify-center">
          {children}
        </span>
        {selected && (
          <motion.span
            {...(!disableLayoutAnimation && { layoutId: "pill-tab-chat-code" })}
            transition={{ duration: 0.2, ease: cubicBezier(0.4, 0, 0.2, 1) }}
            className="absolute inset-0 z-0 rounded-md max-h-[30px]"
            style={{
              backgroundColor:
                variant === "chat" ? "#942894" : modelColor || "#4ADE80",
            }}
          ></motion.span>
        )}
      </button>
    );
  },
);
