import React from "react";
import { motion } from "framer-motion";
import { FaPlay, FaRocket } from "react-icons/fa";
import { useTranslation } from "react-i18next";

function HeroButtons({ scrollToDemo }) {
  const { t } = useTranslation();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
      className="flex flex-wrap flex-row items-center justify-center gap-4 mb-12"
    >
      <a href={import.meta.env.VITE_IDE_URL + "/register/"}>
        <motion.button
          title={t("hero.tryFree")}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="px-6 py-4 bg-accent hover:bg-accent/90 text-white rounded-lg transition-all font-light text-base sm:text-sm md:text-xl flex items-center gap-2 cursor-pointer"
        >
          <FaRocket className="text-lg sm:text-xl" />
          {t("hero.tryFree")}
        </motion.button>
      </a>

      <motion.button
        title={t("hero.seeAiAction")}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={scrollToDemo}
        className="px-6 py-4 bg-navy-700/50 hover:bg-navy-700/70 text-white rounded-lg transition-all font-light text-base sm:text-sm md:text-xl flex items-center gap-2 cursor-pointer"
      >
        <FaPlay className="text-accent text-lg sm:text-xl" />
        {t("hero.seeAiAction")}
      </motion.button>
    </motion.div>
  );
}

export default HeroButtons;
