import React from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { FaTrophy } from "react-icons/fa";

function HeroHeading() {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col items-center gap-3">
      <motion.a
        href="/hackathon"
        className="text-sm border border-[#282828] rounded-full px-4 py-2 hover:bg-[#4ade80] hover:text-black transition-colors mb-[100px] max-md:my-6 flex gap-2 items-center justify-center"
      >
        <FaTrophy size={16} />
        <span className="text-sm">
          {t("joinVibeCoding", "Join Vibe Coding Hackathon")}
        </span>
      </motion.a>
      <h1
        className="text-md md:text-[clamp(1.5rem,3.5vw,1.875rem)] font-light mx-auto text-center font-rexton"
        style={{
          letterSpacing: "-4.5px",
        }}
      >
        {t("whatDoYouWantToBuild", "WHAT DO YOU WANT TO BUILD?")}
      </h1>

      <motion.p
        className="text-sm md:text-xl manrope-light font-light mb-[48px]"
        style={{ color: "rgb(212, 212, 212)" }}
      >
        {t("imaginePromptCreate", " Imagine. Prompt. Create.")}
      </motion.p>
    </div>
  );
}

export default HeroHeading;
