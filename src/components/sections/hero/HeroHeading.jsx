import React from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { FaTrophy } from "react-icons/fa";
import { FaDiscord } from "react-icons/fa";

function HeroHeading() {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col items-center gap-3">
      {/* Discord Vote Message */}
        <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="text-sm border border-[#282828] rounded-full px-4 py-2 hover:border-[#5865F2] transition-all mb-6 max-md:mb-4 flex gap-2 items-center justify-center bg-black/20 backdrop-blur-sm"
      >
        <FaDiscord className="w-4 h-4 text-[#5865F2]" />
        <span className="text-sm font-light">
          Join our {" "}
          <a
            href="https://discord.gg/A9VryKC8TQ"
            target="_blank"
            rel="noopener noreferrer"
            className="text-[#5865F2] hover:text-[#4752C4] transition-colors font-medium underline decoration-1 underline-offset-2"
          >
            Discord Channel
          </a>
          . Get{" "}
          <span className="text-green-400 font-medium">5 Millions Free Tokens</span>
          .
        </span>
      </motion.div>

      <motion.a
        href="https://www.producthunt.com/products/biela-dev?embed=true&utm_source=badge-featured&utm_medium=badge&utm_source=badge-biela&#0045;dev"
        target="_blank"
        className={"mb-[100px] max-md:my-6"}
      >
        <img
          src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=953262&theme=light&t=1751353227565"
          alt="Biela&#0046;dev - If&#0032;you&#0032;can&#0032;imagine&#0032;it&#0032;you&#0032;can&#0032;code&#0032;it | Product Hunt"
          style={{ width: "250px", height: " 54px" }}
          width="250"
          height="54"
        />
      </motion.a>
      {/*<motion.a*/}
      {/*  href="/hackathon"*/}
      {/*  className="text-sm border border-[#282828] rounded-full px-4 py-2 hover:bg-[#4ade80] hover:text-black transition-colors mb-[100px] max-md:my-6 flex gap-2 items-center justify-center"*/}
      {/*>*/}
      {/*  <FaTrophy size={16} />*/}
      {/*  <span className="text-sm">*/}
      {/*    {t("joinVibeCoding", "Join Vibe Coding Hackathon")}*/}
      {/*  </span>*/}
      {/*</motion.a>*/}
      <h1
        className="text-md md:text-[clamp(1.5rem,3.5vw,1.875rem)] font-light mx-auto text-center font-rexton"
        style={{
          letterSpacing: "-4.5px",
        }}
      >
        {t("whatDoYouWantToBuild", "WHAT DO YOU WANT TO BUILD?")}
      </h1>

      <motion.p
        className="text-sm md:text-xl manrope-light font-light mb-[48px]"
        style={{ color: "rgb(212, 212, 212)" }}
      >
        {t("imaginePromptCreate", " Imagine. Prompt. Create.")}
      </motion.p>
    </div>
  );
}

export default HeroHeading;
