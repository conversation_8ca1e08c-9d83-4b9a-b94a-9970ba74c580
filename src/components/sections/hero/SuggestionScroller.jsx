import React, { useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import {
  FaCloud,
  FaStore,
  FaComments,
  FaPalette,
  FaTasks,
  FaDumbbell,
  FaUtensils,
  FaPlane,
  FaBook,
  FaMusic,
  FaHome,
  FaBriefcase,
} from "react-icons/fa";

function SuggestionScroller({ setPrompt }) {
  const { t } = useTranslation();

  const scrollContainerRef = useRef(null);

  // Extended list of suggestions with more diversity
  const suggestions = [
    {
      text: t("hero.suggestions.weatherDashboard"),
      icon: <FaCloud />,
      borderColor: "border-sky-400",
    },
    {
      text: t("hero.suggestions.ecommercePlatform"),
      icon: <FaStore />,
      borderColor: "border-green-400",
    },
    {
      text: t("hero.suggestions.socialMediaApp"),
      icon: <FaComments />,
      borderColor: "border-purple-400",
    },
    {
      text: t("hero.suggestions.portfolioWebsite"),
      icon: <FaPalette />,
      borderColor: "border-pink-400",
    },
    {
      text: t("hero.suggestions.taskManagementApp"),
      icon: <FaTasks />,
      borderColor: "border-yellow-400",
    },
    {
      text: t("hero.suggestions.fitnessTracker"),
      icon: <FaDumbbell />,
      borderColor: "border-red-400",
    },
    {
      text: t("hero.suggestions.recipeSharingPlatform"),
      icon: <FaUtensils />,
      borderColor: "border-orange-400",
    },
    {
      text: t("hero.suggestions.travelBookingSite"),
      icon: <FaPlane />,
      borderColor: "border-teal-400",
    },
    {
      text: t("hero.suggestions.learningPlatform"),
      icon: <FaBook />,
      borderColor: "border-indigo-400",
    },
    {
      text: t("hero.suggestions.musicStreamingApp"),
      icon: <FaMusic />,
      borderColor: "border-pink-400",
    },
    {
      text: t("hero.suggestions.realEstateListing"),
      icon: <FaHome />,
      borderColor: "border-orange-400",
    },
    {
      text: t("hero.suggestions.jobBoard"),
      icon: <FaBriefcase />,
      borderColor: "border-yellow-400",
    },
  ];

  // Duplicate suggestions for continuous scrolling effect
  const extendedSuggestions = [...suggestions, ...suggestions];

  // Continuous scrolling effect
  useEffect(() => {
    if (!scrollContainerRef.current) return;

    let scrollAmount = 0;
    const distance = 0.5; // Reduced from 1 to 0.5 for slower scrolling
    const scrollWidth = scrollContainerRef.current.scrollWidth;

    const scroll = () => {
      if (!scrollContainerRef.current) return;

      scrollAmount += distance;

      // Reset scroll position when we've scrolled through half the items
      if (scrollAmount >= scrollWidth / 2) {
        scrollAmount = 0;
      }

      scrollContainerRef.current.scrollLeft = scrollAmount;
      animationRef.current = requestAnimationFrame(scroll);
    };

    const animationRef = { current: requestAnimationFrame(scroll) };

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // Pause scrolling on hover
  const handleMouseEnter = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.style.animationPlayState = "paused";
    }
  };

  const handleMouseLeave = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.style.animationPlayState = "running";
    }
  };

  return (
    <div className="mt-12 z-10">
      <div
        className="flex items-center gap-4 overflow-x-auto w-[100%] hide-scrollbar blur-margins"
        style={{
          width: "100%",
          position: "relative",
        }}
      >
        <div
          ref={scrollContainerRef}
          className="flex animate-scroll gap-3"
          style={{
            display: "flex",
            gap: "1rem",
            animation: "scroll 120s linear infinite",
            width: "max-content",
          }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {extendedSuggestions.map((suggestion, index) => (
            <motion.button
              title={suggestion.text}
              key={`${suggestion.text}-${index}`}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg border border-[#282828] transition-all duration-1000 whitespace-nowrap cursor-pointer`}
              onClick={() => setPrompt(suggestion.text)}
            >
              {/*<span className="text-xl">{suggestion.icon}</span>*/}
              <span className="font-light">{suggestion.text}</span>
            </motion.button>
          ))}
        </div>
      </div>
    </div>
  );
}

export default SuggestionScroller;
