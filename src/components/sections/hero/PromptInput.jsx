import React, { useState, useEffect, useRef } from "react";
import { motion, cubicBezier } from "framer-motion";

import { useTranslation } from "react-i18next";

import ChatIcon from "../../../assets/icons/chat.svg?react";
import CodeIcon from "../../../assets/icons/code.svg?react";
import LinkIcon from "../../../assets/icons/link.svg?react";
import MicrophoneIcon from "../../../assets/icons/microphone.svg?react";
import SyncIcon from "../../../assets/icons/sync.svg?react";
import EnhanceIcon from "../../../assets/icons/enhance.svg?react";
import SpaceLogo from "./SpaceLogo.jsx";
import { ShortEvents } from "../../short-events.js";
import AIModelSelector from "./AIModelSelector.js";
import { ChatCodeSwitch } from "./ChatCodeSwitch.js";

const customEasingFn = cubicBezier(0.4, 0, 0.2, 1);
const modeOptions = {
  left: {
    value: "chat",
    text: "Chat",
  },
  right: {
    value: "code",
    text: "Code",
  },
};
function PromptInput({ prompt, setPrompt }) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("code");
  const textareaRef = useRef(null);
  const tooltipRefs = useRef([]);
  const { isUser } = ShortEvents();
  const [isEnabled, setIsEnabled] = useState(false);
  function getFirstUserMessageOnHomePage() {
    if (typeof window === "undefined") return null;

    if (window.location.pathname !== "/") return null;

    const match = document.cookie.match(/(?:^|; )first-user-message=([^;]*)/);

    return match ? decodeURIComponent(match[1]) : null;
  }

  useEffect(() => {
    const message = getFirstUserMessageOnHomePage();
    if (message) {
      setPrompt(message);
    }
  }, []);

  // Footer icons with tooltips
  const footerIcons = [
    {
      icon: <LinkIcon className={"opacity-[1] w-4 h-4 text-white/50"} />,
      tooltip: t("hero.attachFiles"),
    },
    {
      icon: <MicrophoneIcon className={"opacity-[1] w-4 h-4 text-white/50"} />,
      tooltip: t("hero.voiceInput"),
    },
    {
      icon: <EnhanceIcon className={"opacity-[1] w-4 h-4 text-white/50"} />,
      tooltip: t("hero.enhancePrompt"),
    },
    {
      icon: <SyncIcon className={"opacity-[1] w-4 h-4 text-white/50"} />,
      tooltip: t("hero.cleanupProject"),
    },
  ];

  // Initialize tooltip refs
  useEffect(() => {
    tooltipRefs.current = footerIcons.map(() => React.createRef());
  }, []);

  const handleSubmit = (e) => {
    if (!prompt) return;
    e.preventDefault();
    document.cookie = `project_prompt=${encodeURIComponent(prompt)}; path=/; domain=biela.dev; max-age=7200; Secure; SameSite=Lax`; // 1 an
    if (isUser) {
      // Setăm cookie-ul cu numele "project_prompt"
      window.location.href = import.meta.env.VITE_IDE_URL;
    } else {
      window.location.href = import.meta.env.VITE_IDE_URL + "/login/";
    }
  };

  const getTooltipMessage = () => {
    return isEnabled
      ? t(
          "extendedThinkingTooltipDisabled",
          "Disable extended thinking to save resources",
        )
      : t(
          "extendedThinkingTooltip",
          "Enable AI to think more deeply before responding",
        );
  };

  return (
    <div className="flex flex-col place-items-center align-items-center justify-center rounded-lg relative w-full max-w-[100vw] mx-auto z-prompt base-chat">
      <div
        className={
          "w-[100%] h-full rotate-full relative group z-2 flex flex-col"
        }
      >
        <div
          className={
            "p-1 py-4 md:p-8 flex-1 relative transition-all flex flex-col  md:block show-prompt"
          }
          style={{
            backgroundColor: "rgba(255, 255, 255, 0.03)",
            borderRadius: "0.75rem",
          }}
        >
          {/* Tab Navigation */}
          <div className="flex flex-col md:flex-row items-center gap-2 mb-[20px]">
            <ChatCodeSwitch
              activeTab={activeTab}
              changeTabToChat={(mode) => {
                setActiveTab(mode);
              }}
            />
            <AIModelSelector />
          </div>
          <div
            className={`h-full main-input-area border w-full border-white/5 flex flex-col gap-4 py-4 px-0.5 md:py-4 md:px-4 ${activeTab === "chat" && "chat-mode"}`}
          >
            {/* Main Input Area */}
            <div className="container-textarea border border-white/5 hover:border-white/10 transition-all hover:border-biela-elements-focus duration-200 text-white rounded-lg bg-transparent">
              <textarea
                ref={textareaRef}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="w-full !h-[80px] !max-h-[80px] p-4 !overflow-auto outline-none resize-none text-biela-elements-textPrimary placeholder-biela-elements-textTertiary bg-transparent text-sm chat-textarea-font transition-all duration-200 hover:border-biela-elements-focus"
                placeholder={t("hero.inputPlaceholder")}
              />

              {/*  /!* Animated placeholder text overlay - improved styling *!/*/}
              {/*  /!* {!prompt && (*/}
              {/*    <div className="absolute top-7 left-0 pointer-events-none width-90">*/}
              {/*      <span className="text-gray-500">{t("hero.inputPlaceholder")} </span>*/}
              {/*      <span className="text-white">{placeholderText}</span>*/}
              {/*      <span className="w-2 h-5 bg-accent ml-1 inline-block animate-blink"></span>*/}
              {/*    </div>*/}
              {/*  )} *!/*/}
            </div>
            {/* Bottom Toolbar */}
            <div className="flex justify-between items-center text-sm prompt-actions-button shadow-xs h-[52px]">
              {/* Left Section: Footer Icons */}
              <div className="max-w-fit md:w-full md:border-r md:border-[#FFFFFF19] flex items-center flex-wrap gap-1.5 md:gap-3 p-1 md:p-3 hover:bg-[#FFFFFF05] transition-all duration-300 ease-in-out settings-icons ">
                {footerIcons.map((item, index) => (
                  <div key={index} className="relative">
                    <button
                      title={item.tooltip}
                      ref={(el) => (tooltipRefs.current[index] = el)}
                      className="action-button p-1.5 rounded-md transition-all duration-200 relative group cursor-pointer bg-transparent"
                    >
                      {item.icon}
                      <span className="tooltip">{item.tooltip}</span>
                    </button>
                    {/* Icon Tooltip */}
                  </div>
                ))}
                <div className={"h-6 w-px bg-white/10 mx-1 hidden md:block"} />
                <div className={"relative"}>
                  <div
                    className={"flex items-center gap-2 cursor-pointer group"}
                  >
                    <button
                      title={"Extended thinking"}
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsEnabled(!isEnabled);
                      }}
                      className={
                        "action-button cursor-pointer flex items-center gap-2 p-1.5 rounded-md transition-all duration-200 relative group  bg-transparent"
                      }
                    >
                      <svg
                        className="w-4 h-4 text-white/50 group-hover:text-white/70 transition-colors"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth={2}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
                        />
                      </svg>
                      <span
                        className={
                          "text-white/70 text-sm whitespace-nowrap ml-1 mr-2"
                        }
                      >
                        Extended thinking
                      </span>
                      <span className="tooltip">{getTooltipMessage()}</span>
                    </button>

                    <button
                      title={
                        isEnabled
                          ? "Disable Extended thinking"
                          : "Enable Extended thinking"
                      }
                      className={`relative cursor-pointer inline-flex h-5 w-10 items-center rounded-full transition-colors focus:outline-none ${
                        isEnabled ? "bg-green-500" : "bg-gray-600"
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsEnabled(!isEnabled);
                      }}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          isEnabled ? "translate-x-5" : "translate-x-1"
                        }`}
                      />
                    </button>
                  </div>
                </div>
              </div>

              {/* Right Section: Powered by text and Generate button */}
              <div className="flex flex-col md:flex-row items-center gap-2 md:gap-4">
                <div onClick={handleSubmit}>
                  <motion.button
                    title={"Biela.Dev Homepage"}
                    className="flex justify-center items-center mr-[20px] max-w-[40px] min-w-[40px] max-h-[40px] border-none  bg-transparent  hover:brightness-94 transition-theme disabled:opacity-50 "
                    transition={{ ease: customEasingFn, duration: 0.17 }}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                  >
                    <SpaceLogo
                      hasContent={!!prompt}
                      running={!!prompt}
                      mode={activeTab}
                      variant="cosmic-pulse"
                    />
                  </motion.button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PromptInput;
