import React, { useState } from "react";
import { useTranslation, Trans } from "react-i18next";
import { motion, AnimatePresence } from "framer-motion";
import { FaVideo } from "react-icons/fa";

function FAQ() {
  const { t } = useTranslation();

  const faqItems = [
    {
      key: "whatAreTokensQuestion",
      answer: ["whatAreTokensAnswer1", "whatAreTokensAnswer2"],
      createdAt: "2025-06-01",
    },
    {
      key: "freePlanTokensQuestion",
      answer: ["freePlanTokensAnswer"],
      createdAt: "2025-06-01",
    },
    {
      key: "outOfTokensQuestion",
      answer: ["outOfTokensAnswer"],
      isTrans: true,
      createdAt: "2025-06-01",
    },
    {
      key: "changePlanQuestion",
      answer: ["changePlanAnswer"],
      isTrans: true,
      createdAt: "2025-06-01",
    },
    {
      key: "rolloverQuestion",
      answer: ["rolloverAnswer"],
      createdAt: "2025-06-01",
    },
  ];

  const [activeIndex, setActiveIndex] = useState(null);

  const toggleIndex = (index) => {
    setActiveIndex((prev) => (prev === index ? null : index));
  };

  return (
    <section id="faq" className="section-wrapper pt-[72px]">
      <div className="section-content">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center flex items-center justify-center flex-col space-y-4"
        >
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            style={{ letterSpacing: "-4.5px" }}
            className="text-md md:text-[30px] font-light mx-auto rexton-light"
          >
            {t("faqHeading")}
          </motion.h2>
          <motion.p
            className="text-sm md:text-xl manrope-light font-light mb-[48px]"
            style={{ color: "rgb(212, 212, 212)" }}
          >
            {t("faqSubheading")}
          </motion.p>
        </motion.div>

        {/* Questions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6 }}
          className="max-w-4xl mx-auto divide-y divide-gray-700"
        >
          {faqItems.map((item, index) => (
            <div
              key={item.key}
              className="text-left py-4 cursor-pointer"
              onClick={() => toggleIndex(index)}
            >
              <div className={"flex justify-between gap-4 items-center"}>
                <p className="text-sm md:text-lg font-light text-white rexton-light">
                  {t(item.key)}
                </p>
                {/*{item.createdAt &&*/}
                {/*  new Date().getTime() - new Date(item.createdAt).getTime() <*/}
                {/*    30 * 24 * 60 * 60 * 1000 && (*/}
                {/*    <div className="border border-green-400 rounded-lg px-4">*/}
                {/*      <p className="font-light rexton-light text-green-400">*/}
                {/*        {t("new", "New")}*/}
                {/*      </p>*/}
                {/*    </div>*/}
                {/*  )}*/}
              </div>

              <AnimatePresence>
                {activeIndex === index && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden mt-2"
                  >
                    {item.answer.map((ansKey, idx) =>
                      item.isTrans ? (
                        <p
                          key={idx}
                          className="text-sm md:text-xl manrope-light font-light mb-2"
                          style={{ color: "rgb(212, 212, 212)" }}
                        >
                          <Trans
                            i18nKey={ansKey}
                            components={{ 1: <strong /> }}
                          />
                        </p>
                      ) : (
                        <p
                          key={idx}
                          className="text-sm md:text-xl manrope-light font-light mb-2"
                          style={{ color: "rgb(212, 212, 212)" }}
                        >
                          {t(ansKey)}
                        </p>
                      ),
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}

export default FAQ;
