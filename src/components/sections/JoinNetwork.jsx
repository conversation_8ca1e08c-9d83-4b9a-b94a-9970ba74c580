import React, { useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import {
  FaHandshake,
  FaUsers,
  FaGift,
  FaInfinity,
  FaPercentage,
  FaArrowRight,
  FaUserFriends,
  FaChalkboardTeacher,
  FaTrophy,
} from "react-icons/fa";
import LoginModal from "../modals/LoginModal.js";
import { ShortEvents } from "../short-events.js";

function JoinNetwork() {
  const { t } = useTranslation();
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const { isUser } = ShortEvents();
  return (
    <section
      id="join-network"
      className="py-16 pt-[100px] md:pt-[152px] relative overflow-hidden"
    >
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center flex justify-center items-center flex-col gap-3"
        >
          <motion.div
            className={
              "text-sm border border-[#282828] mx-auto rounded-full px-4 py-2 hover:bg-[#4ade80] hover:text-black transition-colors mb-[100px] max-md:my-6 flex gap-2 items-center justify-center"
            }
          >
            <FaHandshake size={16} />
            <span className="text-sm">{t("joinNetwork.affiliateProgram")}</span>
          </motion.div>
          <h2 className="text-md md:text-[30px] font-light mx-auto rexton-light">
            {t("joinNetwork.title")}
          </h2>
          <p
            className="text-sm md:text-xl manrope-light font-light mb-[48px]"
            style={{
              color: "rgb(212, 212, 212)",
            }}
          >
            {t("joinNetwork.subtitle")}
          </p>
        </motion.div>

        {/* Main Content - Wider cards with better alignment */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* Left Column - Benefits */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="relative  backdrop-blur-sm rounded-2xl border transition-all duration-300 p-8 bg-[#19263d] border-[#1c2635] overflow-hidden"
          >
            {/* Large background icon */}
            <div className="absolute -bottom-16 -right-16 text-[200px] opacity-3">
              <FaGift />
            </div>

            <div className="relative z-10 flex-grow">
              <div className="flex items-center gap-3 mb-6">
                <FaGift className="text-accent min-w-[24px]" size={24} />
                <h3 className="text-[16px] font-light rexton-light max-sm:text-sm">
                  {t("joinNetwork.registerBring")}
                </h3>
              </div>

              <div className="space-y-8 mb-8">
                <div>
                  <h4 className="text-3xl font-light text-accent mb-2">
                    $1,000
                  </h4>
                  <p className="text-xl text-gray-300 font-light">
                    {t("joinNetwork.aiCourse")}
                  </p>
                  <p className="text-gray-400 font-light">
                    {t("joinNetwork.courseDescription")}
                  </p>
                </div>

                <div>
                  <h4 className="text-3xl font-light text-[#7C3AED] mb-2">
                    {t("joinNetwork.digitalBook")}
                  </h4>
                  <p className="text-gray-400 font-light">
                    {t("joinNetwork.bookDescription")}
                  </p>
                </div>

                <div>
                  <h4 className="text-3xl font-light text-accent-blue mb-2">
                    {t("joinNetwork.exclusiveBenefits")}
                  </h4>
                  <p className="text-gray-400 font-light">
                    {t("joinNetwork.benefitsDescription")}
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-auto">
              {isUser ? (
                <a href={import.meta.env.VITE_AFFILIATE_URL}>
                  <motion.button
                    title={t("joinNetwork.registerNow")}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="relative z-10 flex w-full cursor-pointer flex-1 py-3 rounded-xl transition-all duration-300 bg-green-500 hover:bg-green-600 text-dark-900 items-center justify-center gap-3 group"
                  >
                    <span>{t("joinNetwork.registerNow")}</span>
                    <FaArrowRight className="group-hover:translate-x-1 transition-transform" />
                  </motion.button>
                </a>
              ) : (
                <button
                  title={t("joinNetwork.registerNow")}
                  onClick={() => {
                    setIsLoginModalOpen(true);
                  }}
                  className={"w-full"}
                >
                  <motion.button
                    title={t("joinNetwork.registerNow")}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="relative z-10 flex w-full cursor-pointer flex-1 py-3 rounded-xl transition-all duration-300 bg-green-500 hover:bg-green-600 text-dark-900 items-center justify-center gap-3 group"
                  >
                    <span>{t("joinNetwork.registerNow")}</span>
                    <FaArrowRight className="group-hover:translate-x-1 transition-transform" />
                  </motion.button>
                </button>
              )}
            </div>
          </motion.div>

          {/* Right Column - Team Earnings */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="relative  backdrop-blur-sm rounded-2xl border transition-all duration-300 p-8 bg-[#19263d] border-[#1c2635] overflow-hidden flex flex-col"
          >
            {/* Large background icon */}
            <div className="absolute -bottom-16 -right-16 text-[200px] opacity-3">
              <FaUsers />
            </div>

            <div className="relative z-10 flex-grow">
              <div className="flex items-center gap-3 mb-6">
                <FaUsers
                  className="text-accent-purple min-w-[24px]"
                  size={24}
                />
                <h3 className="text-[16px] font-light text-white rexton-light max-sm:text-sm">
                  {t("joinNetwork.teamEarnings")}
                </h3>
              </div>

              <div className="grid grid-cols-2 max-sm:grid-cols-1 gap-6 mb-8">
                <div className="relative relative  backdrop-blur-sm rounded-2xl border transition-all duration-300 p-6 bg-[#19243b] border-[#4ade80] overflow-hidden">
                  <div className="absolute -bottom-8 -right-8 text-[100px] opacity-3">
                    <FaInfinity />
                  </div>
                  <div className="relative z-10">
                    <div className="text-6xl font-light text-accent-purple mb-2">
                      ∞
                    </div>
                    <p className="text-gray-400 font-light text-white">
                      {t("joinNetwork.teamEarnings")}
                    </p>
                  </div>
                </div>

                <div className="relative relative  backdrop-blur-sm rounded-2xl border transition-all duration-300 p-6 bg-[#19243b] border-[#4ade80] overflow-hidden">
                  <div className="absolute -bottom-8 -right-8 text-[100px] opacity-3">
                    <FaPercentage />
                  </div>
                  <div className="relative z-10">
                    <div className="text-4xl font-light text-accent mb-4 mt-4">
                      50%
                    </div>
                    <p className="text-gray-400 font-light text-white">
                      {t("joinNetwork.upToCommission")}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4 mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-navy-800/80 flex items-center justify-center text-accent-blue">
                    <FaUserFriends />
                  </div>
                  <span className="font-light text-lg">
                    {t("joinNetwork.buildNetwork")}
                  </span>
                </div>

                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-navy-800/80 flex items-center justify-center text-accent-purple">
                    <FaChalkboardTeacher />
                  </div>
                  <span className="font-light text-lg">
                    {t("joinNetwork.weeklyMentorship")}
                  </span>
                </div>

                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-navy-800/80 flex items-center justify-center text-accent">
                    <FaTrophy />
                  </div>
                  <span className="font-light text-lg">
                    {t("joinNetwork.teamBonuses")}
                  </span>
                </div>
              </div>
            </div>

            <div className="mt-auto">
              {isUser ? (
                <a href={import.meta.env.VITE_AFFILIATE_URL}>
                  <motion.button
                    title={t("joinNetwork.startEarning")}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="relative cursor-pointer z-10 w-full bg-accent-purple hover:bg-accent-purple/90 text-white px-6 py-3 rounded-lg transition-all font-light flex items-center justify-center gap-2 group"
                  >
                    <span>{t("joinNetwork.startEarning")}</span>
                    <FaArrowRight className="group-hover:translate-x-1 transition-transform" />
                  </motion.button>
                </a>
              ) : (
                <button
                  title={t("joinNetwork.startEarning")}
                  onClick={() => {
                    setIsLoginModalOpen(true);
                  }}
                  className={"w-full"}
                >
                  <motion.button
                    title={t("joinNetwork.startEarning")}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="relative cursor-pointer z-10 w-full bg-accent-purple hover:bg-accent-purple/90 text-white px-6 py-3 rounded-lg transition-all font-light flex items-center justify-center gap-2 group"
                  >
                    <span>{t("joinNetwork.startEarning")}</span>
                    <FaArrowRight className="group-hover:translate-x-1 transition-transform" />
                  </motion.button>
                </button>
              )}
            </div>
          </motion.div>
        </div>
      </div>
      <AnimatePresence>
        <LoginModal
          isOpen={isLoginModalOpen}
          redirectOnAffiliate={true}
          onClose={() => setIsLoginModalOpen(false)}
          isRegister={false}
        />
      </AnimatePresence>
    </section>
  );
}

export default JoinNetwork;
