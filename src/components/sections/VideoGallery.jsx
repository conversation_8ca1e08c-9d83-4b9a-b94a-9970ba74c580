import React, { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { FaPlay, FaPause, FaExpand, FaVideo } from "react-icons/fa";

function VideoGallery() {
  const { t } = useTranslation();
  const [activeVideo, setActiveVideo] = useState(0);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [shouldAutoplay, setShouldAutoplay] = useState(false);
  const videoRef = useRef(null);
  const [isMobile, setIsMobile] = useState(false);
  const [hasPlayed, setHasPlayed] = useState(false);

  // Detectează dacă e mobil
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setShouldAutoplay(window.innerWidth >= 768); // autoplay doar dacă ecranul e ≥ 768px
    };

    handleResize(); // verifică la montare
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const videos = [
    {
      id: "videoBiela",
      title: "Biela.dev",
      thumbnail:
        "https://storage-dev.biela.dev/affiliate/resources/1751109705242-thumbail-first-video.webp",
      videoUrl:
        "https://storage-dev.biela.dev/affiliate/resources/1750504833108-Ideas-donât-wait-test1.5.mp4",
      color: "from-blue-500/20 to-blue-600/20",
      textColor: "text-blue-400",
    },
  ];

  const togglePlayPause = () => {
    setHasPlayed(true);
    setIsPlaying(true);
    if (videoRef.current) {
      if (videoRef.current.muted) {
        videoRef.current.muted = false;
        videoRef.current.currentTime = 0;
      }
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleVideoEnd = () => {
    setIsPlaying(false);
    if (videoRef.current) {
      videoRef.current.currentTime = 0;
    }
  };

  const handleFullscreen = () => {
    setIsFullScreen(true);
    if (videoRef.current) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen();
      } else if (videoRef.current.webkitRequestFullscreen) {
        videoRef.current.webkitRequestFullscreen();
      } else if (videoRef.current.msRequestFullscreen) {
        videoRef.current.msRequestFullscreen();
      }
    }
  };

  useEffect(() => {
    const handleFullScreenChange = () => {
      if (
        !document.fullscreenElement &&
        !document.webkitFullscreenElement &&
        !document.msFullscreenElement
      ) {
        setIsFullScreen(false);
      }
    };

    document.addEventListener("fullscreenchange", handleFullScreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullScreenChange);
    document.addEventListener("msfullscreenchange", handleFullScreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
      document.removeEventListener(
        "webkitfullscreenchange",
        handleFullScreenChange,
      );
      document.removeEventListener(
        "msfullscreenchange",
        handleFullScreenChange,
      );
    };
  }, []);

  return (
    <section id="demo" className="section-wrapper pt-[72px]">
      <div className="section-content flex flex-col items-center gap-[72px] justify-center">
        <div className="flex items-center justify-center mb-[24px]">
          <motion.a
            href="/explore"
            className="flex gap-2 items-center justify-center px-6 py-2 border border-[#4ade80] rounded-full text-[#4ade80] hover:bg-[#4ade80] hover:text-black transition-colors duration-200 font-light text-sm"
          >
            <FaVideo />
            {t("exploreHowToUseBiela", "Explore How to Use Biela")}
          </motion.a>
        </div>

        <div className="mx-auto w-full">
          <div className="grid grid-cols-1 gap-y-8 px-0 md:px-20">
            <div className="col-span-1">
              <div className="rounded-xl overflow-hidden relative h-full flex flex-col">
                <div className="relative overflow-hidden rounded-lg flex-grow">
                  {isMobile && !hasPlayed ? (
                    <img
                      src={videos[activeVideo].thumbnail}
                      alt={videos[activeVideo].title}
                      width={isMobile ? 300 : 570}
                      height={isMobile ? 200 : 300}
                      className="w-full h-full aspect-[16/9] max-h-[520px] object-contain rounded-lg"
                      onClick={() => {
                        setHasPlayed(true);
                        setIsPlaying(true);
                        setTimeout(() => videoRef.current?.play(), 50); // redare cu delay
                      }}
                    />
                  ) : (
                    <video
                      ref={videoRef}
                      src={videos[activeVideo].videoUrl}
                      preload="metadata"
                      className={`w-full h-full aspect-[16/9] max-h-[520px] ${isFullScreen ? "" : "object-contain"}`}
                      poster={videos[activeVideo].thumbnail}
                      onEnded={handleVideoEnd}
                      autoPlay={!isMobile && shouldAutoplay}
                      muted
                      playsInline
                    />
                  )}

                  {/* Video Controls Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 flex flex-col justify-between p-4">
                    <div className="flex justify-end">
                      <button
                        title={"Expand"}
                        onClick={handleFullscreen}
                        className="p-2 bg-black/50 rounded-full text-white hover:bg-accent/80 transition-colors"
                      >
                        <FaExpand />
                      </button>
                    </div>

                    <div className="flex items-center justify-between">
                      <h3 className="text-white text-xl font-light">
                        {videos[activeVideo].title}
                      </h3>

                      <button
                        title={isPlaying ? "Pause" : "Play"}
                        onClick={togglePlayPause}
                        className="p-3 bg-accent rounded-full text-white hover:bg-[#4ade80] transition-colors"
                      >
                        {isPlaying ? <FaPause /> : <FaPlay />}
                      </button>
                    </div>
                  </div>

                  {!isPlaying && (
                    <div
                      className="absolute inset-0 flex items-center justify-center cursor-pointer"
                      onClick={togglePlayPause}
                    >
                      <motion.div
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="w-16 h-16 bg-[#4ade80] rounded-full flex items-center justify-center"
                      >
                        <FaPlay className="text-white text-xl ml-1" />
                      </motion.div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default VideoGallery;
