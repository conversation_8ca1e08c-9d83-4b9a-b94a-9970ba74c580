import React, { useState, useEffect } from "react";
import CookieSettings from "./CookieSettings";
import { motion, AnimatePresence } from "framer-motion";
import { Link } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON>, Shield, BarChart3, Target } from "lucide-react";
import { FaCookie } from "react-icons/fa";

const ConsentBanner = () => {
  const [showBanner, setShowBanner] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showCompactSettings, setShowCompactSettings] = useState(false);
  const [activeTab, setActiveTab] = useState("consent");
  const [hasConsent, setHasConsent] = useState(false);
  const [consent, setConsent] = useState({
    necessary: true,
    preferences: true,
    statistics: true,
    marketing: true,
  });

  const getCookieIcon = (type) => {
    switch (type) {
      case "necessary":
        return <Shield className="w-5 h-5 text-[#4ade80]" />;
      case "preferences":
        return <Cookie className="w-5 h-5 text-[#4ade80]" />;
      case "statistics":
        return <BarChart3 className="w-5 h-5 text-[#4ade80]" />;
      case "marketing":
        return <Target className="w-5 h-5 text-[#4ade80]" />;
      default:
        return <Cookie className="w-5 h-5 text-[#4ade80]" />;
    }
  };

  useEffect(() => {
    const getCookie = (name) => {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2)
        return decodeURIComponent(parts.pop().split(";").shift());
    };

    const savedConsent = getCookie("cookieConsent");
    if (savedConsent) {
      const parsed = JSON.parse(savedConsent);
      setConsent(parsed);
      setHasConsent(true);
      return;
    }

    fetch("https://ipapi.co/json/")
      .then((res) => res.json())
      .then((data) => {
        const euCountries = [
          "AT",
          "BE",
          "BG",
          "HR",
          "CY",
          "CZ",
          "DK",
          "EE",
          "FI",
          "FR",
          "DE",
          "GR",
          "HU",
          "IE",
          "IT",
          "LV",
          "LT",
          "LU",
          "MT",
          "NL",
          "PL",
          "PT",
          "RO",
          "SK",
          "SI",
          "ES",
          "SE",
          "NO",
          "IS",
          "LI",
        ];
        if (euCountries.includes(data.country_code)) {
          gtag("consent", "default", {
            ad_storage: "denied",
            ad_user_data: "denied",
            ad_personalization: "denied",
            analytics_storage: "denied",
          });
          clearTrackingCookies();
          setShowBanner(true);
        } else {
          const fullConsent = {
            necessary: true,
            preferences: true,
            statistics: true,
            marketing: true,
            timestamp: new Date().toISOString(),
          };

          const isLocalhost =
            window.location.hostname === "localhost" ||
            window.location.hostname === "127.0.0.1";
          const domainPart = isLocalhost ? "" : `; domain=.biela.dev`;

          document.cookie = `cookieConsent=${encodeURIComponent(
            JSON.stringify(fullConsent),
          )}; path=/; max-age=31536000; SameSite=Lax${domainPart}`;

          setConsent(fullConsent);
          setHasConsent(true);
          applyCookieSettings(fullConsent);
        }
      })
      .catch(() => {
        setShowBanner(true);
      });
  }, []);

  const saveConsent = (consentData) => {
    const consentWithTimestamp = {
      ...consentData,
      timestamp: new Date().toISOString(),
    };
    const isLocalhost =
      window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1";
    const domainPart = isLocalhost ? "" : `; domain=.biela.dev`;

    document.cookie = `cookieConsent=${encodeURIComponent(
      JSON.stringify(consentWithTimestamp),
    )}; path=/; max-age=31536000; SameSite=Lax${domainPart}`;
    setConsent(consentData);

    gtag("consent", "update", {
      ad_storage: consentData.marketing ? "granted" : "denied",
      ad_user_data: consentData.marketing ? "granted" : "denied",
      ad_personalization: consentData.marketing ? "granted" : "denied",
      analytics_storage: consentData.statistics ? "granted" : "denied",
    });
    if (consent.statistics) {
      window.clarity && clarity("consent");
    }
    setHasConsent(true);
    setShowBanner(false);
    setShowSettings(false);
    setShowCompactSettings(false);

    applyCookieSettings(consentData);
  };

  const applyCookieSettings = (consentData) => {
    const isLocalhost =
      window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1";
    const domainPart = isLocalhost ? "" : `; domain=.biela.dev`;
    if (!consentData.preferences) {
      removeConsentCookies("preferences");
    }
    if (!consentData.statistics) {
      removeConsentCookies("statistics");
    }
    if (!consentData.marketing) {
      removeConsentCookies("marketing");
    }

    if (consentData.preferences) {
      document.cookie = `user_preferences=enabled; path=/; ${domainPart}; max-age=31536000; SameSite=Lax`;
    }
    if (consentData.statistics) {
      document.cookie = `analytics_enabled=true; path=/; ${domainPart}; max-age=31536000; SameSite=Lax`;
    }
    if (consentData.marketing) {
      document.cookie = `marketing_enabled=true; path=/; ${domainPart}; max-age=31536000; SameSite=Lax`;
    }
  };

  const removeConsentCookies = (category) => {
    const cookiesToRemove = {
      preferences: ["user-language-cookie"],
      statistics: ["_ga", "_ga_CSZ44DYPWS", "_clck", "_clsk"],
      marketing: [
        "marketing_enabled",
        "_gcl_au",
        "_rdt_em",
        "_rdt_uuid",
        "reddit_session",
      ],
    };

    cookiesToRemove[category]?.forEach((cookieName) => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.biela.dev`;
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname};`;
    });
  };

  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      preferences: true,
      statistics: true,
      marketing: true,
    };
    saveConsent(allAccepted);
  };

  const handleRejectAll = () => {
    const onlyNecessary = {
      necessary: true,
      preferences: false,
      statistics: false,
      marketing: false,
    };
    saveConsent(onlyNecessary);
  };

  const handleCustomize = () => {
    setActiveTab("details");
  };

  const handleSaveSettings = () => {
    saveConsent(consent);
  };

  const handleToggleConsent = (category) => {
    if (category === "necessary") return;

    setConsent((prev) => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  const handleOpenSettings = () => {
    setShowCompactSettings(true);
  };

  const handleChangeConsent = () => {
    setShowCompactSettings(false);
    setShowBanner(true);
    setActiveTab("consent");
  };

  const handleDenyAll = () => {
    const onlyNecessary = {
      necessary: true,
      preferences: false,
      statistics: false,
      marketing: false,
    };
    saveConsent(onlyNecessary);
  };

  const getPreferencesList = () => {
    const categories = [
      { key: "necessary", label: "Necessary", enabled: consent.necessary },
      {
        key: "preferences",
        label: "Preferences",
        enabled: consent.preferences,
      },
      { key: "statistics", label: "Statistics", enabled: consent.statistics },
      { key: "marketing", label: "Marketing", enabled: consent.marketing },
    ];
    return categories;
  };

  const ToggleSwitch = ({ checked, onChange, disabled = false }) => (
    <label
      className={`
      relative inline-block w-12 h-6
      ${disabled ? "cursor-not-allowed opacity-60" : "cursor-pointer"}
    `}
    >
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        className="opacity-0 w-0 h-0"
      />
      <span
        className={`
        absolute top-0 left-0 right-0 bottom-0 rounded-full transition-all duration-300 border-2
        ${checked ? "bg-gradient-to-r from-[#4ade80] to-[#22c55e] border-[#4ade80]" : "bg-slate-600 border-slate-500"}
        ${disabled ? "opacity-60" : ""}
        before:absolute before:content-[''] before:h-4 before:w-4 before:left-0.5 before:bottom-0.5
        before:bg-slate-200 before:transition-all before:duration-300 before:rounded-full
        ${checked ? "before:translate-x-6 before:bg-white" : ""}
      `}
      />
    </label>
  );

  const renderConsentTab = () => (
    <div className="flex flex-col gap-8 items-center w-full">
      <div className="text-center">
        <div className={"flex items-center justify-center gap-3 mb-6"}>
          <Cookie className="w-8 h-8 text-[#4ade80]" />
          <h3 className="text-2xl font-bold text-white">We use cookies</h3>
        </div>

        <p className="text-[#D5D5D5] text-base leading-relaxed max-w-md">
          We use cookies and similar technologies to provide the best experience
          on our website. Some are necessary for the site to function, while
          others help us improve your experience by providing insights into how
          the site is being used.
        </p>
      </div>
      <div className="flex flex-col gap-4 w-full max-w-md">
        <button
          title={"Accept All Cookies"}
          className="w-full bg-[#4ade80] hover:bg-[#1FC55C] text-black hover:text-white py-3 rounded-lg transition-colors flex items-center justify-center gap-2 cursor-pointer"
          onClick={handleAcceptAll}
        >
          Accept All Cookies
        </button>

        <button
          title={"Customize Settings"}
          className="w-full bg-[#282828] text-white px-4 py-3 rounded-lg cursor-pointer hover:bg-white/20 transition-colors"
          onClick={handleCustomize}
        >
          Customize Settings
        </button>

        <button
          title={"Reject All"}
          className="w-full bg-transparent text-[#D5D5D5] border border-white/20 px-4 py-3 rounded-lg cursor-pointer hover:bg-white/10 transition-colors"
          onClick={handleRejectAll}
        >
          Reject All
        </button>
      </div>
    </div>
  );

  const renderDetailsTab = () => (
    <div className="flex flex-col gap-8 items-center w-full">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-white mb-4">Cookie Details</h3>
        <p className="text-[#D5D5D5] text-base leading-relaxed max-w-md">
          Manage your cookie preferences below. You can enable or disable
          different types of cookies.
        </p>
      </div>

      <div className="flex flex-col gap-4 w-full max-w-lg">
        <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4 transition-all hover:border-[#4ade80]/50">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              {getCookieIcon("necessary")}
              <div>
                <h4 className="text-white font-semibold">Necessary Cookies</h4>
                <p className="text-[#D5D5D5] text-sm">
                  Essential for website functionality
                </p>
              </div>
            </div>
            <ToggleSwitch checked={consent.necessary} disabled={true} />
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4 transition-all hover:border-[#4ade80]/50">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              {getCookieIcon("preferences")}
              <div>
                <h4 className="text-white font-semibold">Preference Cookies</h4>
                <p className="text-[#D5D5D5] text-sm">
                  Remember your settings and preferences
                </p>
              </div>
            </div>
            <ToggleSwitch
              checked={consent.preferences}
              onChange={() => handleToggleConsent("preferences")}
            />
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4 transition-all hover:border-[#4ade80]/50">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              {getCookieIcon("statistics")}
              <div>
                <h4 className="text-white font-semibold">Statistics Cookies</h4>
                <p className="text-[#D5D5D5] text-sm">
                  Help us understand website usage
                </p>
              </div>
            </div>
            <ToggleSwitch
              checked={consent.statistics}
              onChange={() => handleToggleConsent("statistics")}
            />
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4 transition-all hover:border-[#4ade80]/50">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              {getCookieIcon("marketing")}
              <div>
                <h4 className="text-white font-semibold">Marketing Cookies</h4>
                <p className="text-[#D5D5D5] text-sm">
                  Used for advertising and tracking
                </p>
              </div>
            </div>
            <ToggleSwitch
              checked={consent.marketing}
              onChange={() => handleToggleConsent("marketing")}
            />
          </div>
        </div>
      </div>

      <button
        title={"Save Settings"}
        className="w-full max-w-md bg-[#4ade80] hover:bg-[#1FC55C] text-black hover:text-white py-3 rounded-lg transition-colors flex items-center justify-center gap-2 cursor-pointer"
        onClick={handleSaveSettings}
      >
        Save Settings
      </button>
    </div>
  );

  const renderAboutTab = () => (
    <div className="flex flex-col gap-6 w-full max-w-2xl">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-white mb-4">About Cookies</h3>
      </div>

      <div className="text-[#D5D5D5] leading-relaxed space-y-4 max-h-[60vh] overflow-y-auto">
        <p>
          Cookies are small text files that can be used by websites to make a
          user's experience more efficient.
        </p>

        <p>
          The law states that we can store cookies on your device if they are
          strictly necessary for the operation of this site. For all other types
          of cookies we need your permission. This means that cookies which are
          categorized as necessary, are processed based on GDPR Art. 6 (1) (f).
          All other cookies, meaning those from the categories preferences and
          marketing, are processed based on GDPR Art. 6 (1) (a) GDPR.
        </p>

        <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
          <h4 className="text-[#4ade80] text-lg font-semibold mb-4">
            Types of Cookies We Use:
          </h4>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              {getCookieIcon("necessary")}
              <div>
                <strong className="text-white">Necessary:</strong>
                <span className="text-[#D5D5D5]">
                  {" "}
                  Essential for the website to function properly
                </span>
              </div>
            </div>
            <div className="flex items-start gap-3">
              {getCookieIcon("preferences")}
              <div>
                <strong className="text-white">Preferences:</strong>
                <span className="text-[#D5D5D5]">
                  {" "}
                  Remember your settings and choices
                </span>
              </div>
            </div>
            <div className="flex items-start gap-3">
              {getCookieIcon("statistics")}
              <div>
                <strong className="text-white">Statistics:</strong>
                <span className="text-[#D5D5D5]">
                  {" "}
                  Help us analyze website performance
                </span>
              </div>
            </div>
            <div className="flex items-start gap-3">
              {getCookieIcon("marketing")}
              <div>
                <strong className="text-white">Marketing:</strong>
                <span className="text-[#D5D5D5]">
                  {" "}
                  Used to deliver relevant advertisements
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4">
          <h4 className="text-[#4ade80] text-lg font-semibold mb-3">
            Your Rights:
          </h4>
          <p>
            You have the right to accept or reject cookies. You can change your
            preferences at any time by clicking on the cookie settings link in
            our footer or by revisiting this banner.
          </p>
        </div>

        <p>
          For more information about our privacy practices, please read our
          <Link
            to="/privacy"
            target="_blank"
            className="text-[#4ade80] hover:underline ml-1"
          >
            Privacy Policy
          </Link>
          .
        </p>
      </div>
    </div>
  );

  const renderCompactSettings = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95, y: 20 }}
      className="fixed bottom-24 left-5 bg-[#11182B] border border-[#282828] rounded-xl p-4 shadow-xl z-[10000] min-w-80 max-w-sm"
    >
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-white text-lg font-semibold">Cookie Settings</h4>
        <button
          title={"Close Cookie Settings"}
          onClick={() => setShowCompactSettings(false)}
          className="text-[#D5D5D5] hover:text-white transition-colors cursor-pointer"
        >
          <X className="w-4 h-4" />
        </button>
      </div>

      <div className="space-y-3 mb-4">
        {getPreferencesList().map((category) => (
          <div key={category.key} className="flex justify-between items-center">
            <span className="text-[#D5D5D5] text-sm">{category.label}</span>
            <span
              className={`text-xs px-2 py-1 rounded ${
                category.enabled
                  ? "text-[#4ade80] bg-[#4ade80]/10"
                  : "text-red-400 bg-red-400/10"
              }`}
            >
              {category.enabled ? "Enabled" : "Disabled"}
            </span>
          </div>
        ))}
      </div>

      <div className="flex gap-2">
        <button
          title={"Deny All"}
          className="flex-1 bg-transparent text-[#D5D5D5] border border-white/20 px-3 py-2 rounded-lg text-sm cursor-pointer hover:bg-white/10 transition-colors"
          onClick={handleDenyAll}
        >
          Deny All
        </button>
        <button
          title={"Change Cookie Settings"}
          className="flex-1 bg-[#4ade80] hover:bg-[#1FC55C] text-black px-3 py-2 rounded-lg text-sm cursor-pointer transition-colors"
          onClick={handleChangeConsent}
        >
          Change
        </button>
      </div>
    </motion.div>
  );

  useEffect(() => {
    console.log("Active tab is:", activeTab);
  }, [activeTab]);

  return (
    <>
      <AnimatePresence>
        {showBanner && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowBanner(false)}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[10000] p-4 animate-fade-in"
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-[#11182B] border border-[#282828] max-w-[700px] w-full shadow-xl relative rounded-2xl"
            >
              <X
                className="absolute top-6 right-6 md:top-8 md:right-8 cursor-pointer z-10 text-[#D5D5D5] hover:text-white transition-colors duration-300"
                onClick={() => setShowBanner(false)}
              />
              <div
                className={"flex flex-col max-md:px-6 max-md:py-12 px-8 py-8"}
              >
                {activeTab === "consent" && renderConsentTab()}
                {activeTab === "details" && renderDetailsTab()}
                {activeTab === "about" && renderAboutTab()}
                <div className="flex gap-2 justify-center w-full mt-8 pt-6 border-t border-white/10">
                  <button
                    title={"Consent"}
                    className={`cursor-pointer px-4 py-2 text-sm transition-colors ${
                      activeTab === "consent"
                        ? "text-[#4ade80] border-b-2 border-[#4ade80]"
                        : "text-[#D5D5D5] hover:text-white"
                    }`}
                    onClick={() => setActiveTab("consent")}
                  >
                    Consent
                  </button>
                  <button
                    title={"Details"}
                    className={`cursor-pointer px-4 py-2 text-sm transition-colors ${
                      activeTab === "details"
                        ? "text-[#4ade80] border-b-2 border-[#4ade80]"
                        : "text-[#D5D5D5] hover:text-white"
                    }`}
                    onClick={() => setActiveTab("details")}
                  >
                    Details
                  </button>

                  <button
                    title={"About"}
                    className={`cursor-pointer px-4 py-2 text-sm transition-colors ${
                      activeTab === "about"
                        ? "text-[#4ade80] border-b-2 border-[#4ade80]"
                        : "text-[#D5D5D5] hover:text-white"
                    }`}
                    onClick={() => setActiveTab("about")}
                  >
                    About
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      {showSettings && (
        <CookieSettings
          consent={consent}
          onSave={saveConsent}
          onClose={() => setShowSettings(false)}
        />
      )}
      {hasConsent && !showBanner && (
        <>
          <button
            style={{
              backgroundColor: "rgb(10, 23, 48)",
              boxShadow: "rgba(23, 73, 77, 0.15) 0px 20px 30px",
            }}
            className="fixed bottom-5 left-5 w-15 h-15 rounded-full border-none text-2xl cursor-pointer
                     transition-all duration-300
                     active:translate-y-0 active:scale-95 z-[9999] flex items-center justify-center"
            onClick={handleOpenSettings}
            title="Cookie Settings"
          >
            <Cookie className="w-8 h-8 text-white" />
          </button>

          {showCompactSettings && renderCompactSettings()}
        </>
      )}
    </>
  );
};

export default ConsentBanner;
