import React, { useState } from "react";

const CookieSettings = ({ consent, onSave, onClose }) => {
  const [settings, setSettings] = useState(consent);

  const cookieCategories = [
    {
      id: "necessary",
      title: "Necessary Cookies",
      description:
        "These cookies are essential for the website to function properly. They cannot be disabled.",
      required: true,
      examples: "Session cookies, security cookies, load balancing cookies",
    },
    {
      id: "preferences",
      title: "Preference Cookies",
      description:
        "These cookies allow the website to remember choices you make and provide enhanced features.",
      required: false,
      examples: "Language preferences, theme settings, region selection",
    },
    {
      id: "statistics",
      title: "Statistics Cookies",
      description:
        "These cookies help us understand how visitors interact with our website.",
      required: false,
      examples: "Google Analytics, page view tracking, user behavior analysis",
    },
    {
      id: "marketing",
      title: "Marketing Cookies",
      description:
        "These cookies are used to deliver relevant advertisements and track campaign effectiveness.",
      required: false,
      examples: "Facebook Pixel, Google Ads, retargeting cookies",
    },
  ];

  const handleToggle = (categoryId) => {
    if (categoryId === "necessary") return;

    setSettings((prev) => ({
      ...prev,
      [categoryId]: !prev[categoryId],
    }));
  };

  const handleSave = () => {
    onSave(settings);
  };

  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      preferences: true,
      statistics: true,
      marketing: true,
    };
    setSettings(allAccepted);
    onSave(allAccepted);
  };

  const handleRejectAll = () => {
    const onlyNecessary = {
      necessary: true,
      preferences: false,
      statistics: false,
      marketing: false,
    };
    setSettings(onlyNecessary);
    onSave(onlyNecessary);
  };

  const ToggleSwitch = ({ checked, onChange, disabled = false }) => (
    <label className="relative inline-block w-15 h-7">
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        className="opacity-0 w-0 h-0"
      />
      <span
        className={`
        absolute cursor-pointer top-0 left-0 right-0 bottom-0 rounded-full transition-all duration-300
        border-2 ${
          checked
            ? "bg-gradient-to-r from-[#4ade80] to-[#22c55e] border-[#4ade80]"
            : "bg-slate-600 border-slate-500"
        } ${disabled ? "opacity-80 cursor-not-allowed" : ""}
        before:absolute before:content-[''] before:h-5 before:w-5 before:left-0.5 before:bottom-0.5
        before:bg-slate-200 before:transition-all before:duration-300 before:rounded-full
        ${checked ? "before:translate-x-7 before:bg-white" : ""}
      `}
      />
    </label>
  );

  return (
    <div className="fixed inset-0 bg-[#0a1730]/90 flex items-center justify-center z-[10001] p-4 animate-fade-in">
      <div
        className="bg-[#0a1730] rounded-xl max-w-3xl w-full max-h-[90vh] flex flex-col shadow-2xl 
                    border border-[#4ade80]/20 overflow-hidden animate-slide-up"
      >
        <div
          className="flex justify-between items-center p-8 border-b border-[#4ade80]/20 
                      bg-gradient-to-r from-[#0a1730] to-[#0a0f1c]"
        >
          <h2 className="text-white text-3xl font-bold">Cookie Settings</h2>
          <button
            title={"Close Cookie Settings"}
            className="bg-none border-none text-2xl cursor-pointer text-slate-300 p-2 w-10 h-10 
                     flex items-center justify-center rounded-full transition-all duration-300 
                     hover:bg-[#4ade80]/10 hover:text-[#4ade80]"
            onClick={onClose}
          >
            ×
          </button>
        </div>

        <div className="flex-1 overflow-y-auto p-8 bg-[#0a0f1c]">
          <div className="mb-8">
            <p className="text-slate-300 leading-relaxed text-base">
              We use cookies to enhance your browsing experience and analyze
              website traffic. You can choose which types of cookies you want to
              allow. Your choices will be saved and can be changed at any time.
            </p>
          </div>

          <div className="flex flex-col gap-6">
            {cookieCategories.map((category) => (
              <div
                key={category.id}
                className="border border-[#4ade80]/20 rounded-lg p-6 
                                             bg-[#0a1730]/50 transition-all duration-300 
                                             hover:border-[#4ade80]/40 hover:bg-[#0a1730]/70"
              >
                <div className="flex justify-between items-start gap-6">
                  <div className="flex-1">
                    <h3 className="text-white text-xl font-semibold mb-3">
                      {category.title}
                    </h3>
                    <p className="text-slate-300 text-base leading-relaxed mb-4">
                      {category.description}
                    </p>
                    <div className="text-sm text-slate-400 p-3 bg-[#4ade80]/5 rounded-md border-l-4 border-[#4ade80]">
                      <strong className="text-[#4ade80] font-semibold">
                        Examples:
                      </strong>{" "}
                      {category.examples}
                    </div>
                  </div>
                  <div className="flex flex-col items-center gap-3">
                    <ToggleSwitch
                      checked={settings[category.id]}
                      onChange={() => handleToggle(category.id)}
                      disabled={category.required}
                    />
                    {category.required && (
                      <span className="text-xs text-[#4ade80] text-center font-semibold uppercase tracking-wider">
                        Always On
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="border-t border-[#4ade80]/20 p-8 bg-[#0a1730]">
          <div className="flex gap-4 justify-end">
            <button
              title={"Reject All Cookie Settings"}
              className="px-8 py-4 bg-transparent text-slate-300 border-2 border-slate-600 
                       font-semibold rounded-lg transition-all duration-300 hover:bg-slate-600 
                       hover:text-white hover:border-slate-500 uppercase tracking-wider text-base"
              onClick={handleRejectAll}
            >
              Reject All
            </button>
            <button
              title={"Save Preferences Cookie"}
              className="px-8 py-4 bg-transparent text-[#4ade80] border-2 border-[#4ade80] 
                       font-semibold rounded-lg transition-all duration-300 hover:bg-[#4ade80]/10 
                       hover:text-[#22c55e] hover:border-[#22c55e] uppercase tracking-wider text-base"
              onClick={handleSave}
            >
              Save Preferences
            </button>
            <button
              title={"Accept All Cookies"}
              className="px-8 py-4 bg-gradient-to-r from-[#4ade80] to-[#22c55e] text-[#0a1730] 
                       font-semibold rounded-lg transition-all duration-300 hover:from-[#22c55e] 
                       hover:to-[#16a34a] hover:-translate-y-0.5 hover:shadow-lg 
                       hover:shadow-[#4ade80]/40 uppercase tracking-wider text-base"
              onClick={handleAcceptAll}
            >
              Accept All
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookieSettings;
