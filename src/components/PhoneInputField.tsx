import React, { useEffect, useRef, useState } from "react";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { E164Number } from "libphonenumber-js/types";
import { IoIosGlobe } from "react-icons/io";
import { IconBaseProps } from "react-icons/lib";
import { JSX } from "react/jsx-runtime";

interface PhoneInputFieldProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  label: string;
  className?: string;
  placeholder: boolean;
  error?: string;
  onReady?: (el: HTMLInputElement | null) => void;
}

const InternationalIcon = (props: JSX.IntrinsicAttributes & IconBaseProps) => (
  <IoIosGlobe style={{ color: "#9ca3af" }} />
);

const PhoneInputField: React.FC<PhoneInputFieldProps> = ({
  value,
  onChange,
  disabled = false,
  label,
  className = "",
  onReady,
  placeholder,
  error = "",
}) => {
  const [phoneValue, setPhoneValue] = useState<E164Number | undefined>(
    value as E164Number,
  );

  const internalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (internalRef.current) {
      const input = internalRef.current.querySelector("input");
      if (onReady && input) {
        onReady(input as HTMLInputElement);
      }
    }
  }, [onReady]);

  useEffect(() => {
    if (value && value !== phoneValue) {
      setPhoneValue(value as E164Number);
    }
  }, [value]);

  const handlePhoneChange = (newValue: E164Number | undefined) => {
    setPhoneValue(newValue);
    onChange(newValue ?? "");
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {!placeholder && (
        <label className="text-gray-400 text-sm font-light">{label}</label>
      )}
      <div className="custom-phone-input" ref={internalRef}>
        <PhoneInput
          international
          value={phoneValue}
          onChange={handlePhoneChange}
          disabled={disabled}
          className={`w-full ${placeholder ? "bg-white/10" : "bg-black/20"} border border-gray-700  ${error ? "border-red-500 focus:ring-red-500" : "border-white/20"} rounded-lg text-white font-light focus:outline-none focus:ring-2 focus:ring-green-500 transition-all`}
          countrySelectProps={{
            unicodeFlags: true,
          }}
          error={error}
          placeholder={placeholder ? "Phone" : ""}
          internationalIcon={InternationalIcon}
        />
        {error && <span className="text-red-400 text-sm">{error}</span>}
      </div>
      <style>{`
       .custom-phone-input .PhoneInput {
          display: flex;
          align-items: center;
          ${placeholder ? "bg-white/10" : "bg-black/20"}
          border-radius: 0.5rem;
          padding: 0.75rem 1rem;
        }
        .custom-phone-input .PhoneInputCountry {
          margin-right: 0.75rem;
          display: flex;
          align-items: center;
        }
        .custom-phone-input .PhoneInputCountryIcon {
          width: 1.5rem;
          height: 1rem;
          border-radius: 2px;
          overflow: hidden;
        }
        .custom-phone-input .PhoneInputCountrySelectArrow {
          border-color: #9ca3af;
          margin-left: 0.5rem;
        }
        .custom-phone-input .PhoneInputInput {
          background: transparent;
          border: none;
          color: white;
          font-weight: 300;
          width: 100%;
          padding: 0;
          outline: none;
        }
        .custom-phone-input .PhoneInputInput:focus {
          outline: none;
          box-shadow: none;
        }
        .custom-phone-input .PhoneInputInput::placeholder {
          color: #9ca3af;
        }
        .PhoneInputCountrySelect {
          background: #0d192f;
          color: white; 
         }
        .PhoneInputCountrySelect option:checked {
          background-color: #1abc9c; 
          color: white;
        }
    `}</style>
    </div>
  );
};

export default PhoneInputField;
