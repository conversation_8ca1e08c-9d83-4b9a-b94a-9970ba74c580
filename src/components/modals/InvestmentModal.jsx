import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  <PERSON>a<PERSON>ser,
  FaEnvelope,
  FaBuilding,
  FaDollarSign,
  FaFileAlt,
  FaArrowRight,
  FaCheck,
  FaInfoCircle,
  FaLinkedin,
  FaGlobe,
} from "react-icons/fa";
import Modal from "./Modal";

function InvestmentModal({ isOpen, onClose }) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    website: "",
    linkedin: "",
    investmentAmount: "",
    message: "",
    document: null,
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [step, setStep] = useState(1);

  const investmentOptions = [
    { value: "$10,000 - $50,000", label: "Seed" },
    { value: "$50,000 - $250,000", label: "Angel" },
    { value: "$250,000 - $1,000,000", label: "Series A" },
    { value: "$1,000,000+", label: "Major Investment" },
  ];

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData((prev) => ({ ...prev, document: file }));
      // Clear error when user selects a file
      if (errors.document) {
        setErrors((prev) => ({ ...prev, document: "" }));
      }
    }
  };

  const validateStep1 = () => {
    const newErrors = {};

    if (!formData.name.trim()) newErrors.name = "Name is required";
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }
    if (!formData.company.trim())
      newErrors.company = "Company/Organization is required";

    return newErrors;
  };

  const validateStep2 = () => {
    const newErrors = {};

    if (!formData.investmentAmount)
      newErrors.investmentAmount = "Investment amount is required";
    if (!formData.message.trim()) newErrors.message = "Message is required";

    return newErrors;
  };

  const handleNextStep = () => {
    const newErrors = validateStep1();
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setStep(2);
  };

  const handlePrevStep = () => {
    setStep(1);
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    const newErrors = validateStep2();
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);

      // Reset form after submission
      setTimeout(() => {
        onClose();
        setIsSubmitted(false);
        setStep(1);
        setFormData({
          name: "",
          email: "",
          company: "",
          website: "",
          linkedin: "",
          investmentAmount: "",
          message: "",
          document: null,
        });
      }, 2000);
    }, 1500);
  };

  // Animation variants for form fields
  const formFieldVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (custom) => ({
      opacity: 1,
      y: 0,
      transition: { delay: custom * 0.1, duration: 0.3 },
    }),
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Investment Opportunity"
      maxWidth="max-w-2xl"
    >
      {!isSubmitted ? (
        <form onSubmit={handleSubmit} className="space-y-4">
          {step === 1 ? (
            <>
              <div className="text-gray-400 mb-6">
                Join us in revolutionizing the future of AI-powered development.
                Complete the form below to express your interest in investing in
                BIELA.
              </div>

              {/* Name Field */}
              <motion.div
                variants={formFieldVariants}
                initial="hidden"
                animate="visible"
                custom={0}
                className="space-y-2"
              >
                <label htmlFor="name" className="block text-sm font-light">
                  Full Name <span className="text-accent">*</span>
                </label>
                <div
                  className={`relative ${errors.name ? "animate-shake" : ""}`}
                >
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <FaUser />
                  </div>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className={`w-full bg-navy-700/30 rounded-lg pl-10 pr-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 ${
                      errors.name
                        ? "focus:ring-red-500 border border-red-500"
                        : "focus:ring-accent"
                    } transition-all font-light`}
                    placeholder="John Doe"
                  />
                </div>
                {errors.name && (
                  <p className="text-red-500 text-xs">{errors.name}</p>
                )}
              </motion.div>

              {/* Email Field */}
              <motion.div
                variants={formFieldVariants}
                initial="hidden"
                animate="visible"
                custom={1}
                className="space-y-2"
              >
                <label htmlFor="email" className="block text-sm font-light">
                  Email Address <span className="text-accent">*</span>
                </label>
                <div
                  className={`relative ${errors.email ? "animate-shake" : ""}`}
                >
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <FaEnvelope />
                  </div>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className={`w-full bg-navy-700/30 rounded-lg pl-10 pr-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 ${
                      errors.email
                        ? "focus:ring-red-500 border border-red-500"
                        : "focus:ring-accent"
                    } transition-all font-light`}
                    placeholder="<EMAIL>"
                  />
                </div>
                {errors.email && (
                  <p className="text-red-500 text-xs">{errors.email}</p>
                )}
              </motion.div>

              {/* Company Field */}
              <motion.div
                variants={formFieldVariants}
                initial="hidden"
                animate="visible"
                custom={2}
                className="space-y-2"
              >
                <label htmlFor="company" className="block text-sm font-light">
                  Company/Organization <span className="text-accent">*</span>
                </label>
                <div
                  className={`relative ${errors.company ? "animate-shake" : ""}`}
                >
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <FaBuilding />
                  </div>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleChange}
                    className={`w-full bg-navy-700/30 rounded-lg pl-10 pr-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 ${
                      errors.company
                        ? "focus:ring-red-500 border border-red-500"
                        : "focus:ring-accent"
                    } transition-all font-light`}
                    placeholder="Company Name"
                  />
                </div>
                {errors.company && (
                  <p className="text-red-500 text-xs">{errors.company}</p>
                )}
              </motion.div>

              {/* Website Field */}
              <motion.div
                variants={formFieldVariants}
                initial="hidden"
                animate="visible"
                custom={3}
                className="space-y-2"
              >
                <label htmlFor="website" className="block text-sm font-light">
                  Company Website
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <FaGlobe />
                  </div>
                  <input
                    type="text"
                    id="website"
                    name="website"
                    value={formData.website}
                    onChange={handleChange}
                    className="w-full bg-navy-700/30 rounded-lg pl-10 pr-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent transition-all font-light"
                    placeholder="https://example.com"
                  />
                </div>
              </motion.div>

              {/* LinkedIn Field */}
              <motion.div
                variants={formFieldVariants}
                initial="hidden"
                animate="visible"
                custom={4}
                className="space-y-2"
              >
                <label htmlFor="linkedin" className="block text-sm font-light">
                  LinkedIn Profile
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <FaLinkedin />
                  </div>
                  <input
                    type="text"
                    id="linkedin"
                    name="linkedin"
                    value={formData.linkedin}
                    onChange={handleChange}
                    className="w-full bg-navy-700/30 rounded-lg pl-10 pr-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent transition-all font-light"
                    placeholder="linkedin.com/in/johndoe"
                  />
                </div>
              </motion.div>

              {/* Next Button */}
              <motion.div
                variants={formFieldVariants}
                initial="hidden"
                animate="visible"
                custom={5}
                className="flex justify-end mt-6"
              >
                <motion.button
                  title={"Next Step"}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  onClick={handleNextStep}
                  className="bg-gradient-to-r from-accent to-accent-purple text-white px-6 py-3 rounded-lg font-light flex items-center gap-2 relative overflow-hidden group"
                >
                  <span>Next Step</span>
                  <FaArrowRight className="group-hover:translate-x-1 transition-transform" />
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 -skew-x-12 transition-opacity duration-300" />
                </motion.button>
              </motion.div>
            </>
          ) : (
            <>
              <div className="text-gray-400 mb-6">
                Please provide details about your investment interest and any
                additional information that might be relevant.
              </div>

              {/* Investment Amount Field */}
              <motion.div
                variants={formFieldVariants}
                initial="hidden"
                animate="visible"
                custom={0}
                className="space-y-2"
              >
                <label
                  htmlFor="investmentAmount"
                  className="block text-sm font-light"
                >
                  Investment Range <span className="text-accent">*</span>
                </label>
                <div
                  className={`relative ${errors.investmentAmount ? "animate-shake" : ""}`}
                >
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <FaDollarSign />
                  </div>
                  <select
                    id="investmentAmount"
                    name="investmentAmount"
                    value={formData.investmentAmount}
                    onChange={handleChange}
                    className={`w-full bg-navy-700/30 rounded-lg pl-10 pr-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 ${
                      errors.investmentAmount
                        ? "focus:ring-red-500 border border-red-500"
                        : "focus:ring-accent"
                    } transition-all font-light appearance-none`}
                  >
                    <option value="">Select investment range</option>
                    {investmentOptions.map((option, index) => (
                      <option key={index} value={option.value}>
                        {option.value} ({option.label})
                      </option>
                    ))}
                  </select>
                </div>
                {errors.investmentAmount && (
                  <p className="text-red-500 text-xs">
                    {errors.investmentAmount}
                  </p>
                )}
              </motion.div>

              {/* Message Field */}
              <motion.div
                variants={formFieldVariants}
                initial="hidden"
                animate="visible"
                custom={1}
                className="space-y-2"
              >
                <label htmlFor="message" className="block text-sm font-light">
                  Investment Goals & Questions{" "}
                  <span className="text-accent">*</span>
                </label>
                <div
                  className={`relative ${errors.message ? "animate-shake" : ""}`}
                >
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows="4"
                    className={`w-full bg-navy-700/30 rounded-lg px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 ${
                      errors.message
                        ? "focus:ring-red-500 border border-red-500"
                        : "focus:ring-accent"
                    } transition-all font-light resize-none`}
                    placeholder="Please share your investment goals, questions, or any specific areas of interest..."
                  />
                </div>
                {errors.message && (
                  <p className="text-red-500 text-xs">{errors.message}</p>
                )}
              </motion.div>

              {/* Document Upload */}
              <motion.div
                variants={formFieldVariants}
                initial="hidden"
                animate="visible"
                custom={2}
                className="space-y-2"
              >
                <label htmlFor="document" className="block text-sm font-light">
                  Additional Documents (Optional)
                </label>
                <div className="relative">
                  <div className="flex items-center">
                    <label className="flex-1 flex items-center gap-2 bg-navy-700/30 rounded-lg px-4 py-3 text-white cursor-pointer hover:bg-navy-700/50 transition-all">
                      <FaFileAlt className="text-gray-400" />
                      <span className="text-gray-400 font-light">
                        {formData.document
                          ? formData.document.name
                          : "Upload investment deck, portfolio, etc..."}
                      </span>
                      <input
                        type="file"
                        id="document"
                        name="document"
                        onChange={handleFileChange}
                        accept=".pdf,.doc,.docx,.ppt,.pptx"
                        className="hidden"
                      />
                    </label>
                  </div>
                </div>
                <p className="text-xs text-gray-500">
                  Accepted formats: PDF, DOC, DOCX, PPT, PPTX (Max 10MB)
                </p>
              </motion.div>

              {/* Investment Note */}
              <motion.div
                variants={formFieldVariants}
                initial="hidden"
                animate="visible"
                custom={3}
                className="bg-navy-700/30 rounded-lg p-4 flex items-start gap-3 mt-4"
              >
                <FaInfoCircle className="text-accent mt-1" />
                <div>
                  <p className="text-sm text-gray-300 font-light">
                    Our investment team will review your submission and contact
                    you within 48 hours to discuss next steps, including our
                    detailed investment deck, financial projections, and
                    potential partnership opportunities.
                  </p>
                </div>
              </motion.div>

              {/* Navigation Buttons */}
              <motion.div
                variants={formFieldVariants}
                initial="hidden"
                animate="visible"
                custom={4}
                className="flex justify-between mt-6"
              >
                <motion.button
                  title={"Back"}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  onClick={handlePrevStep}
                  className="bg-navy-700/50 text-white px-6 py-3 rounded-lg font-light hover:bg-navy-700/70 transition-all"
                >
                  Back
                </motion.button>

                <motion.button
                  title={"Submit Investment Interest"}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-accent to-accent-purple text-white px-6 py-3 rounded-lg font-light flex items-center gap-2 relative overflow-hidden group disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  <span>Submit Investment Interest</span>
                  {isSubmitting ? (
                    <svg
                      className="animate-spin h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  ) : (
                    <FaArrowRight className="group-hover:translate-x-1 transition-transform" />
                  )}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 -skew-x-12 transition-opacity duration-300" />
                </motion.button>
              </motion.div>
            </>
          )}
        </form>
      ) : (
        <div className="flex flex-col items-center justify-center py-8">
          <div className="w-16 h-16 bg-accent/20 rounded-full flex items-center justify-center mb-4">
            <FaCheck className="text-accent text-2xl" />
          </div>
          <h3 className="text-2xl font-light mb-2">
            Investment Interest Received!
          </h3>
          <p className="text-gray-400 text-center">
            Thank you for your interest in investing in BIELA. Our investment
            team will review your submission and contact you within 48 hours to
            discuss next steps.
          </p>
        </div>
      )}
    </Modal>
  );
}

export default InvestmentModal;
