import React, { useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaTimes } from "react-icons/fa";

function Modal({ isOpen, onClose, title, children, maxWidth = "max-w-md" }) {
  const modalRef = useRef(null);
  const bodyRef = useRef(document.body);

  // Handle body scroll locking
  useEffect(() => {
    const body = bodyRef.current;
    const originalStyle = window.getComputedStyle(body).overflow;

    let scrollY = 0;

    if (isOpen) {
      // Save current scroll pos
      scrollY = window.scrollY;
      // Apply fixed positioning to the body to prevent scrolling
      body.style.position = "fixed";
      body.style.top = `-${scrollY}px`;
      body.style.width = "100%";
      body.style.overflow = "hidden";
    }

    return () => {
      // restore original body styling
      body.style.position = "";
      body.style.top = "";
      body.style.width = "";
      body.style.overflow = originalStyle;

      // restore scroll position
      window.scrollTo({
        top: scrollY,
        behavior: "auto", // should make the scroll jump instant
      });
    };
  }, [isOpen]);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      // Prevent scrolling when modal is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      // Re-enable scrolling when modal is closed
      document.body.style.overflow = "auto";
    };
  }, [isOpen, onClose]);

  // Close modal when pressing Escape key
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscKey);
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [isOpen, onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="absolute inset-0 bg-navy-900/80 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            ref={modalRef}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className={`${maxWidth} w-full bg-gradient-to-br from-navy-800/90 to-navy-900/90 backdrop-blur-lg rounded-xl border border-white/10 shadow-xl relative z-10 overflow-hidden`}
          >
            {/* Modal Header */}
            <div className="flex items-center justify-between p-5 border-b border-white/10">
              <h3 className="text-xl font-light">{title}</h3>
              <button
                title={"Close " + title}
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-white/10"
              >
                <FaTimes />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-5">{children}</div>

            {/* Decorative Elements */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-accent/5 rounded-full blur-xl -z-10" />
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-accent-purple/5 rounded-full blur-xl -z-10" />
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}

export default Modal;
