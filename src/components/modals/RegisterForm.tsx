import React, { useEffect, useState } from "react";
import { useUser } from "../../lib/context/userContext";
import { signUp } from "../../lib/stores/user/user.ts";
import { UserSignupContextType } from "../../types/user";
import { motion } from "framer-motion";
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ye, FaEyeSlash, FaSpinner } from "react-icons/fa";
import { LuInfo } from "react-icons/lu";
import { FaCircleCheck } from "react-icons/fa6";
import { useTranslation } from "react-i18next";
import PhoneInputField from "../PhoneInputField.tsx";

const turnstileSiteKey = import.meta.env.VITE_TURNSTILE_SITE_KEY;

interface RegisterModalFormProps {
  onBack?: () => void;
}

const RegisterModalForm: React.FC<RegisterModalFormProps> = ({ onBack }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<UserSignupContextType>({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    acceptTerms: false,
  });
  const [error, setError] = useState<string>("");
  const [success, setSuccess] = useState<string>("");
  const { isLoggedIn } = useUser();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [fieldError, setFieldError] = useState<{
    username?: string;
    email?: string;
    password?: string;
    confirmPassword?: string;
    acceptTerms?: string;
  }>({});
  const [loading, setLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [captchaLoaded, setCaptchaLoaded] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      if (window.turnstile && document.getElementById("turnstile-widget")) {
        window.turnstile.render("#turnstile-widget", {
          sitekey: turnstileSiteKey,
          theme: "dark",
          appearance: "interaction-only",
          "before-interactive-callback": () => {
            document
              .getElementById("turnstile-widget")
              ?.classList.remove("hidden");
          },
          callback: () => {
            setCaptchaLoaded(true);
          },
        });
        clearInterval(interval);
      }
    }, 100);
    return () => clearInterval(interval);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const setPhoneValue = (name: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (loading) return;

    setLoading(true);
    setError("");
    setSuccess("");
    setFieldError({});

    const payload = {
      ...formData,
      email: formData.email.trim().toLowerCase(),
      username: formData.username.trim().toLowerCase(),
    };

    if (payload.password.trim() !== payload.confirmPassword.trim()) {
      setLoading(false);
      setError(t("PasswordsMismatch", "Passwords do not match."));

      return;
    }

    let hasValidationErrors = false;

    if (!payload.username?.trim()) {
      setFieldError((prev) => ({
        ...prev,
        username: t("UsernameRequired", "Username is required"),
      }));
      hasValidationErrors = true;
    }

    if (!payload.email?.trim()) {
      setFieldError((prev) => ({
        ...prev,
        email: t("EmailRequired", "Email is required"),
      }));
      hasValidationErrors = true;
    }
    if (!payload.phoneNumber?.trim()) {
      setFieldError((prev) => ({
        ...prev,
        phoneNumber: t("PhoneRequired", "Phone number is Required"),
      }));
      hasValidationErrors = true;
    }
    if (!payload.password?.trim()) {
      setFieldError((prev) => ({
        ...prev,
        password: t("PasswordRequired", "Password is Required"),
      }));
      hasValidationErrors = true;
    }

    if (!payload.confirmPassword?.trim()) {
      setFieldError((prev) => ({
        ...prev,
        confirmPassword: t(
          "ConfirmPasswordRequired",
          "Please confirm your password",
        ),
      }));
      hasValidationErrors = true;
    }

    if (!payload.acceptTerms) {
      setFieldError((prev) => ({
        ...prev,
        acceptTerms: t(
          "AcceptTermsRequired",
          "You must accept the Terms of Service and Privacy Policy",
        ),
      }));
      hasValidationErrors = true;
    }

    if (hasValidationErrors) {
      setLoading(false);
      return;
    }

    try {
      const token = document.querySelector<HTMLInputElement>(
        '[name="cf-turnstile-response"]',
      )?.value;

      if (!token) {
        throw new Error(t("CaptchaRequired", "Please complete the CAPTCHA"));
      }

      const response = await signUp(payload, token);
      const data = await response.json();

      if (!response.ok) {
        const errMsg =
          data.message || t("RegistrationFailed", "Registration failed");
        throw new Error(errMsg);
      }

      if (data) {
        setSuccess(
          t(
            "EmailConfirmationSent",
            "Email confirmation was sent. Please confirm your email and then log in.",
          ),
        );
        setIsSuccess(true);
        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
          event: "sign_up",
          method: "email",
        });
      } else {
        setError(
          t(
            "RegistrationServerError",
            "Registration failed (server returned false).",
          ),
        );
      }
    } catch (err: any) {
      setError(err.message || t("SomethingWentWrong", "Something went wrong"));

      if (window.turnstile && window.turnstile.reset) {
        window.turnstile.reset("#turnstile-widget");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col gap-8 items-center mx-auto w-full py-2 px-2">
      {isSuccess ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex flex-col items-center text-center py-8"
        >
          <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <FaCheck className="text-green-400 text-2xl" />
          </div>
          <h2 className="text-2xl font-light text-white mb-4">
            {t(
              "CheckEmailConfirmRegistration",
              "Check your email to confirm your registration",
            )}
          </h2>
          <p className="text-gray-400 font-light mb-4">
            {t(
              "EmailConfirmationSentText",
              "We've sent you an email with a confirmation link.",
            )}
          </p>
          <a
            onClick={onBack}
            className="w-fit px-20 cursor-pointer bg-[#1FC55C] text-white py-3 rounded-lg transition-colors font-extralight flex items-center justify-center hover:bg-green-600"
          >
            {t("LoginToProfile", "Login to profile")}
          </a>
        </motion.div>
      ) : (
        <>
          <form
            onSubmit={handleSubmit}
            className="space-y-6 max-2xl:space-y-3 w-full"
          >
            <div className="space-y-2">
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleChange}
                className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${fieldError.username ? "border-red-500 focus:ring-red-500" : "border-white/20"} transition-all`}
                placeholder={t("username", "Username")}
              />
              {fieldError.username && (
                <span className="text-red-400 text-sm">
                  {fieldError.username}
                </span>
              )}
            </div>
            <div className="space-y-2">
              <input
                type="text"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${fieldError.email ? "border-red-500 focus:ring-red-500" : "border-white/20"} transition-all`}
                placeholder={t("email", "Emailsss")}
              />
              {fieldError.email && (
                <span className="text-red-400 text-sm">{fieldError.email}</span>
              )}
            </div>
            <PhoneInputField
              label="Phone"
              value={formData.phoneNumber}
              onChange={(value) => setPhoneValue("phoneNumber", value)}
              countryCode={"US"}
              className="w-full"
              placeholder={true}
              error={fieldError.phoneNumber}
            />
            <div className="space-y-2 relative">
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleChange}
                className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${fieldError.password ? "border-red-500 focus:ring-red-500" : "border-white/20"} transition-all`}
                placeholder={t("PasswordPlaceholder", "Password")}
              />
              <button
                title={showPassword ? "Hide Password" : "Show Password"}
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="bg-transparent absolute right-3 top-2.5 my-auto flex items-center justify-center text-white/50 hover:text-white transition-colors"
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
              {fieldError.password && (
                <span className="text-red-400 text-sm">
                  {fieldError.password}
                </span>
              )}
            </div>
            <div className="space-y-2 relative">
              <input
                type={showConfirmPassword ? "text" : "password"}
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${fieldError.confirmPassword ? "border-red-500 focus:ring-red-500" : "border-white/20"} transition-all`}
                placeholder={t(
                  "ConfirmPasswordPlaceholder",
                  "Confirm Password",
                )}
              />
              <button
                title={
                  showConfirmPassword
                    ? "Hide Confirm Password"
                    : "Show Confirm Password"
                }
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="bg-transparent absolute right-3 top-2.5 my-auto flex items-center justify-center text-white/50 hover:text-white transition-colors"
              >
                {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
              {fieldError.confirmPassword && (
                <span className="text-red-400 text-sm">
                  {fieldError.confirmPassword}
                </span>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex items-center">
                <div className="relative flex items-center h-5">
                  <input
                    type="checkbox"
                    name="acceptTerms"
                    id="acceptTerms"
                    checked={formData.acceptTerms}
                    onChange={handleChange}
                    className="sr-only"
                  />
                  <div
                    onClick={() =>
                      setFormData((prev) => ({
                        ...prev,
                        acceptTerms: !prev.acceptTerms,
                      }))
                    }
                    className={`w-5 h-5 flex items-center justify-center rounded border transition-all cursor-pointer ${formData.acceptTerms ? "bg-green-500 border-green-500" : "bg-white/10 backdrop-blur-sm border-white/20"}`}
                  >
                    {formData.acceptTerms && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3.5 w-3.5 text-black"
                        viewBox="0 0 20 20"
                        fill="white"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>
                </div>
                <label
                  htmlFor="acceptTerms"
                  className="ml-2 text-white/70 text-sm cursor-pointer"
                >
                  {t("AcceptTermsPrefix", "I agree to the")}{" "}
                  <a
                    href="/terms/"
                    target="_blank"
                    className="text-green-400 hover:text-green-300 transition-colors"
                  >
                    {t("TermsOfService", "Terms of Service")}
                  </a>{" "}
                  {t("AndSeparator", "&")}{" "}
                  <a
                    href="/privacy/"
                    target="_blank"
                    className="text-green-400 hover:text-green-300 transition-colors"
                  >
                    {t("PrivacyPolicy", "Privacy Policy")}
                  </a>
                </label>
              </div>
              {fieldError.acceptTerms && (
                <span className="text-red-400 text-sm mt-1">
                  {fieldError.acceptTerms}
                </span>
              )}
            </div>
            <div id="turnstile-widget" className="hidden" />
            <button
              title={t("CreateAccount", "Create Account")}
              type="submit"
              className={`w-full ${captchaLoaded && !loading && !isLoggedIn() ? "bg-[#4ade80] text-black hover:bg-green-600 hover:text-white" : "bg-gray-500 opacity-70 cursor-not-allowed text-white"} cursor-pointer py-3 rounded-lg transition-colors relative flex items-center justify-center gap-2`}
              disabled={loading || isLoggedIn() || !captchaLoaded}
            >
              {loading ? (
                <FaSpinner className="animate-spin" />
              ) : !captchaLoaded ? (
                <div className="flex items-center gap-2">
                  <FaSpinner className="animate-spin" />
                  <span>
                    {captchaLoaded && !loading && !isLoggedIn()
                      ? t("loading", "Loading")
                      : t(
                          "checkingYourAuthenticity",
                          "Checking your authenticity",
                        )}
                    ...
                  </span>
                </div>
              ) : (
                t("CreateAccount", "Create Account")
              )}
            </button>
            {error && (
              <div className="flex items-center justify-center gap-2 rounded-lg p-3 text-red-400 bg-red-400/5">
                <LuInfo />
                <p>{error}</p>
              </div>
            )}
            {success && (
              <div className="flex items-center justify-center gap-2 rounded-lg p-3 text-green-400 bg-green-400/5">
                <FaCircleCheck />
                <p>{success}</p>
              </div>
            )}
          </form>
          <div className="flex items-center justify-between gap-2 w-full">
            <p className="text-white/60">
              <button
                title={t("Back", "Back")}
                type="button"
                className="text-white/60 hover:text-white transition-colors underline"
                onClick={onBack}
              >
                {t("Back", "Back")}
              </button>
            </p>
          </div>
        </>
      )}
    </div>
  );
};

export default RegisterModalForm;
