import React, { useState, useEffect } from "react";
import bielaText from "../../assets/text-logo.svg";
import { AnimatePresence, motion } from "framer-motion";
import { X } from "lucide-react";
import { useTranslation } from "react-i18next";
import { <PERSON>a<PERSON><PERSON><PERSON>, FaGithub, Fa<PERSON>pinner } from "react-icons/fa6";
import { handleGitHubAuth, handleGoogleAuth } from "../../lib/stores/user/user";
import { useNavigate } from "react-router-dom";
import { useUser } from "../../lib/context/userContext";
import { forgetPassword } from "../../lib/stores/user/user";
import RegisterForm from "./RegisterForm";

interface LoginModalProps {
  isOpen: boolean;
  isRegister?: boolean;
  onClose: () => void;
  redirectOnAffiliate?: boolean;
}

const turnstileSiteKey = import.meta.env.VITE_TURNSTILE_SITE_KEY;

const LoginModal: React.FC<LoginModalProps> = ({
  isOpen,
  onClose,
  isRegister,
  redirectOnAffiliate = false,
}) => {
  const { t } = useTranslation();
  const { login, isLoggedIn, user } = useUser();
  const navigate = useNavigate();

  // Handle body overflow when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      document.documentElement.classList.add("modal-open");
    } else {
      document.documentElement.classList.remove("modal-open");
    }

    return () => {
      document.documentElement.classList.remove("modal-open");
    };
  }, [isOpen]);

  // State for toggling between provider options and email/password form
  const [showEmailForm, setShowEmailForm] = useState(false);
  const [showRegisterForm, setShowRegisterForm] = useState(false);
  const [formData, setFormData] = useState({ username: "", password: "" });
  const [error, setError] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [loadingReset, setLoadingReset] = useState(false);
  const [fieldError, setFieldError] = useState<{
    username?: string;
    password?: string;
  }>({});
  const [showPassword, setShowPassword] = useState(false);
  const [success, setSuccess] = useState<string>("");
  const [captchaLoaded, setCaptchaLoaded] = useState(false);
  const [showResetForm, setShowResetForm] = useState(false);
  const [resetEmail, setResetEmail] = useState("");
  const [resetError, setResetError] = useState("");
  const [resetSuccess, setResetSuccess] = useState("");

  // Render Turnstile widget on modal open and showEmailForm
  useEffect(() => {
    setFieldError({});

    if (isOpen && (showEmailForm || showResetForm)) {
      const interval = setInterval(() => {
        const widget = document.getElementById("turnstile-widget");

        if (window.turnstile && widget) {
          widget.innerHTML = "";
          window.turnstile.render("#turnstile-widget", {
            sitekey: turnstileSiteKey,
            theme: "dark",
            appearance: "interaction-only",
            callback: () => setCaptchaLoaded(true),
          });
          clearInterval(interval);
        }
      }, 100);
      return () => clearInterval(interval);
    }

    return undefined;
  }, [isOpen, showEmailForm, showResetForm]);
  useEffect(() => {
    if (isRegister) {
      setShowRegisterForm(true);
      setShowEmailForm(false);
      setShowResetForm(false);
    } else {
      setShowRegisterForm(false);
      setShowEmailForm(false);
      setShowResetForm(false);
    }
  }, [isRegister]);

  // Auth handlers for external providers
  const preHandleGoogleAuth = () => {
    onClose();

    const state = crypto.randomUUID().toString();

    handleGoogleAuth(state);
  };

  const preHandleGitHubAuth = () => {
    onClose();
    handleGitHubAuth();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (loading || showResetForm) {
      return;
    }

    setLoading(true);
    setError("");
    setSuccess("");
    setFieldError({});

    let hasFieldError = false;

    if (!formData.username.trim()) {
      setFieldError((prev) => ({
        ...prev,
        username: t("UsernameRequired", "Username is required"),
      }));
      hasFieldError = true;
    }

    if (!formData.password.trim()) {
      setFieldError((prev) => ({
        ...prev,
        password: t("PasswordRequired", "Password is required"),
      }));
      hasFieldError = true;
    }

    // Get Turnstile token
    const token = document.querySelector<HTMLInputElement>(
      '[name="cf-turnstile-response"]',
    )?.value;

    if (!token) {
      setError(
        t(
          "MissingUsernamePasswordOrTurnstile",
          "Missing username, password or Turnstile token",
        ),
      );
      setLoading(false);

      return;
    }

    if (hasFieldError) {
      setLoading(false);
      return;
    }

    try {
      const userData = await login(
        {
          username: formData.username.trim(),
          password: formData.password.trim(),
        },
        token || "",
      );
      if (userData.refreshToken) {
        setSuccess(t("LoginSuccess", "Login successful!"));
        setTimeout(() => {
          onClose();
          if (redirectOnAffiliate) {
            window.location.href = `${import.meta.env.VITE_AFFILIATE_URL}`;
          } else {
            window.location.href = `${import.meta.env.VITE_IDE_URL}/?token=${userData.refreshToken}`;
          }
        }, 1200);
      }
    } catch (err: any) {
      setError(err.message || t("LoginFailed", "Login failed"));

      if (window.turnstile && window.turnstile.reset) {
        window.turnstile.reset("#turnstile-widget");
      }
    } finally {
      setLoading(false);
    }
  };

  const validateEmail = (email: string) => {
    const re = /\S+@\S+\.\S+/;
    return re.test(email);
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setResetError("");
    setResetSuccess("");

    if (!resetEmail.trim()) {
      setResetError(t("EmailRequired", "Email is required"));
      return;
    }

    if (!validateEmail(resetEmail)) {
      setError(t("EmailInvalid", "Please enter a valid email address"));
      return;
    }

    setLoadingReset(true);

    // Get Turnstile token
    const token = document.querySelector<HTMLInputElement>(
      '[name="cf-turnstile-response"]',
    )?.value;

    if (!token) {
      throw new Error(t("CaptchaRequired", "Please complete the CAPTCHA"));
    }

    try {
      const userData = await forgetPassword({ email: resetEmail }, token);

      if (userData.ok) {
        setResetSuccess(
          t("ResetLinkSent", "A reset link has been sent to your email."),
        );
      } else {
        setResetError(t("ResetFailed", "Failed to send reset link"));
      }
    } catch (err: any) {
      setResetError(
        err.message || t("ResetFailed", "Failed to send reset link"),
      );
    } finally {
      setLoadingReset(false);
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 ${isOpen ? "modal-open" : ""}`}
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="bg-[#11182B] border border-[#282828] max-w-[700px] w-full shadow-xl relative rounded-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        <X
          className="absolute top-6 right-6 md:top-8 md:right-8 cursor-pointer z-10 text-[#D5D5D5] hover:text-white transition-colors duration-300"
          onClick={onClose}
        />
        <div className="flex flex-col max-md:px-6 max-md:py-12 px-[7.5rem] py-20 max-2xl:py-12">
          <div className="flex flex-col items-center justify-center max-sm:min-w-[300px] min-w-[380px] mx-auto w-full">
            <div className="sticky top-0 flex flex-col items-center mb-14 max-2xl:mb-6 md:mt-0 mt-6">
              <img
                src={bielaText}
                alt="biela.dev"
                className="h-[24px] w-[220px]"
              />
            </div>
            <div className="flex flex-col gap-8 items-center w-full max-h-[600px] overflow-y-auto vertical-scroll">
              {!showEmailForm && !showRegisterForm && !showResetForm ? (
                <>
                  <p className="text-[14px] sm:text-[16px]">
                    {t(
                      "ToUseBielaDev",
                      "To use Biela.dev you must log into an existing account or create one using one of the options below",
                    )}
                  </p>
                  <div className="flex flex-col gap-4 items-center w-full">
                    <button
                      title={t("SignInWithGoogle", "Sign in with Google")}
                      onClick={preHandleGoogleAuth}
                      disabled={isLoggedIn()}
                      className="bg-[#282828] text-white hover:text-white cursor-pointer px-4 py-2 rounded-lg w-full flex items-center justify-center gap-3 hover:bg-white/20 transition-colors"
                    >
                      <FaGoogle className="w-4 h-4" />
                      <span>
                        {t("SignInWithGoogle", "Sign in with Google")}
                      </span>
                    </button>
                    <button
                      title={t("SignInWithGitHub", "Sign in with GitHub")}
                      onClick={preHandleGitHubAuth}
                      disabled={isLoggedIn()}
                      className="bg-[#282828] text-white  cursor-pointer px-4 py-2 rounded-lg w-full flex items-center justify-center gap-3  hover:bg-white/20  transition-colors"
                    >
                      <FaGithub className="w-4 h-4" />
                      <span>
                        {t("SignInWithGitHub", "Sign in with GitHub")}
                      </span>
                    </button>
                    <button
                      title={t(
                        "SignInWithEmailAndPassword",
                        "Sign in with Email and Password",
                      )}
                      className=" bg-[#4ade80] text-black  hover:text-white px-4 py-2 rounded-lg cursor-pointer hover:bg-green-600 w-full transition-colors"
                      onClick={() => setShowEmailForm(true)}
                    >
                      {t(
                        "SignInWithEmailAndPassword",
                        "Sign in with Email and Password",
                      )}
                    </button>
                  </div>
                  <a
                    href="#"
                    className="text-md text-[#D5D5D5] hover:underline cursor-pointer hover:text-white transition-colors"
                    onClick={() => {
                      setShowRegisterForm(true);
                      setShowEmailForm(false);
                      setShowResetForm(false);
                    }}
                  >
                    {t("DontHaveAnAccount", "Don't have an account?")}{" "}
                    <span className="text-[#4ade80]">
                      {t("SignUp", "Sign me up")}
                    </span>
                  </a>
                  <p className="text-md">
                    {t(
                      "ByUsingBielaDev",
                      "By using Biela.dev you consent to usage data collection.",
                    )}
                  </p>
                </>
              ) : showResetForm ? (
                <form
                  onSubmit={handleResetPassword}
                  className="flex flex-col gap-4 px-2 py-2 w-full items-start"
                >
                  <input
                    type="email"
                    name="resetEmail"
                    value={resetEmail}
                    onChange={(e) => setResetEmail(e.target.value)}
                    className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${fieldError.firstName ? "border-red-500 focus:ring-red-500" : "border-white/20"} transition-all`}
                    placeholder={t("EmailPlaceholder", "Your email")}
                  />
                  <div
                    id="turnstile-widget"
                    className={`transition-opacity duration-300 h-0 opacity-0`}
                  />
                  <button
                    title={"Submit"}
                    type="submit"
                    className="w-full bg-[#4ade80] hover:bg-[#1FC55C] text-black cursor-pointer hover:text-white py-3 rounded-lg transition-colors font-extralight flex items-center justify-center hover:bg-green-600"
                  >
                    {loadingReset ? (
                      <FaSpinner className="animate-spin" />
                    ) : !captchaLoaded ? (
                      <div className="flex items-center gap-2">
                        <FaSpinner className="animate-spin" />
                        <span>
                          {!captchaLoaded && !loadingReset && !isLoggedIn()
                            ? t(
                                "checkingYourAuthenticity",
                                "Checking your authenticity",
                              )
                            : t(
                                "checkingYourAuthenticity",
                                "Checking your authenticity",
                              )}
                          ...
                        </span>
                      </div>
                    ) : (
                      t("SendResetLink", "Send reset link")
                    )}
                  </button>
                  {resetError && (
                    <div className="flex items-center gap-2 rounded-lg p-3 text-red-400 bg-red-400/5 w-full">
                      <span>{resetError}</span>
                    </div>
                  )}
                  {resetSuccess && (
                    <div className="flex items-center justify-center gap-2 rounded-lg p-3 text-green-400 bg-green-400/5 w-full">
                      <span>{resetSuccess}</span>
                    </div>
                  )}
                  <button
                    title={t("BackToLogin", "Back to login")}
                    type="button"
                    className="text-md text-[#D5D5D5] hover:underline mt-6 px-2 py-2"
                    onClick={(e) => {
                      e.preventDefault();

                      setShowResetForm(false);
                      setError("");
                      setSuccess("");
                      setResetEmail("");
                      setResetError("");
                      setResetSuccess("");
                    }}
                  >
                    {t("BackToLogin", "Back to login")}
                  </button>
                </form>
              ) : showEmailForm ? (
                <>
                  <form
                    onSubmit={handleSubmit}
                    className="flex flex-col gap-4 px-2 py-2 w-full"
                  >
                    <input
                      type="text"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${fieldError.username ? "border-red-500 focus:ring-red-500" : "border-white/20"} transition-all`}
                      placeholder={t(
                        "EmailOrUsernamePlaceholder",
                        "Email/Username",
                      )}
                    />
                    {fieldError.username && (
                      <span className="text-red-400 text-md">
                        {fieldError.username}
                      </span>
                    )}
                    <div className="relative w-full">
                      <input
                        type={showPassword ? "text" : "password"}
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${fieldError.password ? "border-red-500 focus:ring-red-500" : "border-white/20"} transition-all`}
                        placeholder={t("passwordPlaceholder", "Password")}
                      />
                      <button
                        title={showPassword ? "Hide Password" : "Show Password"}
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="bg-transparent absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center text-white/50 hover:text-white transition-colors"
                      >
                        {showPassword ? (
                          <svg
                            width="18"
                            height="18"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            viewBox="0 0 24 24"
                          >
                            <path d="M1 1l22 22M17.94 17.94A10.94 10.94 0 0 1 12 19C7.03 19 2.73 15.11 1 12c.74-1.32 2.1-3.31 4.06-5.06M9.5 9.5a3 3 0 0 1 4.5 4.5" />
                            <path d="M12 5c3.31 0 6.31 2.16 8.06 5.06a11.08 11.08 0 0 1-1.57 2.11" />
                          </svg>
                        ) : (
                          <svg
                            width="18"
                            height="18"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            viewBox="0 0 24 24"
                          >
                            <circle cx="12" cy="12" r="3" />
                            <path d="M2.05 12.81A10.94 10.94 0 0 1 12 5c4.97 0 9.27 3.89 11 7-1.73 3.11-6.03 7-11 7a10.94 10.94 0 0 1-9.95-6.19z" />
                          </svg>
                        )}
                      </button>
                    </div>
                    {fieldError.password && (
                      <span className="text-red-400 text-md">
                        {fieldError.password}
                      </span>
                    )}
                    <div
                      id="turnstile-widget"
                      className={`transition-opacity duration-300 h-0 opacity-0`}
                    />
                    <button
                      title={t("LoginInToProfile", "Log In To Your Profile")}
                      type="submit"
                      className={`w-full bg-[#4ade80] hover:bg-[#1FC55C] text-black hover:text-white py-3 rounded-lg transition-colors flex items-center justify-center gap-2 cursor-pointer ${loading ? "opacity-70 cursor-not-allowed" : ""}`}
                    >
                      {loading ? (
                        <FaSpinner className="animate-spin" />
                      ) : !captchaLoaded ? (
                        <div className="flex items-center gap-2">
                          <FaSpinner className="animate-spin" />
                          <span>
                            {!captchaLoaded && !loading && !isLoggedIn()
                              ? t(
                                  "checkingYourAuthenticity",
                                  "Checking your authenticity",
                                )
                              : t(
                                  "checkingYourAuthenticity",
                                  "Checking your authenticity",
                                )}
                            ...
                          </span>
                        </div>
                      ) : (
                        t("LoginInToProfile", "Log In To Your Profile")
                      )}
                    </button>
                    {error && (
                      <div className="flex items-center gap-2 rounded-lg p-3 text-red-400 bg-red-400/5">
                        <span>{error}</span>
                      </div>
                    )}
                    {success && (
                      <div className="flex items-center justify-center gap-2 rounded-lg p-3 text-green-400 bg-green-400/5">
                        <span>{success}</span>
                      </div>
                    )}
                  </form>
                  <div className="flex gap-2 justify-between w-full px-2 py-2">
                    <button
                      title={t("Back", "Back")}
                      type="button"
                      className="text-md text-[#D5D5D5] hover:underline mt-2 self-end"
                      onClick={(e) => {
                        e.preventDefault();

                        setShowEmailForm(false);
                        setFieldError({});
                        setError("");
                        setSuccess("");
                      }}
                    >
                      {t("Back", "Back")}
                    </button>
                    <button
                      title={t("forgotPassword", "Forgot password?")}
                      type="button"
                      className="text-md text-[#D5D5D5] hover:underline mt-2 self-end"
                      onClick={() => {
                        setShowResetForm(true);
                        setResetEmail("");
                        setResetError("");
                        setResetSuccess("");
                      }}
                    >
                      {t("forgotPassword", "Forgot password?")}
                    </button>
                  </div>
                </>
              ) : showRegisterForm ? (
                <AnimatePresence>
                  <RegisterForm
                    onBack={() => {
                      setShowEmailForm(false);
                      setShowRegisterForm(false);
                      setShowResetForm(false);
                    }}
                    onNext={() => {
                      setShowEmailForm(true);
                      setShowRegisterForm(false);
                      setShowResetForm(false);
                    }}
                  />
                </AnimatePresence>
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default LoginModal;
