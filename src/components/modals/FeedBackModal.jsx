import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { X, Bug, Plus, Send, Upload, Trash2 } from "lucide-react";
import { motion } from "framer-motion";

const BASE_URL = import.meta.env.VITE_API_URL;

function FeedbackModal({ isOpen, onClose, type }) {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    email: "",
    fullName: "",
    suggestion: "",
  });
  const [errors, setErrors] = useState({});
  const [images, setImages] = useState([]);
  const [dragActive, setDragActive] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate full name
    if (!formData.fullName.trim()) {
      newErrors.fullName = t("feedback.errors.fullNameRequired");
    } else if (/\d/.test(formData.fullName)) {
      // Check if name contains any numbers
      newErrors.fullName = t("feedback.errors.fullNameNoNumbers");
    }

    // Validate email
    if (!formData.email.trim()) {
      newErrors.email = t("feedback.errors.emailRequired");
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = t("feedback.errors.emailInvalid");
    }

    // Validate suggestion
    if (!formData.suggestion.trim()) {
      newErrors.suggestion = t("feedback.errors.suggestionRequired");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter((file) => file.type.startsWith("image/"));
    handleFiles(imageFiles);
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    handleFiles(files);
  };

  const handleFiles = (files) => {
    files.forEach((file) => {
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const newImage = {
            id: Date.now() + Math.random(),
            file: file,
            preview: e.target.result,
            name: file.name,
            url: e.target.result, // adaugă url base64 aici
            size: file.size,
          };
          setImages((prev) => [...prev, newImage]);
        };
        reader.readAsDataURL(file); // citește ca base64
      }
    });
  };

  const removeImage = (imageId) => {
    setImages((prev) => prev.filter((img) => img.id !== imageId));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Custom validation instead of browser default
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const body = JSON.stringify({
        type,
        ...formData,
        images: images.map((img) => ({
          name: img.name,
          size: img.size,
          url: img.url,
        })),
      });
      const response = await fetch(`${BASE_URL}/suggestions-issues`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: body,
        credentials: "include",
      });
      const data = await response.json();
      console.log(data, "data");
      if (data.success) {
        setIsSubmitted(true);
        setIsSubmitting(false);

        // Reset form after 2 seconds and close modal
        setTimeout(() => {
          setFormData({ email: "", fullName: "", suggestion: "" });
          setImages([]);
          setErrors({});
          setIsSubmitted(false);
          onClose();
        }, 2000);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const resetModal = () => {
    setFormData({ email: "", fullName: "", suggestion: "" });
    setImages([]);
    setErrors({});
    setIsSubmitted(false);
    setIsSubmitting(false);
    setDragActive(false);
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  if (!isOpen) return null;

  const isReportIssue = type === "issue";
  const title = isReportIssue
    ? t("feedback.title.issue")
    : t("feedback.title.feature");
  const description = isReportIssue
    ? t("feedback.description.issue")
    : t("feedback.description.feature");
  const icon = isReportIssue ? Bug : Plus;
  const IconComponent = icon;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={handleClose}
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
    >
      {/* Modal */}
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        onClick={(e) => e.stopPropagation()}
        className="relative w-full max-w-md mx-auto max-h-[90vh] overflow-y-auto"
      >
        <div className="bg-[#11182B] border border-[#282828] rounded-2xl p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div
                className={`p-2 rounded-lg ${isReportIssue ? "bg-orange-400/20 text-orange-400" : "bg-green-600/20 text-green-600"}`}
              >
                <IconComponent className="w-5 h-5" />
              </div>
              <h2
                className="text-sm md:text-xl manrope-light font-light"
                style={{ color: "rgb(212, 212, 212)" }}
              >
                {title}
              </h2>
            </div>
            <button
              title={"Close " + title}
              onClick={handleClose}
              className="p-1 text-gray-400 hover:text-white transition-colors duration-200 cursor-pointer"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Description */}
          <p className="text-gray-100 leading-relaxed drop-shadow-sm font-light mb-6">
            {description}
          </p>

          {/* Success Message */}
          {isSubmitted ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Send className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">
                {t("feedback.success.title")}
              </h3>
              <p className="text-gray-200 text-sm font-light">
                {isReportIssue
                  ? t("feedback.success.issue")
                  : t("feedback.success.feature")}
              </p>
            </div>
          ) : (
            /* Form */
            <form onSubmit={handleSubmit} className="space-y-4" noValidate>
              {/* Full Name */}
              <div>
                <label
                  htmlFor="fullName"
                  className={`block text-sm font-medium mb-2 ${
                    errors.fullName ? "text-red-400" : "text-gray-200"
                  }`}
                >
                  {t("feedback.form.fullName.label")}
                </label>
                <input
                  type="text"
                  id="fullName"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 bg-white/10 border rounded-lg placeholder-gray-400 focus:outline-none transition-all duration-200 ${
                    errors.fullName
                      ? "border-red-400 text-red-400 focus:ring-red-400/50 focus:border-red-400"
                      : `border-white/20 text-white ${type === "issue" ? "focus:ring-orange-400/50 focus:border-orange-400/50" : "focus:ring-[#4ade80] focus:border-[#4ade80]"}`
                  }`}
                  placeholder={t("feedback.form.fullName.placeholder")}
                />
                {errors.fullName && (
                  <p className="mt-1 text-sm text-red-400">{errors.fullName}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <label
                  htmlFor="email"
                  className={`block text-sm font-medium mb-2 ${
                    errors.email ? "text-red-400" : "text-gray-200"
                  }`}
                >
                  {t("feedback.form.email.label")}
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 bg-white/10 border rounded-lg placeholder-gray-400 focus:outline-none  transition-all duration-200 ${
                    errors.email
                      ? "border-red-400 text-red-400 focus:ring-red-400/50 focus:border-red-400"
                      : `border-white/20 text-white ${type === "issue" ? "focus:ring-orange-400/50 focus:border-orange-400/50" : "focus:ring-[#4ade80] focus:border-[#4ade80]"}`
                  }`}
                  placeholder={t("feedback.form.email.placeholder")}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-400">{errors.email}</p>
                )}
              </div>

              {/* Issue Description */}
              <div>
                <label
                  htmlFor="suggestion"
                  className={`block text-sm font-medium mb-2 ${
                    errors.suggestion ? "text-red-400" : "text-gray-200"
                  }`}
                >
                  {isReportIssue
                    ? t("feedback.form.suggestion.labelIssue")
                    : t("feedback.form.suggestion.labelFeature")}
                </label>
                <textarea
                  id="suggestion"
                  name="suggestion"
                  value={formData.suggestion}
                  onChange={handleInputChange}
                  rows={4}
                  className={`w-full px-3 py-2 bg-white/10 border rounded-lg placeholder-gray-400 focus:outline-none  transition-all duration-200 resize-none ${
                    errors.suggestion
                      ? "border-red-400 text-red-400 focus:ring-red-400/50 focus:border-red-400"
                      : `border-white/20 text-white ${type === "issue" ? "focus:ring-orange-400/50 focus:border-orange-400/50" : "focus:ring-[#4ade80] focus:border-[#4ade80]"}`
                  }`}
                  placeholder={
                    isReportIssue
                      ? t("feedback.form.suggestion.placeholderIssue")
                      : t("feedback.form.suggestion.placeholderFeature")
                  }
                />
                {errors.suggestion && (
                  <p className="mt-1 text-sm text-red-400">
                    {errors.suggestion}
                  </p>
                )}
              </div>

              {/* Image Upload - Only for Report Issue */}
              {isReportIssue && (
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    {t("feedback.form.screenshots.label")}
                  </label>
                  <p className="text-xs text-gray-400 mb-3">
                    {t("feedback.form.screenshots.note")}
                  </p>

                  {/* Drop Zone */}
                  <div
                    className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 cursor-pointer ${
                      dragActive
                        ? "border-orange-400 bg-orange-400/10"
                        : "border-white/30 bg-white/5 hover:border-orange-400/50 hover:bg-white/10"
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                    onClick={() => document.getElementById("fileInput").click()}
                  >
                    <input
                      id="fileInput"
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleFileSelect}
                      className="hidden"
                    />

                    <div className="flex flex-col items-center">
                      <Upload className="w-8 h-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-300 mb-1">
                        {t("feedback.form.screenshots.drop")}
                      </p>
                      <p className="text-xs text-gray-400">
                        {t("feedback.form.screenshots.hint")}
                      </p>
                    </div>
                  </div>

                  {/* Image Previews */}
                  {images.length > 0 && (
                    <div className="mt-4 space-y-2">
                      <p className="text-xs text-gray-400">
                        {t("feedback.form.screenshots.count", {
                          count: images.length,
                        })}
                      </p>
                      <div className="grid grid-cols-2 gap-2">
                        {images.map((image) => (
                          <div key={image.id} className="relative group">
                            <div className="aspect-square rounded-lg overflow-hidden bg-white/10 border border-white/20">
                              <img
                                src={image.preview}
                                alt={image.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <button
                              title={"Remove Image"}
                              type="button"
                              onClick={() => removeImage(image.id)}
                              className="absolute cursor-pointer -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                            >
                              <Trash2 className="w-3 h-3 text-white" />
                            </button>
                            <p className="text-xs text-gray-400 mt-1 truncate">
                              {image.name}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Submit Button */}
              <div className="flex gap-3 pt-2">
                <button
                  title={t("feedback.buttons.cancel")}
                  type="button"
                  onClick={handleClose}
                  className="flex-1 cursor-pointer px-4 py-2 text-gray-300 hover:text-white transition-colors duration-200 font-medium"
                >
                  {t("feedback.buttons.cancel")}
                </button>
                <button
                  tabIndex={"Submit"}
                  title={"Submit"}
                  type="submit"
                  disabled={isSubmitting}
                  className={`flex-1 px-4 ${!isSubmitting && "cursor-pointer"} py-2 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
                    isReportIssue
                      ? "bg-orange-400 hover:bg-orange-500 text-white"
                      : "bg-[#4ADE80] text-black hover:bg-green-700 hover:text-white"
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      {t("feedback.buttons.submitting")}
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4" />
                      {isReportIssue
                        ? t("feedback.buttons.submitIssue")
                        : t("feedback.buttons.submitFeature")}
                    </>
                  )}
                </button>
              </div>
            </form>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
}

export default FeedbackModal;
