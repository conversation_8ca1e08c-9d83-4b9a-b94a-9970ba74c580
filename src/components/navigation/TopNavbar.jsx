import React, { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { useLocation } from "react-router-dom";
import { FaBars, FaTimes, FaUserAlt } from "react-icons/fa";
import {
  <PERSON>a<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaRedditAlien,
} from "react-icons/fa6";

import { HashLink } from "react-router-hash-link";
import { useTranslation } from "react-i18next";
import bielaLogo from "../../assets/biela-logo.svg";
import bielaText from "../../assets/text-logo.svg";
import LanguageSwitcher from "../LanguageSwitcher";
import { Link } from "react-router-dom";
import { ShortEvents } from "../short-events.js";
import { AnimatePresence } from "framer-motion";
import LoginModal from "../modals/LoginModal.tsx";

function TopNavbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState("hero-section");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const mobileMenuRef = useRef(null);
  const { setIsUser, isUser } = ShortEvents();
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);

  const getInformation = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/user`, {
        method: "GET",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
      });
      const data = await response.json();
      if (response.ok) {
        setIsUser(data);
      }
    } catch (error) {
      setIsUser(undefined);
      console.log(error);
    }
  };
  useEffect(() => {
    getInformation();
  }, []);

  const isLoggedInUser = () => {
    return isUser;
  };

  // Updated navigation items to include only visible sections
  const navItems = [
    { label: t("navbar.community"), href: "https://t.me/teachmecode" },
    { label: t("navbar.partners"), href: "#partners" }, // This will link to Strategic Partnerships
    { label: t("navbar.pricing"), href: "#pricing" },

    // { label: t("navbar.contest"), href: "/hackathon" },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);

      if (location.pathname === "/") {
        const sections = document.querySelectorAll("section[id]");
        sections.forEach((section) => {
          const sectionTop = section.offsetTop - 100;
          const sectionHeight = section.offsetHeight;
          if (
            window.scrollY >= sectionTop &&
            window.scrollY < sectionTop + sectionHeight
          ) {
            setActiveSection(section.id);
          }
        });
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [location]);

  // Close mobile menu when changing location
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location.pathname]);

  // Close mobile menu when clicking outside of it
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        mobileMenuOpen &&
        mobileMenuRef.current &&
        !mobileMenuRef.current.contains(event.target)
      ) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [mobileMenuOpen]);

  const scrollToSection = (e, href) => {
    e.preventDefault();

    const element = document.querySelector(href);
    const isOnHome = location.pathname === "/";

    if (!isOnHome) {
      window.location.href = `/${i18n.language}/${href}`;
      return;
    }

    if (element) {
      const offset = 80;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - offset;

      setMobileMenuOpen(false);
      setTimeout(() => {
        window.scrollTo({
          top: offsetPosition,
          behavior: "smooth",
        });
      }, 100);
    } else {
      setMobileMenuOpen(false);
    }
  };

  useEffect(() => {
    const hash = window.location.hash;

    if (hash) {
      let attempts = 0;
      const maxAttempts = 10;

      const scrollToHashElement = () => {
        const element = document.querySelector(hash);
        if (element) {
          const offset = 80;
          const elementPosition = element.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.pageYOffset - offset;
          window.scrollTo({ top: offsetPosition, behavior: "smooth" });
        } else if (attempts < maxAttempts) {
          attempts++;
          setTimeout(scrollToHashElement, 200);
        }
      };

      scrollToHashElement();
    }
  }, []);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 bg-[#0A1730]`}
    >
      <div
        className="container mx-auto px-[70px] max-sm:!pl-[40px] relative max-sm:px-[25px]"
        style={{ paddingTop: "0", paddingBottom: "0" }}
      >
        <div className="flex items-center justify-between">
          {/* Logo */}
          <HashLink
            smooth
            to="/"
            className="flex items-center gap-2 sm:gap-3 group"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              style={{
                transform: "none",
                height: "32px",
                minWidth: "40px",
              }}
              className="relative"
            >
              <img
                src={bielaLogo}
                alt="BIELA"
                style={{ position: "absolute", right: 0, top: -7 }}
                className="h-[80px] min-w-[80px] max-sm:min-w-[70px] max-sm:h-[70px] z-50"
              />
              <div className="absolute inset-0 bg-accent/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300" />
            </motion.div>
            <img
              src={bielaText}
              alt="biela.dev"
              className="h-[60px] xl:h-[80px] w-[143px] max-sm:w-[100px]"
            />
          </HashLink>

          {/* Mobile Menu Button */}
          <div className="xl:hidden flex items-center gap-2">
            <LanguageSwitcher />
            <button
              title={mobileMenuOpen ? "Close menu" : "Open menu"}
              onClick={toggleMobileMenu}
              className="p-2 text-white focus:outline-none cursor-pointer"
              aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
            >
              {mobileMenuOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
            </button>
          </div>

          {/* Desktop Navigation Links */}
          <div className="hidden xl:flex items-center gap-8">
            {navItems.map((item, index) => {
              const isAnchor = item.href.startsWith("#");
              const currentPath =
                location.pathname.replace(`/${i18n.language}`, "") || "/";
              const currentHash = location.hash;
              let isActive = isAnchor
                ? currentPath === "/" && currentHash === item.href
                : location.pathname === item.href;
              return isAnchor ? (
                <a
                  key={index}
                  href={item.href}
                  onClick={(e) => scrollToSection(e, item.href)}
                  className={`relative text-base font-light transition-all duration-300 text-[#F5F5F5]`}
                >
                  {item.label}
                </a>
              ) : (
                <Link
                  key={index}
                  target={"_blank"}
                  to={item.href}
                  className={`relative text-base font-light transition-all duration-300 ${
                    isActive ? "text-[#22C55E]" : "text-[#F5F5F5]"
                  }`}
                >
                  {item.label}
                  {isActive && (
                    <span
                      style={{
                        background:
                          "linear-gradient(90deg, rgba(19, 35, 68, 0.00) 0%, #22C55E 50.5%, rgba(19, 35, 68, 0.00) 100%)",
                      }}
                      className="absolute top-[50px] left-1/2 transform -translate-x-1/2 w-[130px] h-[2px] rounded-full"
                    />
                  )}
                </Link>
              );
            })}
          </div>

          {/* Auth Buttons & Language Switcher - Desktop */}
          <div className="hidden xl:flex items-center gap-2 sm:gap-4">
            <LanguageSwitcher />

            <div className="flex items-center justify-between gap-2 sm:gap-3">
              <div className="flex items-center gap-2 sm:gap-3">
                <a
                  href="https://x.com/DevBiela"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="X"
                >
                  <FaXTwitter className="h-[17px] w-[18px] text-[#A3A3A3] hover:text-white transition-colors" />
                </a>
                <a
                  href="https://discord.gg/A9VryKC8TQ"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Discord"
                >
                  <FaDiscord className="h-[17px] w-[17px] text-[#A3A3A3] hover:text-white transition-colors" />
                </a>
                <a
                  href="https://www.linkedin.com/company/bieladev"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="LinkedIn"
                >
                  <FaLinkedin className="h-[14] w-[18px] text-[#A3A3A3] hover:text-white transition-colors" />
                </a>
                <a
                  href="https://www.reddit.com/r/Bieladev/"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Reddit"
                  className="transition-colors bg-[#A3A3A3] hover:bg-white rounded-full w-[20px] h-[20px] flex items-center justify-center"
                >
                  <FaRedditAlien className="h-[14px] w-[14px] text-[#0A1730] transition-colors" />
                </a>
              </div>
              {isLoggedInUser() ? (
                <Link to={`${import.meta.env.VITE_IDE_URL + "/dashboard"}`}>
                  <p className="text-sm px-4 py-2 cursor-pointer rounded-md relative text-white bg-[#4ADE80] flex gap-2 items-center hover:bg-green-600 transition-colors">
                    <FaUserAlt />
                    My Account
                  </p>
                </Link>
              ) : (
                <>
                  <button
                    title={t("SignIn", "Sign In")}
                    onClick={() => {
                      setIsLoginModalOpen(true);
                      setIsRegisterModalOpen(false);
                    }}
                    className="text-sm px-4 py-2 cursor-pointer rounded-md relative text-white bg-[#19243b] flex gap-2 items-center hover:bg-white/20 transition-colors"
                  >
                    <span className="text-xs lg:text-[14px] font-normal tracking-[0.4px] text-nowrap">
                      {t("SignIn", "Sign In")}
                    </span>
                  </button>
                  <button
                    title={t("Register", "Register")}
                    className="text-sm px-4 py-2 cursor-pointer rounded-md relative text-black hover:text-white bg-[#4ADE80] flex gap-2 items-center hover:bg-green-600 transition-colors"
                    onClick={() => {
                      setIsLoginModalOpen(true);
                      setIsRegisterModalOpen(true);
                    }}
                  >
                    <span className="text-xs lg:text-[14px] font-normal tracking-[0.4px] text-nowrap">
                      {t("Register", "Register")}
                    </span>
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <motion.div
        ref={mobileMenuRef}
        initial={{ opacity: 0, height: 0 }}
        animate={{
          opacity: mobileMenuOpen ? 1 : 0,
          height: mobileMenuOpen ? "auto" : 0,
        }}
        transition={{ duration: 0.3 }}
        className="xl:hidden overflow-hidden"
      >
        {mobileMenuOpen && (
          <div className="container mx-auto px-6 py-4">
            <div className="flex flex-col space-y-4">
              {navItems.map((item, index) => {
                const isAnchor = item.href.startsWith("#");
                const currentPath =
                  location.pathname.replace(`/${i18n.language}`, "") || "/";
                const currentHash = location.hash;
                const isActive = isAnchor
                  ? currentPath === "/" && currentHash === item.href
                  : location.pathname === item.href;

                return isAnchor ? (
                  <a
                    key={index}
                    href={item.href}
                    onClick={(e) => scrollToSection(e, item.href)}
                    className={`text-lg py-2 border-b border-white/10 ${
                      isActive ? "text-accent" : "text-white/80"
                    }`}
                  >
                    {item.label}
                  </a>
                ) : (
                  <Link
                    key={index}
                    to={item.href}
                    className={`text-lg py-2 border-b border-white/10 ${
                      isActive ? "text-accent" : "text-white/80"
                    }`}
                  >
                    {item.label}
                  </Link>
                );
              })}

              {/* Auth Buttons - Mobile */}
              <div className="flex flex-col space-y-3 pt-4">
                {isLoggedInUser() ? (
                  <Link to={`${import.meta.env.VITE_IDE_URL + "/dashboard/"}`}>
                    <p className="text-sm px-4 py-2 cursor-pointer rounded-md relative text-white bg-[#4ADE80] flex gap-2 items-center justify-center hover:bg-green-600 transition-colors">
                      <FaUserAlt />
                      My Account
                    </p>
                  </Link>
                ) : (
                  <>
                    <button
                      title={t("SignIn", "Sign In")}
                      onClick={() => {
                        setIsLoginModalOpen(true);
                        setIsRegisterModalOpen(false);
                      }}
                      className="text-sm px-4 py-2 cursor-pointer rounded-md relative text-white bg-[#19243b] flex justify-center gap-2 items-center hover:bg-white/20 transition-colors"
                    >
                      {t("SignIn", "Sign In")}
                    </button>
                    <button
                      title={t("Register", "Register")}
                      onClick={() => {
                        setIsLoginModalOpen(true);
                        setIsRegisterModalOpen(true);
                      }}
                      className="text-sm px-4 py-2 cursor-pointer rounded-md relative text-black hover:text-white bg-[#1FC55C] flex justify-center gap-2 items-center hover:bg-green-600 transition-colors"
                    >
                      {t("Register", "Register")}
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </motion.div>

      {isLoginModalOpen && (
        <AnimatePresence>
          <LoginModal
            isOpen={isLoginModalOpen}
            onClose={() => setIsLoginModalOpen?.(false)}
            isRegister={isRegisterModalOpen}
          />
        </AnimatePresence>
      )}
    </motion.nav>
  );
}

export default TopNavbar;
