import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { FaDiscord, FaGithub } from "react-icons/fa";
import bielaLogo from "../../assets/biela-logo.svg";

function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState("hero");

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 50);

      // Update active section based on scroll position
      const sections = document.querySelectorAll("section[id]");
      sections.forEach((section) => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;
        if (
          scrollPosition >= sectionTop &&
          scrollPosition < sectionTop + sectionHeight
        ) {
          setActiveSection(section.id);
        }
      });
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <motion.nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? " backdrop-blur-lg" : "bg-transparent"
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-3 group">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="relative"
            >
              <img src={bielaLogo} alt="BIELA" className="h-10 w-10" />
              <div className="absolute inset-0 bg-biela-400/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300" />
            </motion.div>
            <span className="text-2xl font-light gradient-text">BIELA.DEV</span>
          </Link>

          {/* Navigation Links */}
          <div className="hidden lg:flex items-center gap-12">
            <a
              href="#about"
              className={`nav-link ${activeSection === "about" ? "active" : ""}`}
            >
              About Us
            </a>
            <a
              href="#product"
              className={`nav-link ${activeSection === "product" ? "active" : ""}`}
            >
              Product
            </a>
            <a
              href="#deployment"
              className={`nav-link ${activeSection === "deployment" ? "active" : ""}`}
            >
              Deployment
            </a>
            <a
              href="#contact"
              className={`nav-link ${activeSection === "contact" ? "active" : ""}`}
            >
              Contact
            </a>
          </div>

          {/* CTA Buttons */}
          <div className="flex items-center gap-4">
            <motion.a
              href="https://discord.gg/biela"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-400 hover:text-biela-400 transition-colors"
            >
              <FaDiscord className="text-xl" />
            </motion.a>
            <motion.a
              href="https://github.com/biela"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-400 hover:text-biela-400 transition-colors"
            >
              <FaGithub className="text-xl" />
            </motion.a>
            <motion.button
              title={"Start Coding"}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary"
            >
              Start Coding
            </motion.button>
          </div>
        </div>
      </div>

      {/* Bottom border with glow */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-biela-500/20 to-transparent" />
    </motion.nav>
  );
}

export default Navbar;
