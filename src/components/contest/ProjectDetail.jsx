import React, { useEffect, useState } from "react";
import { ProjectWall } from "./ProjectWall.jsx";
import {
  Heart,
  Star,
  Trophy,
  Check,
  Sparkles,
  ExternalLink,
  Shield,
  AlertCircle,
  Award,
  Calendar,
  Users,
  Gift,
  Flag,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  LoginRequiredModal,
  MaxLikeModal,
  VerificationRequiredModal,
  YourOwnPost,
} from "./ErrorModals.jsx";
import { motion } from "framer-motion";
import IconButtonRightIcon from "../IconButtonRightIcon.js";

export const ProjectDetail = ({
  totalProjects = 0,
  user,
  loadMoreProjects,
  notVerified = false,
  setYourNotVerified,
  ownProject = false,
  setYourOwnProjectModal,
  loginModal = false,
  unvoteAndVote,
  limitReached = false,
  setLoginModalClose,
  setLimitReaceddFalse,
  singlePostPage = false,
  loading = false,
  project,
  projects,
  onVote,
  showProjectWall,
  selectedProject,
  handleSelectProject,
  handleVoteProject,
  isMobile,
  onBackToProjects,
  showHeader = true,
}) => {
  const { t, i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenLogin, setIsOpenLogin] = useState(false);
  const [selectedFromWall, setSelectedFromWall] = useState(undefined);
  const [yourOwnProject, setYourOwnProject] = useState(undefined);
  const [isNotVerified, setIsNotVerified] = useState(undefined);
  const [projectLikedByUserId, setProjectLikedByUser] = useState(undefined);
  useEffect(() => {
    setIsOpen(limitReached);
  }, [limitReached]);
  useEffect(() => {
    setYourOwnProject(ownProject);
  }, [ownProject]);
  useEffect(() => {
    setIsNotVerified(notVerified);
  }, [notVerified]);
  useEffect(() => {
    setIsOpenLogin(loginModal);
  }, [loginModal]);

  const closeModal = () => {
    setIsOpen(false);
    setIsOpenLogin(false);
    setYourOwnProject(false);
    setIsNotVerified(false);
    setLimitReaceddFalse(false);
    setLoginModalClose(false);
    setYourOwnProjectModal(false);
    setYourNotVerified(false);
  };
  const iconProps = { size: 18, strokeWidth: 1.5 };

  const getLikedProjectId = () => {
    const liked = projects.find((project) => project.isLikedByUser === true);
    setProjectLikedByUser(liked ? liked : undefined);
    return liked ? liked : projects[0];
  };

  useEffect(() => {
    getLikedProjectId();
  }, [projects]);

  const onConfirmLikeModal = () => {
    if (selectedFromWall)
      unvoteAndVote(selectedFromWall, projectLikedByUserId?.id || "null");
    else unvoteAndVote(selectedProject.id, projectLikedByUserId?.id || "null");
  };

  const formatStars = (num) => {
    if (num >= 1e6) {
      return (num / 1e6).toFixed(1).replace(/\.0$/, "") + "M";
    } else if (num >= 1e3) {
      return (num / 1e3).toFixed(1).replace(/\.0$/, "") + "K";
    }
    return num.toString();
  };

  const handleLogin = () => {
    window.location.href = `${import.meta.env.VITE_IDE_URL}/login`;
  };
  const handleVerify = () => {
    window.location.href = `${import.meta.env.VITE_IDE_URL}/settings`;
  };

  if (!project && !singlePostPage) {
    return (
      <div>
        <div className="flex flex-col items-center px-4 py-8 h-full overflow-visible">
          <div className="w-full flex gap-4 max-lg:flex-col">
            <div
              className={` ${!isMobile ? "w-[60%]" : "w-full"} project-wall h-fit `}
            >
              {/* Competition Header Section */}
              <div className="mb-12 relative">
                <div className="absolute inset-0 bg-gradient-to-r from-biela-blue/30 via-biela-teal/20 to-biela-blue/30 rounded-3xl transform rotate-1 scale-105 blur-sm"></div>
                <div className="bg-gradient-to-r from-biela-blue/80 to-biela-teal/60 rounded-2xl p-10 relative overflow-hidden backdrop-blur-sm border border-white/10 shadow-[0_0_30px_rgba(10,81,75,0.3)]">
                  <div className="absolute -top-12 -right-12 w-40 h-40 bg-biela-green/10 rounded-full blur-3xl"></div>
                  <div className="absolute -bottom-12 -left-12 w-40 h-40 bg-biela-accent/10 rounded-full blur-3xl"></div>

                  <div className="relative">
                    {/* Main Competition Title - GLOW EFFECT REMOVED */}
                    <div className="text-center mb-8">
                      <h2 className="text-2xl rexton-light text-white relative inline-block">
                        <span className="relative text-biela-accent rexton-light">
                          {t("competition.header.mainTitle")}
                        </span>
                        <Sparkles className="absolute -top-4 -right-8 w-6 h-6 text-biela-accent animate-ping-slow" />
                        <Sparkles className="absolute -bottom-2 -left-8 w-6 h-6 text-biela-accent animate-ping-slow" />
                      </h2>
                    </div>

                    {/* Four Column Info Boxes */}
                    <div className="grid 2xl:grid-cols-4  sm:grid-cols-2 max-sm:grid-cols-1 gap-4">
                      <div className="bg-gradient-to-br from-white/5 to-transparent p-5 rounded-xl backdrop-blur-sm border border-white/10 transform transition-all duration-500 hover:scale-105 hover:shadow-[0_0_20px_rgba(68,255,161,0.2)] group">
                        <div className="flex items-center justify-center mb-3">
                          <div className="relative">
                            <div className="absolute inset-0 bg-biela-accent/20 rounded-full blur-md transform scale-150 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative bg-biela-dark-blue/80 p-3 rounded-full border border-biela-accent/30">
                              <Calendar className="w-6 h-6 text-biela-accent" />
                            </div>
                          </div>
                        </div>
                        <h3 className="text-lg text-biela-accent rexton-light text-center mb-2">
                          {t("competition.header.timelineTitle")}
                        </h3>
                        <p className="text-white/80 text-center text-md">
                          {t("competition.header.timelineDesc")}
                        </p>
                      </div>

                      <div className="bg-gradient-to-br from-white/5 to-transparent p-5 rounded-xl backdrop-blur-sm border border-white/10 transform transition-all duration-500 hover:scale-105 hover:shadow-[0_0_20px_rgba(68,255,161,0.2)] group">
                        <div className="flex items-center justify-center mb-3">
                          <div className="relative">
                            <div className="absolute inset-0 bg-biela-accent/20 rounded-full blur-md transform scale-150 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative bg-biela-dark-blue/80 p-3 rounded-full border border-biela-accent/30">
                              <Users className="w-6 h-6 text-biela-accent" />
                            </div>
                          </div>
                        </div>
                        <h3 className="text-lg text-biela-accent rexton-light text-center mb-2">
                          {t("competition.header.eligibilityTitle")}
                        </h3>
                        <p className="text-white/80 text-center text-md">
                          {t("competition.header.eligibilityDesc")}
                        </p>
                      </div>

                      <div className="bg-gradient-to-br from-white/5 to-transparent p-5 rounded-xl backdrop-blur-sm border border-white/10 transform transition-all duration-500 hover:scale-105 hover:shadow-[0_0_20px_rgba(68,255,161,0.2)] group">
                        <div className="flex items-center justify-center mb-3">
                          <div className="relative">
                            <div className="absolute inset-0 bg-biela-accent/20 rounded-full blur-md transform scale-150 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative bg-biela-dark-blue/80 p-3 rounded-full border border-biela-accent/30">
                              <Gift className="w-6 h-6 text-biela-accent" />
                            </div>
                          </div>
                        </div>
                        <h3 className="text-lg text-biela-accent rexton-light text-center mb-2">
                          {t("competition.header.prizesTitle")}
                        </h3>
                        <p className="text-white/80 text-center text-md">
                          {t("competition.header.prizesDesc")}
                        </p>
                      </div>

                      <div className="bg-gradient-to-br from-white/5 to-transparent p-5 rounded-xl backdrop-blur-sm border border-white/10 transform transition-all duration-500 hover:scale-105 hover:shadow-[0_0_20px_rgba(68,255,161,0.2)] group">
                        <div className="flex items-center justify-center mb-3">
                          <div className="relative">
                            <div className="absolute inset-0 bg-biela-accent/20 rounded-full blur-md transform scale-150 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative bg-biela-dark-blue/80 p-3 rounded-full border border-biela-accent/30">
                              <Flag className="w-6 h-6 text-biela-accent" />
                            </div>
                          </div>
                        </div>
                        <h3 className="text-lg text-biela-accent rexton-light text-center mb-2">
                          {t("competition.header.votingTitle")}
                        </h3>
                        <p className="text-white/80 text-center text-md">
                          {t("competition.header.votingDesc")}
                        </p>
                      </div>
                    </div>

                    {/* Tagline Footer */}
                    <div className="mt-8 text-center relative">
                      <div className="inline-block relative">
                        <Sparkles className="absolute -top-4 -left-8 w-6 h-6 text-biela-accent/70 animate-ping-slow" />
                        <Sparkles className="absolute -top-4 -right-8 w-6 h-6 text-biela-accent/70 animate-ping-slow" />
                        <p className="text-lg rexton-light text-white rexton-light">
                          {t("competition.header.tagline")}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Competition Details */}
              <div className="mb-12">
                <div className="grid grid-cols-2 gap-6 max-xl:grid-cols-1">
                  {/* Left Column */}
                  <div className="bg-gradient-to-br from-biela-blue/60 to-biela-teal/40 rounded-2xl p-8 relative backdrop-blur-sm border border-white/10 overflow-hidden shadow-[0_0_30px_rgba(10,81,75,0.3)]">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent background-shimmer"></div>
                    <div className="relative">
                      <h3 className="text-xl rexton-light text-start text-biela-accent mb-6 flex items-center gap-3">
                        <Award className="text-biela-accent" />
                        <span>{t("competition.details.howToEnter")}</span>
                      </h3>

                      <div className="space-y-5 text-start">
                        <div className="flex gap-3 items-start bg-white/5 rounded-xl p-5 border border-white/10 backdrop-blur-sm transition-all duration-300 hover:bg-white/10">
                          <div className="flex-shrink-0 bg-biela-accent text-biela-dark-blue  rounded-full w-8 h-8 flex items-center justify-center">
                            1
                          </div>
                          <div>
                            <h4 className="text-white text-lg font-light mb-1">
                              {t("competition.details.enter.step1.title")}
                            </h4>
                            <p className="text-white/70">
                              {t("competition.details.enter.step1.desc")}
                            </p>
                          </div>
                        </div>

                        <div className="flex gap-3 items-start bg-white/5 rounded-xl p-5 border border-white/10 backdrop-blur-sm transition-all duration-300 hover:bg-white/10">
                          <div className="flex-shrink-0 bg-biela-accent text-biela-dark-blue  rounded-full w-8 h-8 flex items-center justify-center">
                            2
                          </div>
                          <div>
                            <h4 className="text-white text-lg font-light mb-1">
                              {t("competition.details.enter.step2.title")}
                            </h4>
                            <p className="text-white/70">
                              {t("competition.details.enter.step2.desc")}.{" "}
                              {t("competition.details.enter.step2.subdesc")}
                            </p>
                          </div>
                        </div>

                        <div className="flex gap-3 items-start bg-white/5 rounded-xl p-5 border border-white/10 backdrop-blur-sm transition-all duration-300 hover:bg-white/10">
                          <div className="flex-shrink-0 bg-biela-accent text-biela-dark-blue  rounded-full w-8 h-8 flex items-center justify-center">
                            3
                          </div>
                          <div>
                            <h4 className="text-white text-lg font-light mb-2">
                              {t("competition.details.enter.step3.title")}
                            </h4>
                            <ul className="space-y-3">
                              <li className="flex items-center gap-3">
                                <Check
                                  className="text-biela-accent flex-shrink-0"
                                  size={18}
                                />
                                <span className="text-white/70">
                                  {t("competition.details.enter.step3.list1")}
                                </span>
                              </li>
                              <li className="flex items-center gap-3">
                                <Check
                                  className="text-biela-accent flex-shrink-0"
                                  size={18}
                                />
                                <span className="text-white/70">
                                  {t("competition.details.enter.step3.list2")}
                                </span>
                              </li>
                              <li className="flex items-center gap-3">
                                <Check
                                  className="text-biela-accent flex-shrink-0"
                                  size={18}
                                />
                                <span className="text-white/70">
                                  {t("competition.details.enter.step3.list3")}
                                </span>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="bg-gradient-to-br from-biela-blue/60 to-biela-teal/40 rounded-2xl p-8 relative backdrop-blur-sm border border-white/10 overflow-hidden shadow-[0_0_30px_rgba(10,81,75,0.3)]">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent background-shimmer"></div>
                    <div className="relative">
                      <h3 className="text-xl rexton-light text-biela-accent mb-6 flex items-center gap-3 text-start">
                        <Trophy className="text-biela-accent" />
                        <span>{t("competition.details.prizeStructure")}</span>
                      </h3>

                      <div className="space-y-4">
                        <div className="bg-white/5 rounded-xl p-5 border border-white/10 backdrop-blur-sm relative overflow-hidden transition-all duration-300 hover:bg-white/10">
                          <div className="absolute -top-6 -right-6 w-16 h-16 bg-green-500/10 rounded-full blur-xl"></div>
                          <div className="relative flex items-center gap-4">
                            <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center flex-shrink-0 border border-green-500/30">
                              <Trophy size={24} className="text-green-400" />
                            </div>
                            <div>
                              <h4 className="text-green-400 rexton-light text-xl">
                                {t("competition.details.prizes.firstPlace")}
                              </h4>
                              <p className="text-white text-lg font-light  text-start">
                                {t(
                                  "competition.details.prizes.firstPlaceValue",
                                )}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="bg-white/5 rounded-xl p-5 border border-white/10 backdrop-blur-sm relative overflow-hidden transition-all duration-300 hover:bg-white/10">
                          <div className="absolute -top-6 -right-6 w-16 h-16 bg-blue-500/10 rounded-full blur-xl"></div>
                          <div className="relative flex items-center gap-4">
                            <div className="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center flex-shrink-0 border border-blue-500/30">
                              <Trophy size={24} className="text-blue-400" />
                            </div>
                            <div>
                              <h4 className="text-blue-400 rexton-light text-xl">
                                {t("competition.details.prizes.secondPlace")}
                              </h4>
                              <p className="text-white text-lg font-light  text-start">
                                {t(
                                  "competition.details.prizes.secondPlaceValue",
                                )}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="bg-white/5 text-start rounded-xl p-5 border border-white/10 backdrop-blur-sm relative overflow-hidden transition-all duration-300 hover:bg-white/10">
                          <div className="absolute -top-6 -right-6 w-16 h-16 bg-purple-500/10 rounded-full blur-xl"></div>
                          <div className="relative flex items-center gap-4">
                            <div className="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 border border-purple-500/30">
                              <Trophy size={24} className="text-purple-400" />
                            </div>
                            <div>
                              <h4 className="text-purple-400 rexton-light text-xl">
                                {t("competition.details.prizes.thirdPlace")}
                              </h4>
                              <p className="text-white text-lg font-light  text-start">
                                {t(
                                  "competition.details.prizes.thirdPlaceValue",
                                )}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="bg-white/5 rounded-xl p-5 border border-white/10 backdrop-blur-sm relative overflow-hidden transition-all duration-300 hover:bg-white/10">
                          <div className="absolute -top-6 -right-6 w-16 h-16 bg-biela-accent/10 rounded-full blur-xl"></div>
                          <div className="relative flex items-center gap-4">
                            <div className="w-12 h-12 rounded-full bg-biela-accent/20 flex items-center justify-center flex-shrink-0 border border-biela-accent/30">
                              <Gift size={24} className="text-biela-accent" />
                            </div>
                            <div>
                              <h4 className="text-biela-accent rexton-light text-xl text-start">
                                {t("competition.details.prizes.fourthToTenth")}
                              </h4>
                              <p className="text-white text-lg font-light  text-start">
                                {t(
                                  "competition.details.prizes.fourthToTenthValue",
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Qualification Requirements & Voting Guidelines */}
              <div className="">
                <div className="grid grid-cols-2 gap-6 max-xl:grid-cols-1">
                  {/* Qualification Requirements */}
                  <div className="bg-gradient-to-br from-biela-blue/60 to-biela-teal/40 rounded-2xl p-8 relative backdrop-blur-sm border border-white/10 overflow-hidden shadow-[0_0_30px_rgba(10,81,75,0.3)]">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent background-shimmer"></div>
                    <div className="relative">
                      <h3 className="text-xl rexton-light text-biela-accent mb-6 flex items-center gap-3 text-start">
                        <Star className="text-biela-accent" />
                        <span>{t("competition.qualification.heading")}</span>
                      </h3>

                      <div className="rounded-xl p-4 bg-gradient-to-br from-white/10 to-white/5 text-start backdrop-blur-md border border-white/20">
                        <p className="text-white mb-5 leading-relaxed">
                          {t("competition.qualification.description")}
                        </p>

                        <div className="flex gap-3 items-center mb-6">
                          <div className="flex-shrink-0 bg-biela-accent text-biela-dark-blue  rounded-full w-8 h-8 flex items-center justify-center">
                            1
                          </div>
                          <div>
                            <h4 className="text-white font-light">
                              {t("competition.qualification.criteria1")}
                            </h4>
                          </div>
                        </div>

                        <div className="flex gap-3 items-center">
                          <div className="flex-shrink-0 bg-biela-accent text-biela-dark-blue  rounded-full w-8 h-8 flex items-center justify-center">
                            2
                          </div>
                          <div>
                            <h4 className="text-white font-light">
                              {t("competition.qualification.criteria2")}
                            </h4>
                          </div>
                        </div>
                      </div>

                      <div className="mt-6 p-4 bg-biela-accent/10 rounded-xl text-start border border-biela-accent/30">
                        <div className="flex items-start gap-3">
                          <Sparkles
                            className="text-biela-accent flex-shrink-0 mt-1"
                            size={20}
                          />
                          <p className="text-white/90">
                            <span className="text-biela-accent ">
                              {t("competition.qualification.proTipLabel")}
                            </span>{" "}
                            {t("competition.qualification.proTipDesc")}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Voting Guidelines */}
                  <div className="bg-gradient-to-br text-start from-biela-blue/60 to-biela-teal/40 rounded-2xl p-8 relative backdrop-blur-sm border border-white/10 overflow-hidden shadow-[0_0_30px_rgba(10,81,75,0.3)]">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent background-shimmer"></div>
                    <div className="relative">
                      <h3 className="text-xl rexton-light text-biela-accent mb-6 flex items-center gap-3 text-start">
                        <Shield className="text-biela-accent" />
                        <span>{t("competition.voting.heading")}</span>
                      </h3>

                      <div className="space-y-5">
                        <div className="flex items-start gap-4 bg-white/5 rounded-xl p-4 border border-white/10 backdrop-blur-sm">
                          <div className="flex-shrink-0 bg-white/10 rounded-full p-2">
                            <AlertCircle
                              className="text-biela-accent"
                              size={22}
                            />
                          </div>
                          <div>
                            <h4 className="text-white text-lg font-light mb-1">
                              {t("competition.voting.oneVotePolicyTitle")}
                            </h4>
                            <p className="text-white/80">
                              {t("competition.voting.oneVotePolicyDesc")}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start gap-4 bg-white/5 rounded-xl p-4 border border-white/10 backdrop-blur-sm">
                          <div className="flex-shrink-0 bg-white/10 rounded-full p-2">
                            <AlertCircle
                              className="text-biela-accent"
                              size={22}
                            />
                          </div>
                          <div>
                            <h4 className="text-white text-lg font-light mb-1">
                              {t("competition.voting.accountVerificationTitle")}
                            </h4>
                            <p className="text-white/80">
                              {t("competition.voting.accountVerificationDesc")}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start gap-4 bg-white/5 rounded-xl p-4 border border-white/10 backdrop-blur-sm">
                          <div className="flex-shrink-0 bg-white/10 rounded-full p-2">
                            <AlertCircle
                              className="text-biela-accent"
                              size={22}
                            />
                          </div>
                          <div>
                            <h4 className="text-white text-lg font-light mb-1">
                              {t("competition.voting.tieBreakingTitle")}
                            </h4>
                            <p className="text-white/80">
                              {t("competition.voting.tieBreakingDesc")}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="mt-6 p-4 bg-biela-accent/10 rounded-xl border border-biela-accent/30">
                        <h4 className="text-biela-accent rexton-light text-lg mb-2 flex items-center gap-2">
                          <Heart
                            className="text-biela-accent"
                            size={18}
                            fill="currentColor"
                          />
                          <span>{t("competition.voting.howToVoteTitle")}</span>
                        </h4>
                        <p className="text-white/90">
                          {t("competition.voting.howToVoteDesc")}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className={`${isMobile ? "w-full h-full" : "w-[40%] max-h-[calc(100vh-80px)] sticky top-[80px] border-l border-white/10 bg-biela-dark-blue bg-opacity-70"}`}
            >
              <ProjectWall
                totalProjects={totalProjects}
                user={user}
                isLink={true}
                projects={projects}
                selectedProject={selectedProject}
                onSelectProject={handleSelectProject}
                onVoteProject={(e, i) => {
                  setSelectedFromWall(e);
                  handleVoteProject(e, i);
                }}
                loadMoreProjects={loadMoreProjects}
              />
            </div>
          </div>
        </div>
        <MaxLikeModal
          isOpen={isOpen}
          onClose={closeModal}
          onConfirm={onConfirmLikeModal}
          currentLikedProject={projectLikedByUserId}
        />

        <LoginRequiredModal
          isOpen={isOpenLogin}
          onClose={closeModal}
          onLogin={handleLogin}
        />
        <YourOwnPost
          isOpen={yourOwnProject}
          onClose={closeModal}
          onLogin={handleLogin}
        />
        <VerificationRequiredModal
          isOpen={isNotVerified}
          onClose={closeModal}
          onVerify={handleVerify}
        />
      </div>
    );
  }

  if (loading)
    return (
      <div>
        <p>{t("contestItems.loading")}</p>
      </div>
    );

  return (
    <div
      className={
        "flex max-lg:flex-col max-lg:mb-8 max-lg:bg-[linear-gradient(to_right,_#0A1023,_#0D1A3A,_#0a514b8a,_#14b0727d)] bg-[linear-gradient(to_right,_#0A1023,_#0D1A3A,_#0A514B,_#14B072)]"
      }
    >
      <div
        className={` ${!isMobile ? "w-[60%]" : "w-full"} xl:max-h-[calc(100vh-80px)] lg:max-h-[calc(100vh-60px)]   flex flex-col bg-gradient-to-r from-biela-blue/50 to-biela-teal/30 relative shadow-[0_0_30px_rgba(10,81,75,0.3)]  `}
      >
        <div className="relative  min-h-1/3 overflow-hidden max-lg:min-h-[400px] ">
          <div
            className="absolute inset-0 animate-scroll-down "
            style={{ height: "100vh" }}
          >
            <img
              loading="lazy"
              src={project.image}
              alt={project.title}
              className="w-full object-cover min-h-[150%]"
            />
          </div>
          <div className="absolute inset-0 overflow-hidden bg-gradient-to-t from-biela-dark-blue to-transparent"></div>
        </div>

        <div className="flex-grow p-4 md:p-8 overflow-y-auto project-wall text-start bg-transparent">
          <div className="flex flex-col flex-wrap md:flex-row mb-4 md:justify-between md:items-start gap-4">
            <div>
              <h1 className="text-2xl text-start md:text-3xl rexton-light text-white">
                {project.title}
              </h1>
              <p className="text-gray-400 text-start ">
                {t("contestItems.createBy")} {project.creator}
              </p>
            </div>

            <div>
              <div className="flex items-center gap-3">
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  className="flex justify-center"
                >
                  <motion.a
                    href={project.projectUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full sm:w-auto px-6 py-1.5 bg-gradient-to-r from-green-500 to-green-600 rounded-lg text-white font-light flex items-center justify-center gap-2 group relative overflow-hidden cursor-pointer"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                      animate={{ x: ["-100%", "200%"] }}
                      transition={{ repeat: Infinity, duration: 1.5 }}
                    />
                    <IconButtonRightIcon
                      icon={<ExternalLink {...iconProps} />}
                      data-tooltip-id="project-tooltip"
                      variant="primary"
                      data-tooltip-content={t(
                        "contestItems.exploreLiveProject",
                      )}
                      text={t("contestItems.exploreLiveProject")}
                    />
                    {/*<FaExternalLinkAlt className="text-sm group-hover:translate-x-1 transition-transform"/>*/}
                  </motion.a>
                </motion.div>
                <div className="relative group">
                  <button
                    title={"Like"}
                    className="cursor-pointer flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
                    onClick={() => onVote(project.id, project.isLikedByUser)}
                  >
                    <Heart
                      size={18}
                      className={
                        project.isLikedByUser
                          ? "fill-[#44FFA1] text-[#44FFA1]"
                          : "text-white"
                      }
                    />
                    <span>
                      {project.votes}{" "}
                      {project.votes === 1 ? "like" : t("contestItems.likes")}
                    </span>
                  </button>
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 hidden group-hover:block bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap z-50">
                    {project.isLikedByUser
                      ? t("contestItems.unlikeThisProject")
                      : t("contestItems.likeThisProject")}
                  </div>
                </div>
                {/*<a*/}
                {/*    href={project.projectUrl}*/}
                {/*    target="_blank"*/}
                {/*    rel="noopener noreferrer"*/}
                {/*    className="flex items-center gap-2 px-4 py-2 rounded-full bg-[#0A514B] hover:bg-[#14B072] text-white transition-colors duration-300"*/}
                {/*>*/}
                {/*  <ExternalLink size={16} />*/}
                {/*  <span>{t("contestItems.viewProject")}</span>*/}
                {/*</a>*/}
                {/* {
                    project.creatorId === user?.id ? (
                        <button
                            className="flex cursor-not-allowed items-center gap-2 px-4 py-2 rounded-full bg-white/10 transition-colors"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                            disabled
                        >
                          <Heart size={18} className={project.isLikedByUser ? "fill-[#44FFA1] text-[#44FFA1]" : "text-white"}/>
                          <span>{project.votes}</span>
                        </button>
                    ) : (
                        <button
                            className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
                            onClick={() => onVote(project.id, project.isLikedByUser)}
                        >
                          <Heart size={18} className={project.isLikedByUser ? "fill-[#44FFA1] text-[#44FFA1]" : "text-white"}/>
                          <span>{project.votes}</span>
                        </button>
                    )
                  }*/}
                <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-yellow-700  text-white">
                  <Star size={14} className="text-yellow-300" />
                  <span>
                    {formatStars(project?.stars)} {t("contestItems.stars")}
                  </span>
                </div>
              </div>

              <div className="flex justify-end gap-5 mt-2 text-white/70 text-sm">
                {/*<div className="flex items-center gap-1.5">*/}
                {/*  <Star size={14} className="text-yellow-300" />*/}
                {/*  <span>{project?.stars?.toLocaleString()} {t("contestItems.stars")}</span>*/}
                {/*</div>*/}
                {/*<div className="flex items-center gap-1.5">*/}
                {/*  <DollarSign size={14} className="text-biela-accent" />*/}
                {/*  <span>{project.cost}</span>*/}
                {/*</div>*/}
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mb-6">
            {project.tech.map((tech, index) => (
              <span
                key={index}
                className="px-3 py-1 text-sm rounded-full bg-white/10 text-white/70"
              >
                {tech}
              </span>
            ))}
          </div>

          <div className="space-y-6 font-manrope">
            <div>
              <h2 className="text-xl text-white mb-3 rexton-light capitalize">
                {t("contestItems.overview")}
              </h2>
              <p className="text-gray-300 leading-relaxed">
                {project.fullDescription}
              </p>
            </div>

            <div>
              <h2 className="text-xl text-white mb-3 rexton-light">
                {t("contestItems.keyFeatures")}
              </h2>
              <ul className="list-disc list-inside text-gray-300 space-y-2">
                {project.features.map((feature, index) => (
                  <li key={index} className="leading-relaxed">
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            <div className="pt-4">
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="flex justify-center"
              >
                <motion.a
                  href={project.projectUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-full sm:w-auto px-6 py-1.5 bg-gradient-to-r from-green-500 to-green-600 rounded-lg text-white font-light flex items-center justify-center gap-2 group relative overflow-hidden cursor-pointer"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                    animate={{ x: ["-100%", "200%"] }}
                    transition={{ repeat: Infinity, duration: 1.5 }}
                  />
                  <IconButtonRightIcon
                    icon={<ExternalLink {...iconProps} />}
                    data-tooltip-id="project-tooltip"
                    variant="primary"
                    data-tooltip-content={t("contestItems.exploreLiveProject")}
                    text={t("contestItems.exploreLiveProject")}
                  />
                  {/*<FaExternalLinkAlt className="text-sm group-hover:translate-x-1 transition-transform"/>*/}
                </motion.a>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
      <div
        className={`${isMobile ? "w-full h-full" : "w-[40%] h-full border-l border-white/10 bg-[#0A1023]/70"}`}
      >
        <ProjectWall
          totalProjects={totalProjects}
          user={user}
          projects={projects}
          fullHeight={true}
          selectedProject={selectedProject}
          onSelectProject={handleSelectProject}
          onVoteProject={(e, i) => {
            setSelectedFromWall(e);
            handleVoteProject(e, i);
          }}
          loadMoreProjects={loadMoreProjects}
        />
      </div>
      <YourOwnPost isOpen={yourOwnProject} onClose={closeModal} />
      <MaxLikeModal
        isOpen={isOpen}
        onClose={closeModal}
        onConfirm={onConfirmLikeModal}
        currentLikedProject={projectLikedByUserId}
      />
      <LoginRequiredModal
        isOpen={isOpenLogin}
        onClose={closeModal}
        onLogin={handleLogin}
      />
      <VerificationRequiredModal
        isOpen={isNotVerified}
        onClose={closeModal}
        onVerify={handleVerify}
      />
    </div>
  );
};
