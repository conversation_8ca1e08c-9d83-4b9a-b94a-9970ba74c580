import React, { useEffect } from "react";
import Modal from "./Modal";
import { AlertTriangle, Heart, LogIn, UserCheck } from "lucide-react";
import { BiMessageError } from "react-icons/bi";
import { useTranslation } from "react-i18next";
import { FaSpinner } from "react-icons/fa";

export const MaxLikeModal = ({
  isOpen,
  onClose,
  onConfirm,
  currentLikedProject,
}) => {
  const { t } = useTranslation();
  const [isLoaded, setIsLoading] = React.useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsLoading(false);
    }
  }, [isOpen]);
  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {
        setIsLoading(false);
        onClose();
      }}
      title={t("contestItems.likeLimitReached", "Like Limit Reached")}
    >
      <div className="flex items-start gap-4 mb-6">
        <div className="bg-amber-500/20 p-2 rounded-full flex-shrink-0">
          <AlertTriangle className="text-amber-400" size={24} />
        </div>
        <div>
          <p className="text-white mb-4">
            {t(
              "contestItems.youCanLikeMaximumOneProject",
              "You can like maximum one project. Would you like to unlike the current one and like this one instead?",
            )}
          </p>

          {currentLikedProject && (
            <div className="bg-white/5 p-4 rounded-lg border border-white/10 mb-4 text-start">
              <h3 className="text-lg font-semibold text-white mb-2">
                {t(
                  "contestItems.currentlyLikedProject",
                  "Currently Liked Project",
                )}
              </h3>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded overflow-hidden flex-shrink-0">
                  <img
                    loading="lazy"
                    src={currentLikedProject.image}
                    alt={currentLikedProject.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <p className="font-medium text-white">
                    {currentLikedProject.title}
                  </p>
                  <p className="text-xs text-white/70">
                    by {currentLikedProject.creator}
                  </p>
                </div>
                <div className="ml-auto flex items-center gap-1 text-biela-accent">
                  <Heart size={14} className="fill-biela-accent" />
                  <span className="text-sm">{currentLikedProject.votes}</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex gap-3 justify-end">
        <button
          title={"Cancel"}
          onClick={onClose}
          className="px-4 py-2 rounded-lg bg-white/10 hover:bg-white/20 text-white transition-colors"
        >
          {t("common.cancel", "Cancel")}
        </button>
        <button
          title={t("contestItems.switchMyLike", "Switch My Like")}
          onClick={() => {
            setIsLoading(true);
            onConfirm();
          }}
          className="px-4 min-w-[165px] py-2 rounded-lg cursor-pointer bg-[#14B072] hover:bg-opacity-90 text-white transition-colors flex items-center gap-2"
        >
          {isLoaded ? (
            <FaSpinner className="animate-spin flex-1 text-white" />
          ) : (
            <>
              <Heart size={16} />
              <span>{t("contestItems.switchMyLike", "Switch My Like")}</span>
            </>
          )}
        </button>
      </div>
    </Modal>
  );
};

export const LoginRequiredModal = ({ isOpen, onClose, onLogin }) => {
  const { t } = useTranslation();

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Login Required">
      <div className="flex items-start gap-4 mb-6 text-start">
        <div className="bg-blue-500/20 p-2 rounded-full flex-shrink-0">
          <LogIn className="text-blue-400" size={24} />
        </div>
        <div>
          <p className="text-white mb-2">
            {t(
              "contestItems.mustLoggedIntoLikeProject",
              "You must be logged in to like a project.",
            )}
          </p>
          <p className="text-white/70 text-sm">
            {t(
              "contestItems.signInToBielaAccountToVote",
              "Please sign in to your Biela.dev account to participate in the voting process.",
            )}
          </p>
        </div>
      </div>

      <div className="flex gap-3 justify-end">
        <button
          title={t("common.close", "Close")}
          onClick={onClose}
          className="px-4 py-2 rounded-lg bg-white/10 hover:bg-white/20 text-white transition-colors"
        >
          {t("common.close", "Close")}
        </button>
        <button
          title={t("common.loginNow", "Login Now")}
          onClick={onLogin}
          className="px-4 py-2 cursor-pointer rounded-lg bg-[#44FFA1] hover:bg-opacity-90 text-[#0A1023] font-medium transition-colors flex items-center gap-2"
        >
          <LogIn size={16} />
          <span>{t("common.loginNow", "Login Now")}</span>
        </button>
      </div>
    </Modal>
  );
};

export const YourOwnPost = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Your Own Project">
      <div className="flex items-start gap-4 mb-6 text-start">
        <div className="bg-blue-500/20 p-2 rounded-full flex-shrink-0">
          <Heart className="text-blue-400" size={24} />
        </div>
        <div>
          <p className="text-white/70 text-sm">
            You can't like your own post, please like a post of another person
          </p>
        </div>
      </div>

      <div className="flex gap-3 justify-end">
        <button
          title={t("common.close", "Close")}
          onClick={onClose}
          className="px-4 py-2 rounded-lg bg-white/10 hover:bg-white/20 text-white transition-colors cursor-pointer"
        >
          {t("common.close", "Close")}
        </button>
      </div>
    </Modal>
  );
};

export const VerificationRequiredModal = ({ isOpen, onClose, onVerify }) => {
  const { t } = useTranslation();

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Verification Required">
      <div className="flex items-start gap-4 mb-6 text-start">
        <div className="bg-purple-500/20 p-2 rounded-full flex-shrink-0">
          <UserCheck className="text-purple-400" size={24} />
        </div>
        <div>
          <p className="text-white mb-2">
            {t(
              "contestItems.yourAccountIsNotVerifiedYet",
              "Your account is not yet verified. Please verify your account to be able to like a project.",
            )}
          </p>
          <p className="text-white/70 text-sm">
            {t(
              "contestItems.accountVerificationEnsureFairVoting",
              "Account verification ensures fair voting and helps maintain the integrity of the competition.",
            )}
          </p>
        </div>
      </div>

      <div className="flex gap-3 justify-end">
        <button
          title={t("common.close", "Close")}
          onClick={onClose}
          className="px-4 py-2 rounded-lg bg-white/10 hover:bg-white/20 text-white transition-colors"
        >
          {t("common.close", "Close")}
        </button>
        <button
          title={t("common.verifyAccount", "Verify Account")}
          onClick={onVerify}
          className="px-4 py-2 cursor-pointer rounded-lg bg-[#14B072] hover:bg-opacity-90 text-white transition-colors flex items-center gap-2"
        >
          <UserCheck size={16} />
          <span>{t("common.verifyAccount", "Verify Account")}</span>
        </button>
      </div>
    </Modal>
  );
};

export const GenericErrorModal = ({ isOpen, onClose, message, title }) => {
  const { t } = useTranslation();
  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title ?? "Error"}>
      <div className="flex items-center gap-4 mb-6 text-start">
        <div className="bg-blue-500/20 p-2 rounded-full flex-shrink-0">
          <BiMessageError className="text-blue-400" size={24} />
        </div>
        <div>
          <p className="text-white/70 text-sm">
            {message ||
              t(
                "contestItems.somethingWentWrong",
                "Something went wrong. Please try again later.",
              )}
          </p>
        </div>
      </div>

      <div className="flex gap-3 justify-end">
        <button
          title={t("common.close", "Close")}
          onClick={onClose}
          className="px-4 py-2 rounded-lg bg-white/10 hover:bg-white/20 text-white transition-colors cursor-pointer"
        >
          {t("common.close", "Close")}
        </button>
      </div>
    </Modal>
  );
};
