import React from "react";
import { Heart } from "lucide-react";
import { useTranslation } from "react-i18next";

export const ProjectCard = ({
  user,
  isLink,
  project,
  isLast,
  isSelected,
  onSelect,
  onVote,
}) => {
  const { t } = useTranslation();

  if (isLink) {
    return (
      <a
        href={`/hackathon/${project.id}`}
        className={`relative p-4 block rounded-lg transition-all duration-300 cursor-pointer blurred-card hover:bg-opacity-20 border border-transparent ${
          isSelected
            ? "border-biela-accent bg-opacity-20"
            : "hover:border-white/20"
        }`}
      >
        <div className={`flex ${!isLast && "mb-3 md:mb-4"}`}>
          <div className="w-24 h-36 rounded-md overflow-hidden flex-shrink-0">
            <img
              loading="lazy"
              src={project.imageSmall ?? project.image}
              alt={project.title}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="ml-3 flex-grow">
            <h3 className="font-rexton text-lg text-white">{project.title}</h3>
            <p className="text-sm text-gray-300 font-manrope">
              {t("contestItems.by")} {project.creator}
            </p>
            <div className="flex flex-wrap mt-1 gap-1">
              {project.tech.slice(0, 2).map((tech, index) => (
                <span
                  key={index}
                  className="px-1.5 py-0.5 text-sm rounded bg-white/10 text-white/70"
                >
                  {tech}
                </span>
              ))}
              {project.tech.length > 2 && (
                <span className="px-1.5 py-0.5 text-sm rounded bg-white/10 text-white/70">
                  +{project.tech.length - 2}
                </span>
              )}
              <p className="text-md text-gray-300 font-manrope line-clamp-3 mb-3">
                {project.description}
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          {user && project.creatorId !== user._id ? (
            <span className="flex items-center cursor-pointer gap-1 text-md text-white/60 transition-colors">
              <Heart
                size={14}
                className={
                  project.isLikedByUser ? "fill-[#44FFA1] text-[#44FFA1]" : ""
                }
              />
              <span>{project.votes}</span>
            </span>
          ) : (
            <div className="relative group overflow-visible">
              <span className="flex items-center cursor-pointer gap-1 text-sm text-white/60 hover:text-[#44FFA1] transition-colors">
                <Heart
                  size={14}
                  className={
                    project.isLikedByUser ? "fill-[#44FFA1] text-[#44FFA1]" : ""
                  }
                />
                <span>{project.votes}</span>
              </span>
              <div className="absolute bottom-full right-0 mb-0.5 hidden group-hover:block bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap z-50">
                {project.isLikedByUser
                  ? t("contestItems.unlikeThisProject")
                  : t("contestItems.likeThisProject")}
              </div>
            </div>
          )}
        </div>
      </a>
    );
  }
  return (
    <div
      className={`relative p-4 mb-4 rounded-lg transition-all duration-300 cursor-pointer blurred-card hover:bg-opacity-20 border border-transparent ${
        isSelected
          ? "border-biela-accent bg-opacity-20"
          : "hover:border-white/20"
      }`}
      onClick={() => onSelect(project)}
    >
      <div className={`flex ${!isLast && "mb-3 md:mb-4"}`}>
        <div className="w-24 h-36 rounded-md overflow-hidden flex-shrink-0">
          <img
            loading="lazy"
            src={project.imageSmall ?? project.image}
            alt={project.title}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="ml-3 flex-grow">
          <h3 className="font-rexton text-lg text-white">{project.title}</h3>
          <p className="text-xs text-gray-300 font-manrope">
            {t("contestItems.by")} {project.creator}
          </p>
          <div className="flex flex-col mt-1 gap-1">
            <div className="flex flex-wrap gap-1">
              {project.tech.slice(0, 2).map((tech, index) => (
                <span
                  key={index}
                  className="px-1.5 py-0.5 text-[10px] rounded bg-white/10 text-white/70"
                >
                  {tech}
                </span>
              ))}
              {project.tech.length > 2 && (
                <span className="px-1.5 py-0.5 text-[10px] rounded bg-white/10 text-white/70">
                  +{project.tech.length - 2}
                </span>
              )}
            </div>

            <p className="text-sm text-gray-300 font-manrope line-clamp-3 mb-3">
              {project.description}
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        {user && project.creatorId === user._id ? (
          <button
            title={"Like"}
            className="flex items-center cursor-pointer gap-1 text-sm text-white/60 transition-colors"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onVote(project.id, project.isLikedByUser);
            }}
          >
            <Heart
              size={14}
              className={
                project.isLikedByUser ? "fill-[#44FFA1] text-[#44FFA1]" : ""
              }
            />
            <span>{project.votes}</span>
          </button>
        ) : (
          <div className="relative group overflow-visible z-10">
            <button
              title={"Like"}
              className="flex items-center gap-1 cursor-pointer text-sm text-white/60 hover:text-[#44FFA1] transition-colors"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onVote(project.id, project.isLikedByUser);
              }}
            >
              <Heart
                size={14}
                className={
                  project.isLikedByUser ? "fill-[#44FFA1] text-[#44FFA1]" : ""
                }
              />
              <span>{project.votes}</span>
            </button>
            <div className="absolute bottom-full right-0 mb-0.5 hidden group-hover:block bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap z-50">
              {project.isLikedByUser
                ? t("contestItems.unlikeThisProject")
                : t("contestItems.likeThisProject")}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
