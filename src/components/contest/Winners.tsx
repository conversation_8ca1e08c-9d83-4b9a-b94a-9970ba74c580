import React from "react";
import { useTranslation } from "react-i18next";
import { CheckCircle, XCircle, Star, Sparkles, Heart } from "lucide-react";

interface Winner {
  id: string;
  title: string;
  creator: string;
  creatorId: string;
  description: string;
  tech: string[];
  votes: number;
  image: string;
  projectUrl: string;
  hasActiveReferrals: boolean;
  hasLikedProject: boolean;
  place: number;
  isQualified?: boolean;
}

interface WinnersProps {
  winners: Winner[];
}

export const Winners: React.FC<WinnersProps> = ({ winners }) => {
  const { t } = useTranslation();

  const getPlaceColor = (place: number) => {
    // All places 4-10 use yellow styling
    return "text-yellow-400";
  };

  const getPlaceGradient = (place: number) => {
    // All places 4-10 use yellow gradient
    return "from-yellow-400 to-yellow-600";
  };

  const getPrizeAmount = (place: number) => {
    return "Prize";
  };

  const getPlaceLabel = (place: number) => {
    switch (place) {
      case 4:
        return "4th PLACE";
      case 5:
        return "5th PLACE";
      case 6:
        return "6th PLACE";
      case 7:
        return "7th PLACE";
      case 8:
        return "8th PLACE";
      case 9:
        return "9th PLACE";
      case 10:
        return "10th PLACE";
      default:
        return `${place}th PLACE`;
    }
  };

  // Just use the winners directly (they are already places 4-10)
  const allDisplayItems = winners;

  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-8">
      {/* Winners Announcement Header */}
      <div className="text-center mb-12">
        <h2 className="rexton-light text-3xl md:text-5xl text-white mb-6 relative inline-flex items-center justify-center gap-4">
          <Sparkles className="w-8 h-8 md:w-12 md:h-12 text-biela-accent animate-pulse" />
          {t("contest.winnersAnnouncement")}
          <Sparkles className="w-8 h-8 md:w-12 md:h-12 text-biela-accent animate-pulse" />
        </h2>
        <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto">
          {t("contest.congratulationsMessage")}
        </p>
      </div>

      {/* Actual Winners (Places 4-10) */}
      {allDisplayItems.length > 0 && (
        <>
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl text-white mb-4">
              {t("contest.qualifiedWinners")}
            </h3>
            <p className="text-gray-300">
              {t("contest.qualifiedWinnersMessage")}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {allDisplayItems.slice(0, 4).map((item, index) => (
              <div
                key={item.id}
                className={`relative bg-gradient-to-br ${getPlaceGradient(
                  item.place
                )} p-1 rounded-xl transform hover:scale-105 transition-all duration-300`}
              >
                <div className="bg-gray-900 rounded-xl p-4 h-full flex flex-col">
                  {/* Place Badge */}
                  <div className="flex items-center justify-between mb-3">
                    <div
                      className={`inline-flex items-center gap-1 px-3 py-1 rounded-full bg-gradient-to-r ${getPlaceGradient(
                        item.place
                      )} text-white font-bold text-sm`}
                    >
                      <Star className="w-4 h-4" />
                      {getPlaceLabel(item.place)}
                    </div>
                    <div className={`text-lg font-bold ${getPlaceColor(item.place)}`}>
                      {getPrizeAmount(item.place)}
                    </div>
                  </div>

                  {/* Project Image */}
                  {item.image && (
                    <div className="mb-3 rounded-lg overflow-hidden">
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-full h-32 object-cover"
                      />
                    </div>
                  )}

                  {/* Project Info */}
                  <div className="mb-3 flex-grow">
                    <h4 className="text-lg font-bold text-white mb-1 overflow-hidden" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      {item.title}
                    </h4>
                    <p className="text-gray-400 text-sm mb-1">
                      by {item.creator}
                    </p>
                    <p className="text-xs text-gray-300 overflow-hidden" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      {item.description}
                    </p>
                  </div>

                  {/* Likes */}
                  <div className="flex items-center gap-2 mb-3">
                    <Heart className="w-4 h-4 text-red-400 fill-current" />
                    <span className="text-white text-sm font-semibold">{item.votes} likes</span>
                  </div>

                  {/* Bottom Section - Qualifications and Project Link */}
                  <div className="mt-auto">
                    {/* Qualifications */}
                    <div className="border-t border-gray-600 pt-3 mb-3">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          {item.hasActiveReferrals ? (
                            <CheckCircle className="w-4 h-4 text-green-400" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-400" />
                          )}
                          <span className="text-xs text-gray-300">
                            {t("contest.conditionActiveReferrals")}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          {item.hasLikedProject ? (
                            <CheckCircle className="w-4 h-4 text-green-400" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-400" />
                          )}
                          <span className="text-xs text-gray-300">
                            {t("contest.conditionLikedProject")}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Project Link */}
                    {item.projectUrl && (
                      <div>
                        <a
                          href={item.projectUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`inline-flex items-center gap-1 px-3 py-1 rounded-lg bg-gradient-to-r ${getPlaceGradient(
                            item.place
                          )} text-white text-sm font-semibold hover:opacity-90 transition-opacity`}
                        >
                          View Project
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Places 8-10 - Centered */}
          {allDisplayItems.length > 4 && (
            <div className="flex justify-center mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl">
                {allDisplayItems.slice(4, 7).map((item, index) => (
                  <div
                    key={item.id}
                    className={`relative bg-gradient-to-br ${getPlaceGradient(
                      item.place
                    )} p-1 rounded-xl transform hover:scale-105 transition-all duration-300`}
                  >
                    <div className="bg-gray-900 rounded-xl p-4 h-full flex flex-col">
                      {/* Place Badge */}
                      <div className="flex items-center justify-between mb-3">
                        <div
                          className={`inline-flex items-center gap-1 px-3 py-1 rounded-full bg-gradient-to-r ${getPlaceGradient(
                            item.place
                          )} text-white font-bold text-sm`}
                        >
                          <Star className="w-4 h-4" />
                          {getPlaceLabel(item.place)}
                        </div>
                        <div className={`text-lg font-bold ${getPlaceColor(item.place)}`}>
                          {getPrizeAmount(item.place)}
                        </div>
                      </div>

                      {/* Project Image */}
                      {item.image && (
                        <div className="mb-3 rounded-lg overflow-hidden">
                          <img
                            src={item.image}
                            alt={item.title}
                            className="w-full h-32 object-cover"
                          />
                        </div>
                      )}

                      {/* Project Info */}
                      <div className="mb-3 flex-grow">
                        <h4 className="text-lg font-bold text-white mb-1 overflow-hidden" style={{
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical'
                        }}>
                          {item.title}
                        </h4>
                        <p className="text-gray-400 text-sm mb-1">
                          by {item.creator}
                        </p>
                        <p className="text-xs text-gray-300 overflow-hidden" style={{
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical'
                        }}>
                          {item.description}
                        </p>
                      </div>

                      {/* Likes */}
                      <div className="flex items-center gap-2 mb-3">
                        <Heart className="w-4 h-4 text-red-400 fill-current" />
                        <span className="text-white text-sm font-semibold">{item.votes} likes</span>
                      </div>

                      {/* Bottom Section - Qualifications and Project Link */}
                      <div className="mt-auto">
                        {/* Qualifications */}
                        <div className="border-t border-gray-600 pt-3 mb-3">
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              {item.hasActiveReferrals ? (
                                <CheckCircle className="w-4 h-4 text-green-400" />
                              ) : (
                                <XCircle className="w-4 h-4 text-red-400" />
                              )}
                              <span className="text-xs text-gray-300">
                                {t("contest.conditionActiveReferrals")}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              {item.hasLikedProject ? (
                                <CheckCircle className="w-4 h-4 text-green-400" />
                              ) : (
                                <XCircle className="w-4 h-4 text-red-400" />
                              )}
                              <span className="text-xs text-gray-300">
                                {t("contest.conditionLikedProject")}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Project Link */}
                        {item.projectUrl && (
                          <div>
                            <a
                              href={item.projectUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className={`inline-flex items-center gap-1 px-3 py-1 rounded-lg bg-gradient-to-r ${getPlaceGradient(
                                item.place
                              )} text-white text-sm font-semibold hover:opacity-90 transition-opacity`}
                            >
                              View Project
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </>
      )}

      {/* No Qualified Participants Message - Moved to Bottom */}
      <div className="text-center mt-12 bg-gradient-to-br from-red-900/20 to-orange-900/20 border border-red-500/30 rounded-2xl p-8 max-w-4xl mx-auto">
        <div className="flex items-center justify-center mb-6">
          <XCircle className="w-16 h-16 text-red-400 mr-4" />
          <div className="text-left">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-2">
              {t("contest.firstPlace")}, {t("contest.secondPlace")}, {t("contest.thirdPlace")}
            </h3>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-xl md:text-2xl font-bold text-red-400">
            {t("contest.noTopPrizesQualified")}
          </h4>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto leading-relaxed">
            {t("contest.noTopPrizesMessageShort")}
          </p>

          <div className="bg-gray-800/50 rounded-lg p-4 mt-6">
            <h5 className="text-sm font-semibold text-white mb-3">
              {t("contest.qualificationsMet")}
            </h5>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="flex items-center gap-2">
                <XCircle className="w-5 h-5 text-red-400" />
                <span className="text-sm text-gray-300">
                  {t("contest.conditionActiveReferrals")}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <XCircle className="w-5 h-5 text-red-400" />
                <span className="text-sm text-gray-300">
                  {t("contest.conditionLikedProject")}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
