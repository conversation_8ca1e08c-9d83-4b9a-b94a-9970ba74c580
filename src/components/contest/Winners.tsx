import React from "react";
import { useTranslation } from "react-i18next";
import { CheckCircle, XCircle, Star, Sparkles } from "lucide-react";

interface Winner {
  id: string;
  title: string;
  creator: string;
  creatorId: string;
  description: string;
  tech: string[];
  votes: number;
  image: string;
  projectUrl: string;
  hasActiveReferrals: boolean;
  hasLikedProject: boolean;
  place: number;
}

interface WinnersProps {
  winners: Winner[];
}

export const Winners: React.FC<WinnersProps> = ({ winners }) => {
  const { t } = useTranslation();

  const getPlaceColor = (place: number) => {
    switch (place) {
      case 1:
        return "text-green-400";
      case 2:
        return "text-blue-400";
      case 3:
        return "text-purple-400";
      default:
        return "text-white";
    }
  };

  const getPlaceGradient = (place: number) => {
    switch (place) {
      case 1:
        return "from-green-400 to-green-600";
      case 2:
        return "from-blue-400 to-blue-600";
      case 3:
        return "from-purple-400 to-purple-600";
      default:
        return "from-gray-400 to-gray-600";
    }
  };

  const getPrizeAmount = (place: number) => {
    switch (place) {
      case 1:
        return "$10,000";
      case 2:
        return "$5,000";
      case 3:
        return "$1,000";
      default:
        return "";
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-8">
      {/* Winners Announcement Header */}
      <div className="text-center mb-12">
        <h2 className="rexton-light text-3xl md:text-5xl text-white mb-6 relative inline-flex items-center justify-center gap-4">
          <Sparkles className="w-8 h-8 md:w-12 md:h-12 text-biela-accent animate-pulse" />
          {t("contest.winnersAnnouncement")}
          <Sparkles className="w-8 h-8 md:w-12 md:h-12 text-biela-accent animate-pulse" />
        </h2>
        <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto">
          {t("contest.congratulationsMessage")}
        </p>
      </div>

      {/* Winners Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {winners.slice(0, 3).map((winner, index) => (
          <div
            key={winner.id}
            className={`relative bg-gradient-to-br ${getPlaceGradient(
              winner.place
            )} p-1 rounded-2xl transform hover:scale-105 transition-all duration-300 ${
              winner.place === 1 ? "md:scale-110 order-2 md:order-1" : ""
            } ${winner.place === 2 ? "order-1 md:order-2" : ""} ${
              winner.place === 3 ? "order-3" : ""
            }`}
          >
            <div className="bg-biela-dark-blue rounded-2xl p-6 h-full">
              {/* Place Badge */}
              <div className="flex items-center justify-between mb-4">
                <div
                  className={`inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r ${getPlaceGradient(
                    winner.place
                  )} text-white font-bold`}
                >
                  <Star className="w-5 h-5" />
                  {winner.place === 1 && t("contest.firstPlace")}
                  {winner.place === 2 && t("contest.secondPlace")}
                  {winner.place === 3 && t("contest.thirdPlace")}
                </div>
                <div className={`text-2xl font-bold ${getPlaceColor(winner.place)}`}>
                  {getPrizeAmount(winner.place)}
                </div>
              </div>

              {/* Project Image */}
              {winner.image && (
                <div className="mb-4 rounded-lg overflow-hidden">
                  <img
                    src={winner.image}
                    alt={winner.title}
                    className="w-full h-48 object-cover"
                  />
                </div>
              )}

              {/* Project Info */}
              <div className="mb-4">
                <h3 className="text-xl font-bold text-white mb-2 overflow-hidden" style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}>
                  {winner.title}
                </h3>
                <p className="text-gray-400 mb-2">
                  by {winner.creator}
                </p>
                <p className="text-sm text-gray-300 overflow-hidden" style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 3,
                  WebkitBoxOrient: 'vertical'
                }}>
                  {winner.description}
                </p>
              </div>

              {/* Votes */}
              <div className="flex items-center gap-2 mb-4">
                <Star className="w-5 h-5 text-yellow-400 fill-current" />
                <span className="text-white font-semibold">{winner.votes} votes</span>
              </div>

              {/* Qualifications */}
              <div className="border-t border-gray-600 pt-4">
                <h4 className="text-sm font-semibold text-white mb-3">
                  {t("contest.qualificationsMet")}
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    {winner.hasActiveReferrals ? (
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-400" />
                    )}
                    <span className="text-sm text-gray-300">
                      {t("contest.conditionActiveReferrals")}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {winner.hasLikedProject ? (
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-400" />
                    )}
                    <span className="text-sm text-gray-300">
                      {t("contest.conditionLikedProject")}
                    </span>
                  </div>
                </div>
              </div>

              {/* Project Link */}
              {winner.projectUrl && (
                <div className="mt-4">
                  <a
                    href={winner.projectUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`inline-flex items-center gap-2 px-4 py-2 rounded-lg bg-gradient-to-r ${getPlaceGradient(
                      winner.place
                    )} text-white font-semibold hover:opacity-90 transition-opacity`}
                  >
                    View Project
                  </a>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
