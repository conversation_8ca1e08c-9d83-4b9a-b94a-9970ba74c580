import React from "react";
import { useTranslation } from "react-i18next";
import { CheckCircle, XCircle, Star, Sparkles } from "lucide-react";

interface Winner {
  id: string;
  title: string;
  creator: string;
  creatorId: string;
  description: string;
  tech: string[];
  votes: number;
  image: string;
  projectUrl: string;
  hasActiveReferrals: boolean;
  hasLikedProject: boolean;
  place: number;
  isQualified?: boolean;
}

interface WinnersProps {
  winners: Winner[];
}

export const Winners: React.FC<WinnersProps> = ({ winners }) => {
  const { t } = useTranslation();

  const getPlaceColor = (place: number, isQualified: boolean = true) => {
    if (!isQualified) return "text-gray-500";

    switch (place) {
      case 1:
        return "text-green-400";
      case 2:
        return "text-blue-400";
      case 3:
        return "text-purple-400";
      case 4:
        return "text-green-400"; // 4th place gets 1st place styling
      case 5:
        return "text-blue-400";  // 5th place gets 2nd place styling
      case 6:
        return "text-purple-400"; // 6th place gets 3rd place styling
      default:
        return "text-yellow-400";
    }
  };

  const getPlaceGradient = (place: number, isQualified: boolean = true) => {
    if (!isQualified) return "from-gray-500 to-gray-700";

    switch (place) {
      case 1:
        return "from-green-400 to-green-600";
      case 2:
        return "from-blue-400 to-blue-600";
      case 3:
        return "from-purple-400 to-purple-600";
      case 4:
        return "from-green-400 to-green-600"; // 4th place gets 1st place styling
      case 5:
        return "from-blue-400 to-blue-600";  // 5th place gets 2nd place styling
      case 6:
        return "from-purple-400 to-purple-600"; // 6th place gets 3rd place styling
      default:
        return "from-yellow-400 to-yellow-600";
    }
  };

  const getPrizeAmount = (place: number, isQualified: boolean = true) => {
    if (!isQualified) return "N/A";

    switch (place) {
      case 1:
        return "$10,000";
      case 2:
        return "$5,000";
      case 3:
        return "$1,000";
      case 4:
        return "$10,000"; // 4th place gets 1st place prize
      case 5:
        return "$5,000";  // 5th place gets 2nd place prize
      case 6:
        return "$1,000";  // 6th place gets 3rd place prize
      default:
        return "Prize";
    }
  };

  const getPlaceLabel = (place: number, isQualified: boolean = true) => {
    if (!isQualified) {
      switch (place) {
        case 1:
          return t("contest.firstPlace");
        case 2:
          return t("contest.secondPlace");
        case 3:
          return t("contest.thirdPlace");
        default:
          return `${place}th PLACE`;
      }
    }

    switch (place) {
      case 4:
        return t("contest.firstPlace"); // 4th place becomes 1st
      case 5:
        return t("contest.secondPlace"); // 5th place becomes 2nd
      case 6:
        return t("contest.thirdPlace"); // 6th place becomes 3rd
      default:
        return `${place - 3}th PLACE`; // Adjust numbering
    }
  };

  // Create unqualified placeholders for top 3
  const unqualifiedPlaces = [1, 2, 3].map(place => ({
    id: `unqualified-${place}`,
    title: t("contest.noQualifiedParticipant"),
    creator: t("contest.requirementsNotMet"),
    creatorId: "",
    description: t("contest.noQualificationDescription"),
    tech: [],
    votes: 0,
    image: "",
    projectUrl: "",
    hasActiveReferrals: false,
    hasLikedProject: false,
    place: place,
    isQualified: false
  }));

  // Combine unqualified places with actual winners (places 4-10)
  const allDisplayItems = [...unqualifiedPlaces, ...winners.slice(0, 7)];

  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-8">
      {/* Winners Announcement Header */}
      <div className="text-center mb-12">
        <h2 className="rexton-light text-3xl md:text-5xl text-white mb-6 relative inline-flex items-center justify-center gap-4">
          <Sparkles className="w-8 h-8 md:w-12 md:h-12 text-biela-accent animate-pulse" />
          {t("contest.winnersAnnouncement")}
          <Sparkles className="w-8 h-8 md:w-12 md:h-12 text-biela-accent animate-pulse" />
        </h2>
        <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto">
          {t("contest.congratulationsMessage")}
        </p>
      </div>

      {/* Top 3 Unqualified Places */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {allDisplayItems.slice(0, 3).map((item, index) => (
          <div
            key={item.id}
            className={`relative bg-gradient-to-br ${getPlaceGradient(
              item.place, item.isQualified
            )} p-1 rounded-2xl transform hover:scale-105 transition-all duration-300 ${
              item.place === 1 ? "md:scale-110 order-2 md:order-1" : ""
            } ${item.place === 2 ? "order-1 md:order-2" : ""} ${
              item.place === 3 ? "order-3" : ""
            } ${!item.isQualified ? "opacity-60" : ""}`}
          >
            <div className="bg-biela-dark-blue rounded-2xl p-6 h-full">
              {/* Place Badge */}
              <div className="flex items-center justify-between mb-4">
                <div
                  className={`inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r ${getPlaceGradient(
                    item.place, item.isQualified
                  )} text-white font-bold`}
                >
                  {item.isQualified ? (
                    <Star className="w-5 h-5" />
                  ) : (
                    <XCircle className="w-5 h-5" />
                  )}
                  {getPlaceLabel(item.place, item.isQualified)}
                </div>
                <div className={`text-2xl font-bold ${getPlaceColor(item.place, item.isQualified)}`}>
                  {getPrizeAmount(item.place, item.isQualified)}
                </div>
              </div>

              {/* Project Image */}
              {item.isQualified && item.image && (
                <div className="mb-4 rounded-lg overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-48 object-cover"
                  />
                </div>
              )}

              {!item.isQualified && (
                <div className="mb-4 rounded-lg overflow-hidden bg-gray-700 h-48 flex items-center justify-center">
                  <XCircle className="w-16 h-16 text-gray-500" />
                </div>
              )}

              {/* Project Info */}
              <div className="mb-4">
                <h3 className={`text-xl font-bold mb-2 overflow-hidden ${
                  item.isQualified ? 'text-white' : 'text-gray-500'
                }`} style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}>
                  {item.title}
                </h3>
                <p className="text-gray-400 mb-2">
                  by {item.creator}
                </p>
                <p className={`text-sm overflow-hidden ${
                  item.isQualified ? 'text-gray-300' : 'text-gray-500'
                }`} style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 3,
                  WebkitBoxOrient: 'vertical'
                }}>
                  {item.description}
                </p>
              </div>

              {/* Votes */}
              {item.isQualified && (
                <div className="flex items-center gap-2 mb-4">
                  <Star className="w-5 h-5 text-yellow-400 fill-current" />
                  <span className="text-white font-semibold">{item.votes} votes</span>
                </div>
              )}

              {/* Qualifications */}
              <div className="border-t border-gray-600 pt-4">
                <h4 className="text-sm font-semibold text-white mb-3">
                  {t("contest.qualificationsMet")}
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    {item.hasActiveReferrals ? (
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-400" />
                    )}
                    <span className="text-sm text-gray-300">
                      {t("contest.conditionActiveReferrals")}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {item.hasLikedProject ? (
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-400" />
                    )}
                    <span className="text-sm text-gray-300">
                      {t("contest.conditionLikedProject")}
                    </span>
                  </div>
                </div>
              </div>

              {/* Project Link */}
              {item.isQualified && item.projectUrl && (
                <div className="mt-4">
                  <a
                    href={item.projectUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`inline-flex items-center gap-2 px-4 py-2 rounded-lg bg-gradient-to-r ${getPlaceGradient(
                      item.place, item.isQualified
                    )} text-white font-semibold hover:opacity-90 transition-opacity`}
                  >
                    View Project
                  </a>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Actual Winners (Places 4-10) */}
      {allDisplayItems.length > 3 && (
        <>
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl text-white mb-4">
              {t("contest.qualifiedWinners")}
            </h3>
            <p className="text-gray-300">
              {t("contest.qualifiedWinnersMessage")}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {allDisplayItems.slice(3, 10).map((item, index) => (
              <div
                key={item.id}
                className={`relative bg-gradient-to-br ${getPlaceGradient(
                  item.place, item.isQualified
                )} p-1 rounded-xl transform hover:scale-105 transition-all duration-300`}
              >
                <div className="bg-biela-dark-blue rounded-xl p-4 h-full">
                  {/* Place Badge */}
                  <div className="flex items-center justify-between mb-3">
                    <div
                      className={`inline-flex items-center gap-1 px-3 py-1 rounded-full bg-gradient-to-r ${getPlaceGradient(
                        item.place, item.isQualified
                      )} text-white font-bold text-sm`}
                    >
                      <Star className="w-4 h-4" />
                      {getPlaceLabel(item.place, item.isQualified)}
                    </div>
                    <div className={`text-lg font-bold ${getPlaceColor(item.place, item.isQualified)}`}>
                      {getPrizeAmount(item.place, item.isQualified)}
                    </div>
                  </div>

                  {/* Project Image */}
                  {item.image && (
                    <div className="mb-3 rounded-lg overflow-hidden">
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-full h-32 object-cover"
                      />
                    </div>
                  )}

                  {/* Project Info */}
                  <div className="mb-3">
                    <h4 className="text-lg font-bold text-white mb-1 overflow-hidden" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      {item.title}
                    </h4>
                    <p className="text-gray-400 text-sm mb-1">
                      by {item.creator}
                    </p>
                    <p className="text-xs text-gray-300 overflow-hidden" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      {item.description}
                    </p>
                  </div>

                  {/* Votes */}
                  <div className="flex items-center gap-2 mb-3">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-white text-sm font-semibold">{item.votes} votes</span>
                  </div>

                  {/* Qualifications */}
                  <div className="border-t border-gray-600 pt-3">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-400" />
                        <span className="text-xs text-gray-300">
                          {t("contest.conditionActiveReferrals")}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-400" />
                        <span className="text-xs text-gray-300">
                          {t("contest.conditionLikedProject")}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Project Link */}
                  {item.projectUrl && (
                    <div className="mt-3">
                      <a
                        href={item.projectUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`inline-flex items-center gap-1 px-3 py-1 rounded-lg bg-gradient-to-r ${getPlaceGradient(
                          item.place, item.isQualified
                        )} text-white text-sm font-semibold hover:opacity-90 transition-opacity`}
                      >
                        View Project
                      </a>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};
