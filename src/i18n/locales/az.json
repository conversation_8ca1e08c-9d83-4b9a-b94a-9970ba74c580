{"meta": {"home": {"title": "biela.dev | Süni İntellektlə İşləyən Veb və Tətbiq Qurucusu – Promptlarla Qur", "description": "biela.dev ilə ideyalarınızı canlı veb saytlara və ya tətbiqlərə çevirin. Fərdi rəqəmsal məhsullar yaratmaq üçün süni intellekt tərəfindən idarə olunan promptlardan istifadə edin"}, "privacy": {"title": "biela.dev <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "biela.dev-in şəxsi məlumatlarınızı necə topladığını, istifadə etdiyini və qoruduğunu anlayın."}, "terms": {"title": "biela.dev <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "biela.dev-in süni intellektlə işləyən inkişaf platformasından istifadə üçün şərtlər və qaydaları nəzərdən keçirin"}, "changelogSection": {"title": "biela.dev Değişiklik Günlüğü - Her Zaman. İleriye. Gelişiyor.", "description": "biela.dev'in her yeni s<PERSON><PERSON><PERSON> nasıl gel<PERSON> an<PERSON>ın"}, "competitionterms": {"title": "Biela İnkişaf Müsabiqəsinin Şərtləri və Qaydaları", "description": "Biela İnkişaf Müsabiqəsinə qoşulun—Biela.dev və TeachMeCode İnstitutu tərəfindən təşkil edilən bir aylıq rəsmi yarışma. Rəqəmsal layihələrinizi nümayiş etdirin, icma dəstəyi ilə tanının və ən yaxşı nəticələr uğrunda yarışın. Son təqdim etmə tarixi 14 may 2025-dir."}, "contest": {"title": "biela.dev <PERSON><PERSON><PERSON><PERSON> 2025 – Qalibler <PERSON><PERSON>", "description": "biela.dev musabiqesinin qaliblerini tanıyın ve en yuksek onurlari qazanan heyretverici suni intellektla yaradılmıs lay<PERSON> kesfedin"}, "howTo": {"title": "Biela.dev-dən necə istifadə etmək olar – Süni İntellekt dəstəklənən tətbiqlər yaratmaq üçün tam bələdçi", "description": "Kod yazmadan və sürətli şəkildə veb tətbiqlər yaratmaq üçün Biela.dev-dən necə istifadə etməyi öyrənin. Dərsliklər və praktik məsləhətlərlə dolu tam addım-addım bələdçi.", "keywords": "biela, biela.dev, necə istifadə etmək, süni intellekt tətbiqləri ya<PERSON>, k<PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> də<PERSON>", "ogTitle": "Biela.dev-dən necə istifadə etmək olar – Addım-addım bələdçi", "ogDescription": "Biela.dev ilə AI tətbiqlərini necə yaratmağı öyrənin. Fikirdən istifadəyə qədər, heç bir kod yazmağa ehtiyac yoxdur.", "twitterTitle": "Biela.dev-dən necə istifadə etmək olar – Tam bələdçi", "twitterDescription": "Biela.dev üzərində kodsuz AI tətbiqləri yaradın. Bu sadə addım-addım bələdçini izləyin!"}}, "navbar": {"home": "<PERSON>", "demo": "Demo", "community": "İcma", "affiliate": "Partnyor Proqramı", "partners": "Partnyorlar", "contest": "<PERSON>ibe <PERSON>dl<PERSON>ş<PERSON><PERSON>", "subscriptions": "Abunəliklə<PERSON>", "pricing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "counter": {"days": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hours": "Saatlar", "minutes": "Dəqi<PERSON><PERSON><PERSON><PERSON><PERSON>", "seconds": "San<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "centisec": "Sentisaniyə"}, "hero": {"title": "İdeyanızı dəqiqələr ərzində canlı vebsayt və ya tətbiqə çevirin", "subtitle": "Əgər onu <1>təsəvvür edə bilirsiniz</1>, onu <5>kodlaya bilərsiniz</5>.", "subtitle2": "Pulsuz başlayın. Hər şeyi kodlayın. Hər sorğu ilə bacarıqlarınızı bir fürsətə çevirin.", "timeRunningOut": "Vaxt tükənir!", "launchDate": "WoodSnake Versiyası", "experimentalVersion": "Beta buraxılışı 15 aprel 2025-ci ildə", "earlyAdopter": "<PERSON><PERSON><PERSON><PERSON> bura<PERSON>ılışdan əvvəl erkən istifadəçi üstünlüklərini ələ keçirmək üçün indi qoşulun", "tryFree": "Pul<PERSON>z <PERSON>ı<PERSON>n", "seeAiAction": "İA-nın fəali<PERSON>tini gö<PERSON>ün", "tellBiela": "Bieladan yaratmasını istəyin", "inputPlaceholder": "Təsəvvür edə bilirsinizsə, BIELA onu kodlaya bilər, bu gün nə edək?", "chat": "Çat", "code": "Kod", "checklist": "<PERSON><PERSON><PERSON>a si<PERSON>ı", "checklistTitle": "<PERSON><PERSON><PERSON>a siyahılarından istifadə edərək", "checklistItems": {"trackDevelopment": "İnkişaf tapşırıqlarını izləyin", "setRequirements": "Layihə tələblərini müəyyənləşdirin", "createTesting": "Test protokolları yaradın"}, "registerNow": "İndi qeydiyyatdan keçin", "attachFiles": "<PERSON><PERSON><PERSON><PERSON><PERSON> fayllar əlavə edin", "voiceInput": "<PERSON><PERSON><PERSON><PERSON> girişdən istifadə edin", "enhancePrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> tə<PERSON>", "cleanupProject": "<PERSON><PERSON><PERSON>ni təmizləyin", "suggestions": {"weatherDashboard": "<PERSON>va proqnozu lövhəsi ya<PERSON>ın", "ecommercePlatform": "E-ticarət platforması qurun", "socialMediaApp": "Sosial media tətbiqi dizayn edin", "portfolioWebsite": "Portfolio vebsaytı yaradın", "taskManagementApp": "Tapşırıq idarəetmə tətbiqi yaradın", "fitnessTracker": "Fitness izləyicisi qurun", "recipeSharingPlatform": "Resept paylaşım platforması dizayn edin", "travelBookingSite": "<PERSON>ə<PERSON>ət rezervasiya saytı yaradın", "learningPlatform": "Öyrənmə platforması qurun", "musicStreamingApp": "Musiqi axını tətbiqi dizayn edin", "realEstateListing": "<PERSON><PERSON><PERSON> siyahısı yaradın", "jobBoard": "İş elanları saytı yaradın"}}, "videoGallery": {"barber": {"title": "<PERSON>ç kəsimi salonu vebsaytı", "description": "Biela.dev-in dəqiqələr ərzində tam saç kəsimi salonu vebsaytını necə yaratdığını görün"}, "tictactoe": {"title": "<PERSON><PERSON>u", "description": "İA-n<PERSON>n <PERSON><PERSON><PERSON>' oyununu adını demədən tez yaratdığını izləyin"}, "coffeeShop": {"title": "Kofe Dükanı vebsaytı", "description": "Biela.dev-in bir kofe dükanı yaratdığını görün"}, "electronicsStore": {"title": "Elektronika mağazası vebsaytı", "description": "Biela.dev-in dəqiqələr ərzində tam onlayn mağaza yaratdığını görün"}, "seoAgency": {"title": "SEO agentliyi vebsaytı", "description": "İA-nın SEO agentliyi vebsaytını necə yaratdığını izləyin"}}, "telegram": {"title": "Telegramımıza qoşulun", "subtitle": "Biela icması ilə əlaqə qurun", "description": "<PERSON><PERSON><PERSON><PERSON> dəstək alın, lay<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paylaşın və dünyanın dörd bir yanından İA həvəskarları ilə əlaqə qurun. <br/><br/> Bizim Telegram icması Biela.dev haqqında yenilikləri ən sürətli şəkildə izləmək üçün ən yaxşı yoldur.", "stats": {"members": "Üzvlər", "support": "Dəstək", "countries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projects": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "joinButton": "Telegram icmamıza qoşulun", "communityTitle": "Biela.dev <PERSON>", "membersCount": "üzvlər", "onlineCount": "hazı<PERSON> onlayn", "messages": {"1": "Biela.dev ilə bir saatdan az müddətdə e-ticarət saytımı işə saldım!", "2": "Çox möhtəşəmdir! Məhsul kataloqunu necə idarə etdiniz?", "3": "Biela bunu avtomatik olaraq hal etdi! Mən sadəcə istədiyimi təsvir etdim.", "4": "Hazırda portfel saytımı qururam. İA təklifləri inanılmazdır!", "5": "Kimsə yeni responsiv şablonları sınadı? Onlar mobil cihazlarda fantastik görünür."}}, "joinNetwork": {"title": "Şəbəkəmizə qoşulun", "subtitle": "Bağlanın. İnkişaf edin. Qazanın.", "affiliateProgram": "Partnyor Proqramı", "registerBring": "Qeydiyyatdan keçin və 3 partnyor gətirin", "aiCourse": "İA mühəndisliyi kursu", "courseDescription": "TeachMeCode ilə pulsuz əhatəli İA kursu", "digitalBook": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kitab", "bookDescription": "“Prompt Engineering Sənəti” eksklüziv rəqəmsal nəşr", "exclusiveBenefits": "Eksklüziv üstünlüklər", "benefitsDescription": "Ömürboyu qazanc və xüsusi üzv imtiyazları", "registerNow": "İndi qeydiyyatdan keçin", "teamEarnings": "Komanda qazancı", "buildNetwork": "Şəbək<PERSON><PERSON><PERSON> ya<PERSON>ın", "weeklyMentorship": "Həftəlik mentorluq", "teamBonuses": "Komanda <PERSON>", "startEarning": "Bu gün qazanc əldə etməyə başlayın", "upToCommission": "<PERSON><PERSON><PERSON><PERSON><PERSON> qədər"}, "partners": {"title": "<PERSON><PERSON><PERSON><PERSON> liderləri tərəfindən etibar edilir", "subtitle": "Qlobal innovatorlarla əməkdaşlıq edərək İA inkişafının gələcəyini formalaşdırırıq", "strategicPartnerships": "Strateji tərəf<PERSON>şlıqlar", "joinNetwork": "BIELA ilə inkişafı dəyişdirən partnyor və şirkətlər şəbəkəsinə qoşulun"}, "demo": {"title": "İA-nın real vaxtda tətbiq və vebsaytlar yaratdığını izləyin", "subtitle": "Bizneslərin Biela.dev ilə rəqəmsal varlıqlarını necə dəyişdirdiyini araşdırın", "seeInAction": "Biela.dev-in fəaliyyətini görün", "whatKind": "Hansı növ vebsayta ehtiyacınız var?", "describeWebsite": "Vebsayt ideyanızı təsvir edin (məsələn, 'Fotograf üçün portfel')", "generatePreview": "<PERSON><PERSON> baxı<PERSON> ya<PERSON>ın", "nextDemo": "Növbəti demo", "startBuilding": "Bu gün Biela.dev ilə yaratmağa və qazanc əldə etməyə başlayın"}, "footer": {"quickLinks": {"title": "<PERSON>z keçidlər", "register": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bielaInAction": "Biela.dev-in fəaliyyətini görün", "liveOnTelegram": "Biz Telegram-da canlıyıq", "affiliate": "Əməkdaşlıq əsaslı affiliate marketinq", "partners": "Partnyorlar"}, "legal": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "glassCard": {"copyright": "© 2025 TeachMeCode İnstitutu. Bütün hüqu<PERSON> qorunur.", "unauthorized": "Bu xidmətin ica<PERSON> isti<PERSON>d<PERSON>, surəti çıxarılması və ya yayılması, tam və ya qismən, açıq yazılı icazə olmadan qətiyyən qadağandır."}, "reservations": "TeachMeCode İnstitutu, Biela ilə bağlı intellektual mülkiyyət üzərində bütün hüquqları özündə saxlayır.", "terms": "<PERSON>d<PERSON><PERSON><PERSON>", "privacy": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "bottomBar": {"left": "2025 Biela.dev. %s tərəfindən təmin edilir © Bütün hüquqlar qorunur.", "allRights": "<PERSON><PERSON><PERSON><PERSON><PERSON> hü<PERSON> qor<PERSON>ur.", "right": "%s tərəfindən hazırlanıb"}}, "common": {"loading": "Yüklənir...", "error": "<PERSON><PERSON><PERSON> baş verdi", "next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submit": "Təsdiq et", "cancel": "<PERSON><PERSON>ğ<PERSON> et", "close": "Bağla", "loginNow": "İndi daxil olun", "verifyAccount": "Hesabı təsdiqləyin"}, "languageSwitcher": {"language": "Dil"}, "contest": {"bielaDevelopment": "Vibe Kodlaşdırma <PERSON>", "competition": "YARIŞMA", "firstPlace": "1-Cİ YER", "secondPlace": "2-Cİ YER", "thirdPlace": "3-CÜ YER", "currentLeader": "<PERSON><PERSON> lider:", "footerText": "Suni intellektla desteklenen inkisaf Biela.dev terefinden, TeachMeCode® Institutu terefinden desteklenir", "browseHackathonSubmissions": "Hackathon teqdimatlar<PERSON>na baxın", "winnersAnnouncement": "Yarışma Qalibleri Elan Edildi!", "congratulationsMessage": "Mohtesem qaliblərimizi tebrik edirik! Bu inanılmaz hackathonda iştirak eden her kese tesekkur edirik.", "conditionActiveReferrals": "3 aktiv referansı var", "conditionLikedProject": "Basqa bir lay<PERSON>eni beyendi", "winner": "<PERSON><PERSON><PERSON>", "qualificationsMet": "Kvalifikasiyalar:", "noQualifiedParticipant": "Kvalifikasiyalı İstirakci Yoxdur", "requirementsNotMet": "Telebler yer<PERSON>", "noQualificationDescription": "Bu movqe ucun hec bir istirakci kvalifikasiya teleblerini yerine yetirmeyib.", "qualifiedWinners": "🏆 Kvalifikasiyalı Qalibler", "qualifiedWinnersMessage": "Butun kvalifikasiya teleblerini yerine yetiren istirakcıları tebrik edirik!", "noTopPrizesQualified": "En Yaxsı Mukafatlar ucun Kvalifikasiyalı İstirakci Yoxdur", "noTopPrizesMessage": "<PERSON><PERSON><PERSON> k<PERSON>, hec bir istirakci ilk uc mukafat movqeyi ucun kvalifikasiya teleblerini yerine yetirmeyib. Kvalifikasiyalı olan butun istirakcılar qalan mukafat fondundan mukafatlandırılacaq.", "noTopPrizesMessageShort": "<PERSON><PERSON><PERSON> k<PERSON>, hec bir istirakci ilk uc mukafat movqeyi ucun kvalifikasiya teleblerini yerine yetirmeyib."}, "competition": {"header": {"mainTitle": "Sizin təklifiniz. Sizin üslubunuz. Sizin oyun meydançası.", "timelineTitle": "<PERSON><PERSON><PERSON>", "timelineDesc": "AI tərəfindən yaradılmış ən yaxşı layihənizi yaratmaq üçün bir ay", "eligibilityTitle": "<PERSON><PERSON><PERSON><PERSON>", "eligibilityDesc": "Lay<PERSON>ə təqdim edən aktiv Biela.dev hesabları", "prizesTitle": "Mükafatlar", "prizesDesc": "Qaliblər üçün 16,000 $ nağd pul mükafatı və pulsuz giriş", "votingTitle": "Səsvermə", "votingDesc": "İcma ə<PERSON>slı, təsdiq edilmiş istifadəçiyə yalnız bir səs", "tagline": "Yaradıcılığınızı göstərin. Dünyanın səs verməsinə imkan yaradın."}, "details": {"howToEnter": "Necə iştirak etmək olar", "enter": {"step1": {"title": "Biela.dev AI-dən istifadə edərək layihə yaradın", "desc": "Biela-nın güclü AI alətlərindən istifadə edərək innovativ layihənizi yaradın"}, "step2": {"title": "İstifadəçi panelinizə daxil olun", "desc": "\"Hakaton üçün dərc et\" d<PERSON><PERSON><PERSON><PERSON><PERSON> layihənizin yanında basın", "subdesc": "Maksimum 3 fərqli layihə təqdim edə bilərsiniz"}, "step3": {"title": "Biela sizin iştirakınızı emal edəcək:", "list1": "Layihənizin ekran görüntüsünü ç<PERSON>", "list2": "Tələblərinizin iştirak üçün uyğun olub olmadığını yoxlayın", "list3": "Xülasə və təsvir ya<PERSON>ın"}}, "prizeStructure": "Mükafat strukturu", "prizes": {"firstPlace": "1-ci yer", "firstPlaceValue": "$10,000", "secondPlace": "2-ci yer", "secondPlaceValue": "$5,000", "thirdPlace": "3-cü yer", "thirdPlaceValue": "$1,000", "fourthToTenth": "4-cü - 10-cu yer", "fourthToTenthValue": "30 gün <PERSON><PERSON> giri<PERSON>"}}, "qualification": {"heading": "<PERSON>ş<PERSON><PERSON>", "description": "Top 3 mükafatına ($10,000, $5,000 və $1,000) layiq olmaq ü<PERSON>ün aşağıdakı meyarları ödəməlisiniz:", "criteria1": "Ən az 3 aktiv tövsiyəyə sahib olmalısınız", "criteria2": "<PERSON>ərgi divarında ən az bir layihəyə 'bəyənmə' verməlisiniz", "proTipLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> məsləhət:", "proTipDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> nə qədər tez təqdim etsəniz, daha çox diqqət və səs alacaqsınız!"}, "voting": {"heading": "<PERSON><PERSON>s<PERSON><PERSON>ə qaydaları", "oneVotePolicyTitle": "<PERSON>ir nəfər, bir səs prinsipi", "oneVotePolicyDesc": "Yalnız öz layihənizə deyil, hər istifadəçi yalnız bir dəfə səs verə bilər", "accountVerificationTitle": "<PERSON><PERSON><PERSON><PERSON>n təsdiqlənməsi", "accountVerificationDesc": "Yalnız təsdiqlənmiş Biela.dev hesab sahibi olanlar səs verə bilər", "tieBreakingTitle": "Bərabərlik halında qərar", "tieBreakingDesc": "Bərabərlik halında, lay<PERSON>ə yaradı<PERSON>ısının qazandığı ümumi ulduz sayı (istifadə olunmuş və ya istifadə olunmamış) qəti qərarı verəcək", "howToVoteTitle": "Necə səs vermək olar", "howToVoteDesc": "Sərgi divarını nəzərdən keçirin, ya<PERSON><PERSON>mlanmış bütün AI tərəfindən yaradılmış layihələri araşdırın və sevdiyiniz layihəyə səs vermək üçün ürək ikonuna klikləyin."}}, "contestItems": {"projectShowcase": "<PERSON><PERSON><PERSON>", "loading": "Yüklənir...", "createBy": "Tə<PERSON><PERSON><PERSON><PERSON><PERSON>n <PERSON>ılıb ", "viewProject": "<PERSON><PERSON><PERSON><PERSON>", "stars": "<PERSON><PERSON><PERSON><PERSON>", "overview": "ümumi baxış", "keyFeatures": "<PERSON><PERSON><PERSON>", "exploreLiveProject": "Canlı layihəni araşdır", "by": "tərəfindən", "likeLimitReached": "Bəyənmə limiti çatdı", "youCanLikeMaximumOneProject": "Siz maksimum bir layihəni bəyənə bilərsiniz. Mövcud bəyəndiyinizi ləğv edib bu layihəni bəyənmək istəyirsinizmi?", "currentlyLikedProject": "<PERSON>-<PERSON><PERSON><PERSON><PERSON> bəyənilən layihə", "switchMyLike": "<PERSON>ə<PERSON><PERSON>n<PERSON><PERSON><PERSON> də<PERSON>", "mustLoggedIntoLikeProject": "Lay<PERSON>əni bəyənmək üçün daxil olmalısınız.", "signInToBielaAccountToVote": "Səsvermə prosesində iştirak etmək üçün Biela.dev hesabınıza daxil olun.", "yourAccountIsNotVerifiedYet": "Hesabınız hələ təsdiqlənməyib. Layihəni bəyənmək üçün hesabınızı təsdiqləyin.", "accountVerificationEnsureFairVoting": "<PERSON><PERSON><PERSON><PERSON>n təsdiqlənməsi ədalətli səsverməni təmin edir və müsabiqənin dürüstlüyünü qorumağa kömək edir.", "projectsSubmitted": "<PERSON>ə<PERSON><PERSON><PERSON> edil<PERSON>ə<PERSON>ər:", "unlikeThisProject": "Bu layihəni bəyənməmək", "likeThisProject": "Bu layihəni bəyənmək", "likes": "bəyən<PERSON>ələr", "like": "bəyən", "somethingWentWrong": "<PERSON>ir şey səhv getdi. <PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>, bir az sonra yenidən cəhd edin", "voteError": "Səsvermə xətası"}, "textsLiveYoutube": {"first": {"title": "Dəvətlisiniz!", "description": "Bu cümə canlı yayımlanacağıq — və bu sessiya <green>məqsəd</green>, <blue>dqiqlik</blue>, və <orange>təmiz vibe</orange> ilə qurmağa həsr olunub.", "secondDescription": "Öyrənin necə <green>vibe kodlaşdırmanı mənimsəmək</green>, <blue>yeni xüsusiyyətləri kəşf etmək</blue> hər kəsdən əvvəl və <orange>həqiqi layihələrin canlı inşasını izləmək</orange>.", "specialText": "<PERSON><PERSON>ya klikləyin və YouTube-da \"Bildir\" d<PERSON><PERSON><PERSON><PERSON><PERSON> basın — qaçırmayın."}, "second": {"title": "<PERSON>lk sən qur.", "description": "Bu cümə <green>vibe kodlaşdırmanı</green> tamamilə yeni səviyyəyə qaldırırıq — sənə göstərəcəyik necə <blue>niyyətlə yaratmaq</blue>, <orange>yeni xüsusiyyətləri açmaq</orange> və <green>hər zamankindən daha sürətli</green> işə başlamaq.", "secondDescription": "<green>Yeni t<PERSON></green>, <blue>yeni id<PERSON></blue> və <orange>inkişaf üçün yeni im<PERSON></orange>.", "specialText": "YouTube xatırlatmasını qurmaq üçün buraya klikləyin — və yaradıcıların növbəti dalğasına qoşulun."}, "third": {"title": "<PERSON><PERSON><PERSON>.", "description": "<PERSON><PERSON><PERSON> <green>Biela.dev canlı yayımımız</green> bu cümə keçiriləcək — və sən <blue>rəsmi olaraq dəvətlisən</blue>.", "secondDescription": "Biz <green>vibe kodlaşdırmaya</green> dərindən dalırıq, <blue>gü<PERSON><PERSON><PERSON> yeni xüsus<PERSON>yyətləri</blue> açıqlayırıq və sənə <orange>daha ağıllı, daha sürətli və daha böyük</orange> qurmağa kömək edirik.", "specialText": "<PERSON><PERSON>ya k<PERSON>ləyin və YouTube-da \"Bildir\"-ə toxunun — növ<PERSON><PERSON>ti böyük ideyan buradan başlaya bilər."}}, "stream": {"title": "<1><PERSON><PERSON><PERSON><PERSON></1> <3>Qurmaq</3> <PERSON><PERSON><PERSON><PERSON> olun.", "subtitle": "Cümə günü canlı yayımımıza qoşulun — xüsusiyyətlər, layihələr və eksklüziv yeniliklər.", "button": "YouTube xatırlatıcınızı təyin edin."}, "billings": {"modalTitle": "Plan Seçin", "modalSubtitle": "Limitsiz kodlaşdırmaya davam etmək üçün üç elastik plandan birini seçin.", "toggleMonthly": "Aylıq", "toggleYearly": "İllik (10% qənaət)", "mostPopular": "<PERSON><PERSON>", "tokensLowerCase": "<PERSON><PERSON><PERSON><PERSON>", "tokensUpperCase": "Tokenlər", "below": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unlimited": "LİMİTSİZ", "10Bonus": "10% Bonus", "currentPlan": "Cari Plan", "upgradePlan": "Planı Yüksəlt", "purchaseDetails": "Alış Təfərrüatları", "dateLabel": "<PERSON><PERSON>", "planLabel": "Plan", "amountLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>ğ", "whatsNext": "Növb<PERSON><PERSON>ı<PERSON>", "yourTokensAvailable": "To<PERSON><PERSON><PERSON><PERSON> artıq he<PERSON> mövcuddur", "purchaseDetailsStored": "Alış təfərrüatları hesabınızda saxlanılır", "viewPurchaseHistory": "<PERSON><PERSON>ş ta<PERSON>ə<PERSON>zi fakturalaşdırma bölməsində görə bilərsiniz", "thankYou": "Təş<PERSON>kk<PERSON>r edirik!", "purchaseSuccessful": "Alış uğurla tamamlandı", "startCoding": "Kodlaşdırmağa Başla", "month": "ay", "year": "il"}, "feature": {"basic_ai": "Əsas Süni İntellekt İnkişafı", "community_support": "<PERSON><PERSON><PERSON>", "standard_response": "<PERSON><PERSON>", "basic_templates": "<PERSON><PERSON><PERSON>", "advanced_ai": "İnkişaf etmiş Süni İntellekt", "priority_support": "Prioritet Dəstək", "fast_response": "S<PERSON>rətli Cavab Vaxtı", "premium_templates": "Premium Şablonlar", "team_collaboration": "Komanda Əməkdaşlığı", "enterprise_ai": "Korporativ Süni İntellekt İnkişafı", "support_24_7": "24/7 Prioritet Dəstək", "instant_response": "<PERSON><PERSON><PERSON>hal Cavab Vaxtı", "custom_templates": "<PERSON><PERSON>rdi <PERSON>", "advanced_team": "İnkişaf etmiş Komanda Xüsusiyyətləri", "custom_integrations": "Fərdi İnteqrasiyalar", "big_context_window": "Böyük Kontekst Pəncərəsi - Orta ölçüdəkindən 5 dəfə böyük", "code_chat_ai": "Kod və Çat Süni İntellektinə Giriş", "tokens_basic": "Təxminən 3 təqdimat saytı üçün kifayət qədər token", "standard_webcontainer": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "medium_context_window": "<PERSON><PERSON> Kontekst Pəncərə<PERSON> <PERSON> <PERSON><PERSON>, blog və ya əsas brauzer oyunu üçün uyğundur", "basic_support": "Standart Cavab Vaxtı ilə Əsas Dəstək", "supabase_integration": "Supabase İnteqrasiyası", "project_sharing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "project_download": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ü<PERSON>ə<PERSON>", "project_deployment": "Yerləşdirmə Xüsusiyyəti", "custom_domain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tokens_creator_plus": "Təxminən 7 təqdimat saytı üçün kifayət qədər token", "advanced_support": "Daha Sürətli Cavab Vaxtı ilə İnkişaf etmiş Dəstək", "prioritary_support": "<PERSON>əyi<PERSON> edilmiş Prioritet Dəstək", "early_feature_access": "Yeni Xüsusiyyətlərə Erkən Giriş", "human_cto": "Biznesiniz üçün Təyin edilmiş İnsan CTO", "community_promotion": "Bizim İcma ilə Biznesinizi Təşviq Edin"}, "JoinUsLive": "Canlı qoşulun", "howToUseBiela": {"subtitle": "Biela necə istifadə edilir", "title": "D<PERSON>qi<PERSON><PERSON><PERSON><PERSON><PERSON> İç<PERSON>ə İzah", "description": "Hər bir <PERSON>yanın necə işlədiyinə qısa izahlar"}, "slides": {"newHistoryFeature": "Biela-nın YENİ TARİX xüsusiyyətini kəşf edin!", "newHistoryFeatureDescription": "TARİX SEKMƏSİ layihənizin əvvəlki versiyalarını geri qaytarmağa və bərpa etməyə imkan verərək kodlaşdırma axışınızı nəzarətdə saxlamağa kömək edir!", "fixPreview": "Biela-da <PERSON><PERSON>ışı Necə Düzəltmək Olar!", "fixPreviewDescription": "<PERSON> bələdçiyə baxın və hər şeyin necə qurduğunuz kimi göründüyünə əmin olun.", "signUp": "Biela-da <PERSON><PERSON><PERSON>eydiyyatdan Keçmək Olar!", "signUpDescription": "biela.dev-də yenisiniz? Tez qeydiyyat və hesabı təsdiqləmə bələdçisini izləyin!", "buildWebsite": "Biela ilə Vebsayt Necə Qurulur!", "buildWebsiteDescription": "biela.dev ilə sıfırdan vebsayt qurmağı və işə salmağı öyrənin — heç bir kod yazmadan.", "downloadAndImport": "Biela-da Layihə və Çatları Necə Yükləmək və İdxal Etmək Olar!", "downloadAndImportDescription": "Bu addım-addım təlimatda tamamladığınız layihələri necə yükləməyi və saxlanılmış çatları iş sahəsinə necə idxal etməyi öyrənəcəksiniz—beləliklə işinizə qaldığınız yerdən davam edə bilərsiniz.", "shareProject": "BIELA Layihənizi Necə Paylaşmaq Olar!", "shareProjectDescription": "Layihənizi peşəkar kimi paylaşmağa hazırsınız? Tam bələdçiyə baxın və başlayın.", "launchProject": "Layihənizi Öz Domeninizdə Necə Başlatmaq Olar!", "launchProjectDescription": "Bu videoda biz saytınızı onlayn vəziyyətə gətirmək üçün lazım olan sadə addımları izah edirik — qurulmadan dərc etməyə qədər hər şeyi əhatə edirik.", "deployProject": "Layihənizi Necə Yerləşdirmək və Aktiv Etmək Olar!", "deployProjectDescription": "Bu tez bələdçi saytınızı, tətbiqinizi və ya panelinizi yerləşdirməyin bütün mərhələlərindən sizi addım-addım keçirəcək — sürətli və asan şəkildə."}, "maintenance": {"title": "Biz problemi həll edirik", "description": "Biela.dev-in təhlükəsizliyi üçün təhlükəli problemlər həll edilir. Biz problemi həll edirik", "text": "Problem həll edin"}, "planCard": {"descriptionBeginner": "<PERSON>lk layihələrini quran və veb inkişafı öyrənən yeni başlayanlar üçün mükəmməldir", "descriptionCreator": "<PERSON>ha ink<PERSON>af etmiş layihələr üçün daha çox güc və elastiklik tələb edən yaradıcılar üçün idealdır", "descriptionProfessional": "Tət<PERSON>q<PERSON>ər hazırlayan peşəkar inkişafçılar və komandalar üçün tam həll", "perMillionTokens": "$ {{price}} / hər 1M token üçün", "dedicatedCto": "Təyin edilmiş CTO - 24 saat", "tokens": "Tokenlər", "perMonth": "<PERSON><PERSON><PERSON><PERSON>", "perYear": "illik", "freeTokens": "{{ tokens }} Pulsuz Token", "monthly": "Aylıq", "yearly": "İllik", "save": "10% Qənaət"}, "faqLabel": "Tez-tez verilən suallar", "faqHeading": "Ən çox verilən suallara baxın", "faqSubheading": "Başla. Anla. <PERSON> et", "whatAreTokensQuestion": "To<PERSON><PERSON>ər nədir?", "whatAreTokensAnswer1": "Tokenlər süni intellektin layihənizi necə emal etdiyini göstərir. Biela.dev-də AI-yə sorğu göndərdiyiniz zaman tokenlər istifadə olunur. Bu proses zamanı layihə fayllarınız mesaja daxil edilir ki, süni intellekt kodunuzu başa düşsün. Layihəniz nə qədər böyük, cavab nə qədər uzun olsa, hər mesaj üçün o qədər çox token lazım olacaq.", "whatAreTokensAnswer2": "<PERSON><PERSON><PERSON>, veb konteyner istifadə etdiyiniz hər dəqiqəyə görə, inkişaf mühitinin xərclərini qarşılamaq üçün hesabınızdan az sayda token tutulur.", "freePlanTokensQuestion": "<PERSON><PERSON><PERSON>z planla neçə token alıram?", "freePlanTokensAnswer": "Pulsuz plan istifadəçiləri hər gün 200,000 token alır və bu hər gün yenilənir.", "outOfTokensQuestion": "Tokenlərim qurtarsa nə olacaq?", "outOfTokensAnswer": "Narahat olmayın! Menyu → <PERSON><PERSON><PERSON><PERSON><PERSON> bölməsinə keçərək istədiyiniz zaman dərhal əlavə token ala bilərs<PERSON>z.", "changePlanQuestion": "Planımı sonradan dəyişə bilərəm?", "changePlanAnswer": "<PERSON>ə<PERSON> <PERSON> istə<PERSON><PERSON>ən vaxt. Sad<PERSON><PERSON><PERSON> Menyu → Ödəniş bölməsinə keçin və planınızı ehtiyaclarınıza uyğun olaraq dəyişin.", "rolloverQuestion": "İstifadə olunmamış tokenlər növbəti günə keçir?", "rolloverAnswer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>ər son istifadə tarixində sıfırlanır və ötürülmür — hər gün onlardan maksimum istifadə etməyə çalışın!", "exploreHowToUseBiela": "Bieladan necə istifadə etməli", "new": "<PERSON><PERSON>", "joinVibeCoding": "Vibe Kodlaşdırma Hakatonuna qoşulun", "whatDoYouWantToBuild": "NƏ İSTƏYİRSƏN QURASAN?", "imaginePromptCreate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et. <PERSON><PERSON><PERSON><PERSON> ver. <PERSON>.", "levelOfAmbition": "Ambisiya səviyyən nədir?", "startGrowScale": "Başla. İnkişaf et. Miqyasla.", "calloutBar": {"start": "<PERSON><PERSON><PERSON><PERSON>n planlar pulsuz başlayır", "tokens": "Gündə 200,000 token", "end": ". <PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur. <PERSON><PERSON><PERSON><PERSON> qur<PERSON>a başla."}, "atPriceOf": "bu qiymətə", "startFreeNoCreditCard": "Pulsuz Başla - Kredit Kartı Tələb Olunmur", "SignIn": "Daxil ol", "Register": "Qeydiyyatdan keç", "Affiliates": "T<PERSON><PERSON>əfdaşlar", "UsernameRequired": "İstifadəçi adı tələb olunur", "PasswordRequired": "Ş<PERSON><PERSON>ə tələb olunur", "MissingUsernamePasswordOrTurnstile": "İstifadəçi adı, şifrə və ya Turnstile tokeni çatışmır", "LoginSuccess": "G<PERSON>ş uğurludur!", "LoginFailed": "<PERSON><PERSON><PERSON> uğursuz oldu", "EmailRequired": "<PERSON><PERSON> tələb olu<PERSON>r", "EmailInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> olmasa etibarlı email ünvanı daxil edin", "CaptchaRequired": "<PERSON><PERSON><PERSON><PERSON>t olmasa CAPTCHA-nı tamamlayın", "ResetLinkSent": "Email ünvanınıza sıfırlama keçidi göndərildi.", "ResetFailed": "Sıfırlama keçidi göndərilə bilmədi", "BackToLogin": "<PERSON><PERSON><PERSON><PERSON> qayıt", "EmailPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "SendResetLink": "Sıfırlama keçidini göndər", "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checkingYourAuthenticity": "Həqiqiliyiniz yo<PERSON>lanılır", "ToUseBielaDev": "Biela.dev-dən istifadə etmək üçün mövcud hesabınıza daxil olun və ya aşağıdakı variantlardan biri ilə qeydiyyatdan keçin", "Sign in with Google": "Google ilə daxil olun", "Sign in with GitHub": "Git<PERSON><PERSON> ilə daxil olun", "Sign in with Email and Password": "Email və Şifrə ilə daxil olun", "DontHaveAnAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur?", "SignUp": "Qeydiyyatdan keç", "ByUsingBielaDev": "Biela.dev-dən istifadə etməklə istifadə məlumatlarının toplanmasına razılıq verirsiniz.", "EmailOrUsernamePlaceholder": "Email/İstifadəçi adı", "passwordPlaceholder": "Şifrə", "login.loading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LoginInToProfile": "Profilinizə daxil olun", "login.back": "<PERSON><PERSON>", "forgotPassword": "Ş<PERSON><PERSON><PERSON>ni unutmusunuz?", "PasswordsMismatch": "Şif<PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON> gə<PERSON>ir.", "PhoneRequired": "Telefon nömrəsi tələb olunur", "ConfirmPasswordRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON> təsdiqləyin", "AcceptTermsRequired": "Xidmət Şərtləri və Məxfilik Siyasəti ilə razılaşmalısınız", "RegistrationFailed": "Qeydiyyat uğursuz oldu", "EmailConfirmationSent": "Email təsdiqi göndərildi. Zə<PERSON><PERSON>t olmasa emailinizi təsdiqləyin və daxil olun.", "RegistrationServerError": "Qeydiyyat uğ<PERSON> oldu (server cavabı false).", "SomethingWentWrong": "Nə isə səhv getdi", "CheckEmailConfirmRegistration": "Qeydiyyatınızı təsdiqləmək üçün emailinizi yoxlayın", "EmailConfirmationSentText": "Təsdiqləmə keçidi olan bir email göndərdik.", "LoginToProfile": "Profilə daxil olun", "username": "İstifadəçi adı", "email": "Email", "PasswordPlaceholder": "Şifrə", "ConfirmPasswordPlaceholder": "Şifrəni Təsdiqlə", "AcceptTermsPrefix": "<PERSON><PERSON><PERSON> razıyam", "TermsOfService": "<PERSON>d<PERSON><PERSON><PERSON>", "AndSeparator": "və", "PrivacyPolicy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CreateAccount": "<PERSON><PERSON><PERSON>", "Back": "<PERSON><PERSON>", "SignInWithGoogle": "Google ilə daxil olun", "SignInWithGitHub": "Git<PERSON><PERSON> ilə daxil olun", "SignInWithEmailAndPassword": "Email və Şifrə ilə daxil olun", "translation": {"AIModel": "Süni İntellekt Modeli", "UpgradePlan": "premium plan", "PremiumBadge": "PREMİUM", "Active": "Aktiv", "projectInfo.features": "<PERSON>ü<PERSON><PERSON><PERSON><PERSON><PERSON>tlər", "Stats": "Statistikalar", "Performance": "Performans", "Cost": "<PERSON><PERSON><PERSON><PERSON><PERSON>ğ", "UpgradeTooltip": "Aşağıdakı planı əldə etməklə kilidi açın ", "UpgradeTooltipSuffix": "paketi", "chat": "Çat", "code": "Kod"}, "extendedThinkingTooltip": "<PERSON><PERSON><PERSON> vermədən əvvəl süni intellektin daha dərindən düşünməsinə icazə verin", "extendedThinkingTooltipDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> qənaət etmək üçün dərin düşünməyi deaktiv edin", "AIModel": "Süni İntellekt Modeli", "UpgradePlan": "premium plan", "PremiumBadge": "PREMİUM", "Active": "Aktiv", "projectInfo.features": "<PERSON>ü<PERSON><PERSON><PERSON><PERSON><PERSON>tlər", "Stats": "Statistikalar", "Performance": "Performans", "Cost": "<PERSON><PERSON><PERSON><PERSON><PERSON>ğ", "UpgradeTooltip": "Aşağıdakı planı əldə etməklə kilidi açın ", "UpgradeTooltipSuffix": "paketi", "HowBielaWorks": "BIELA Necə İşləyir", "WatchPromptLaunch": "İzləyin. Yazın. Başladın.", "SearchVideos": "Kitabxanada axtar...", "NewReleased": "YENİ ÇIXIŞ", "Playing": "Oynadılır", "NoVideosFound": "<PERSON><PERSON> bir video tapılmadı", "TryAdjustingYourSearchTerms": "Axtarış sözlərinizi dəyişməyi sınayın", "feedback": {"title": {"issue": "<PERSON><PERSON>", "feature": "Xüsus<PERSON><PERSON><PERSON><PERSON><PERSON> Tələb Edin"}, "description": {"issue": "Bir xəta tapdınız və ya problem yaşayırsınız? Bizə bildirin və ən qısa zamanda düzəldəcəyik.", "feature": "Yeni xüsusiyyət üçün bir ideyanız var? Platformamızı təkmilləşdirmək üçün təkliflərinizi eşitmək istərdik."}, "success": {"title": "Təş<PERSON>kk<PERSON>r edirik!", "issue": "<PERSON><PERSON>z uğ<PERSON> göndərildi.", "feature": "Xüsusiyyət tələbiniz uğurla göndərildi."}, "form": {"fullName": {"label": "Tam ad", "placeholder": "Tam adınızı daxil edin"}, "email": {"label": "E-poçt ünvanı", "placeholder": "E-poçt ünvanınızı daxil edin"}, "suggestion": {"labelIssue": "Problem təsviri", "labelFeature": "Xüsusi<PERSON><PERSON><PERSON>t tək<PERSON>fi", "placeholderIssue": "Yaşadığınız problemi təsvir edin...", "placeholderFeature": "Görmək istədiyiniz xüsusiyyəti təsvir edin..."}, "screenshots": {"label": "Ekran görü<PERSON> (İstəyə bağlı)", "note": "Problemi daha yaxşı başa düşməyimiz üçün ekran görüntüləri əlavə edin", "drop": "Yükləmək üçün klikləyin və ya faylları buraya atın", "hint": "PNG, JPG, JPEG — hər biri 10MB-a qədər", "count": "{{count}} <PERSON><PERSON><PERSON><PERSON>"}}, "buttons": {"cancel": "<PERSON><PERSON>ğ<PERSON> et", "submitting": "Göndərilir...", "submitIssue": "<PERSON><PERSON>", "submitFeature": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "errors": {"fullNameRequired": "Tam ad tələb olunur", "fullNameNoNumbers": "Tam ad rəqəmlər ehtiva edə bilməz", "emailRequired": "E-poçt ünvanı tələb olunur", "emailInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> olmasa etibarlı e-poçt ünvanı daxil edin", "suggestionRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> olmasa probleminizi və ya təklifinizi təsvir edin"}}, "changelog": {"title": "D<PERSON>yiş<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Həmişə. İnkişaf edir. İrəli.", "TotalReleases": "Ümumi Buraxılışlar", "ActiveUsers": "Aktiv İstifadəçilər", "FeaturesAdded": "<PERSON><PERSON><PERSON> Olunan <PERSON>i<PERSON>tlər", "BugsFixed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lər", "shortCallText": "Təklifləriniz var və ya bir səhv aşkar etdiniz? Fikirlərinizi eşitmək istərdik!", "reportIssue": "<PERSON><PERSON>", "requestFeature": "Xüsusiy<PERSON>ət Tələb Et", "types": {"feature": "Xüsusiy<PERSON><PERSON><PERSON>", "improvement": "Yaxşılaşdırma", "bugfix": "Səhv Düzəlişi", "ui/ux": "İnterfeys/İstifadəçi Təcrübəsi", "announcement": "<PERSON><PERSON>", "general": "Ümumi"}, "versions": {"v3.2.3": {"date": "26 İyun 2025", "changes": {"0": "İstifadəçi panelində bəzi UI/UX problemləri dü<PERSON>.", "1": "WebContainer sabitliyi artırıldı.", "2": "AI Diff rejimi kiçik və böyük faylları fərqli şəkildə idarə etmək üçün yaxşılaşdırıldı."}}, "v3.2.2": {"date": "24 İyun 2025", "changes": {"0": "İstifadəçi təcrübəsini yaxşılaşdırmaq üçün ön ofis yenidən təşkil və dizayn edildi.", "1": "<PERSON>u güclü dəyişikliklər jurnal səhifəsi əlavə edildi.", "2": "WebContainer-in yenidən bağlanma alqoritmi yaxşılaşdırıldı.", "3": "Qovluqlara əlavə olaraq ZIP faylları ilə layihə yükləməyə dəstək əlavə olundu.", "4": "API çağırışlarının performansı daha yaxşı indeksləşdirmə ilə artırıldı."}}, "v3.2.1": {"date": "24 İyun 2025", "changes": {"0": "IDE: İstifadəçi panelindəki \"Qovluqlar\" bö<PERSON><PERSON><PERSON> daha yaxşı təcrübə üçün yenidən dizayn edildi.", "1": "Content Studio: <PERSON>da şə<PERSON><PERSON>ri k<PERSON>alama<PERSON> artıq <PERSON>ü<PERSON>."}}, "v3.2.0": {"date": "17 İyun 2025", "changes": {"0": "AI Diff Rejimi (Eksperimental): Süni intellekt artıq bütün faylı yenidən yaratmaq əvəzinə yalnız dəyişiklikləri yazır. Bu performansı artırır və token istifadəsini optimallaşdırır. Bu xüsusiyyəti Layihə Ayarları → İdarəetmə Mərkəzi bölməsindən aktivləşdirə və ya deaktiv edə bilərsiniz."}}, "v3.1.6": {"date": "12 İyun 2025", "changes": {"0": "IDE: Abun<PERSON><PERSON><PERSON><PERSON>r və əlavə tokenlər üçün kupon kodu sahəsi əlavə olundu."}}, "v3.1.5": {"date": "11 İyun 2025", "changes": {"0": "IDE: \"Lay<PERSON>ə Paylaşımı\" prosesi üzrə müxtəlif düzəlişlər tətbiq olundu."}}, "v3.1.4": {"date": "10 İyun 2025", "changes": {"0": "IDE: Kriptovalyuta daxilolmaları və Stripe inteqrasiyası problemləri həll edildi, abun<PERSON><PERSON><PERSON><PERSON>r yenidən yoxlanıldı."}}, "v3.1.3": {"date": "9 İyun 2025", "changes": {"0": "AI: <PERSON><PERSON> ya<PERSON>ı gecikmə üçün Gemini ilə birbaşa API inteqrasiyası həyata keçirildi və sınaqdan keçirildi."}}, "v3.1.2": {"date": "6 İyun 2025", "changes": {"0": "Affiliate: Yeni <PERSON> məlumat pəncərəsi dizaynına uyğun ödəniş tapşırıqları əlavə olundu.", "1": "Content Studio: İstifadəçilər artıq AI-yə şəkilləri linklərlə birlikdə göndərmək istədiklərinə qərar verə bilərlər (defolt olaraq 'yox')."}}, "v3.1.1": {"date": "5 İyun 2025", "changes": {"0": "Content Studio: <PERSON><PERSON><PERSON> lay<PERSON>ə üçün ayrıca təşkil edilmişdir."}}, "v3.1.0": {"date": "3 İyun 2025", "changes": {"0": "🚀 BIELA.dev üçün Böyük Yeniləmə Gəldi! 🚀", "1": "AI bu gün səhərdən 5x daha sürətli kod yazır!", "2": "Terminal UI indi daha təmiz və axıcıdır.", "3": "Supabase bağlantısı indi daha intuitivdir.", "4": "<PERSON>əta idarəsi yeniləndi — AI köməyi ilə və ya əl ilə seçim o<PERSON>.", "5": "Layihə Ayarlarında yeni İdarəetmə Mərkəzi və Unit Test interfeysi əlavə olundu.", "6": "Yükləmələr kod tam şəkildə göndəriləndən sonra başlayır — dəqiq və tam ixrac üçün."}}, "v3.0.0": {"date": "3 İyun 2025", "changes": {"0": "🚀 BIELA.dev <PERSON> Da<PERSON> Gü<PERSON>lü! 🚀", "1": "Terminal interfeysi yeniləndi — daha estetik və istifadəsi rahat.", "2": "npm əmrləri üçün bir klik düymələri əlavə olundu.", "3": "AI xətaları avtomatik anlayır və yardım göstərir.", "4": "Söhbətdə fayl adına klik etdikdə birbaşa WebContainer-də açılır."}}, "v2.1.1": {"date": "30 May 2025", "changes": {"0": "IDE: WebContainer üçün '<PERSON><PERSON><PERSON>' düyməsi əlavə olundu. Əl ilə dəyişiklik edildikdə görünür və GitHub-a 'commit' və 'push' əməliyyatı həyata keçirir.", "1": "Panel: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> layihələrin adını dəyişərkən baş verən xəta aradan qaldırıldı.", "2": "WebContainer: <PERSON><PERSON><PERSON><PERSON> sız<PERSON>ı ilə bağlı təcili düzəliş tətbiq olundu."}}, "v2.1.0": {"date": "28 May 2025", "changes": {"0": "🚀 BIELA.dev Yeniləməsi — 7 Yeni Təkmilləşdirmə! 🚀", "1": "IDE: Layihələrə öz domeninizi interfeys vasitəsilə bağlamaq imkanı əlavə edildi.", "2": "Yayımlanmış layihələrə baxış indi tək kliklə mümkündür.", "3": "Kopyalanmış layihələrin xərc hesabla<PERSON>arı indi sıfırdan başlayır.", "4": "Prompt başına real vaxt xərc hesablaması əlavə edildi.", "5": "<PERSON><PERSON><PERSON> ta<PERSON><PERSON><PERSON>in bərpası yenidən işləyir və daha sabitdir.", "6": "Avtomatik sürüşmə indi daha axıcı və cavabvericidir.", "7": "<PERSON><PERSON> səhifələri indi yeni tab-da açılır."}}, "v2.0.4": {"date": "27 May 2025", "changes": {"0": "Panel: Ekran görüntüləri üçün avtomatik sürüşmə deaktiv edildi, istifadəçi tərəfindən idarəolunan oldu və yükləmə animasiyası əlavə olundu."}}, "v2.0.3": {"date": "20 May 2025", "changes": {"0": "Content Studio: Robot animasiyası indi tam ölçüdə göstərilir.", "1": "Affiliate: Firefox brauzerində görünüş <PERSON>əri həll <PERSON>."}}, "v2.0.2": {"date": "19 May 2025", "changes": {"0": "Content Studio: <PERSON><PERSON><PERSON> anda yüklənə bilən fayl sayı 10-dan 50-yə qədər artırıldı."}}, "v2.0.1": {"date": "18 May 2025", "changes": {"0": "Content Studio: <PERSON><PERSON><PERSON><PERSON><PERSON>r yalnız səhifə yenilənəndə dəyişirdi — indi dərhal yenilənir.", "1": "Redaktə düyməsi indi daha gö<PERSON>ü<PERSON>əndir.", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n faylların qovluğu son istifadə olunan qovluq olaraq avtomatik seçilir.", "3": "Sürüklə-burax əməliyyatında seçilmiş qovluq qorunur.", "4": "<PERSON>b ba<PERSON><PERSON><PERSON>ğının rəngi dəyişdirildi.", "5": "“Şəkil əlavə et” düyməsi indi “Layihədə istifadə et” adlanır.", "6": "Yü<PERSON><PERSON><PERSON> düyməsinin arxa fonu indi yaşıl rəngdədir.", "7": "Axtarış çubuğunun hündürlüyü uyğunluq üçün azaldıldı.", "8": "<PERSON><PERSON><PERSON><PERSON>, ax<PERSON><PERSON><PERSON> çubuğu göstərilmir."}}, "v2.0.0": {"date": "16 May 2025", "changes": {"0": "🚀 BIELA.DEV-də ƏSAS YENİLƏMƏLƏR! 🚀", "1": "Öz Web Container texnologiyamız artıq mövcuddur — tamamilə müstəqil!", "2": "Kod yaradılması üçün çoxsaylı AI modelləri əlavə edildi (o cümlədən Google Gemini).", "3": "Content Studio: <PERSON>ə<PERSON><PERSON> şəkillərinizi yük<PERSON><PERSON><PERSON><PERSON> layihələrdə istifadə edə bilərsiniz.", "4": "<PERSON><PERSON><PERSON>ımı artıq mü<PERSON> — əmək<PERSON><PERSON><PERSON><PERSON>q sadə<PERSON>.", "5": "Supabase layihələri ilə birbaşa bağlantı imkanı əlavə edildi."}}, "v1.0.5": {"date": "15 May 2025", "changes": {"0": "Ödənişlər: Stripe vasitəsilə ödəniş sistemi əlavə edildi.", "1": "Content Studio: <PERSON><PERSON> səhifədə <PERSON>ə<PERSON> yo<PERSON>, robot animasiyas<PERSON> göstəril<PERSON>."}}, "v1.0.4": {"date": "13 May 2025", "changes": {"0": "Content Studio: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> problem<PERSON><PERSON>."}}, "v1.0.3": {"date": "5 May 2025", "changes": {"0": "Content Studio istifadəyə verildi — media fayllarınızı saxlamaq və idarə etmək üçün bir yer.", "1": "Frontoffice: biela_frontoffice üçün schema.org dəstəyi əlavə olundu.", "2": "Komanda Paneli: Yeni üzv əlavə olunanda və ya mesaj gələndə bildiriş nöqtəsi göstərilir.", "3": "IDE: Menyudan açılan pəncərələr WebContainer-də müəyyən sahələrə kliklənəndə bağlanmırdı — bu düzəldildi."}}, "v1.0.2": {"date": "2 May 2025", "changes": {"0": "Affiliate: <PERSON>manda üzvü sahəsinin dizaynı yenidən quruldu.", "1": "Affiliate Paneli üçün ikonlar yeniləndi.", "2": "IDE: \"Supabase Connect\" d<PERSON><PERSON><PERSON><PERSON>in adı dəyişdirildi — indi \"Verilənlər Bazası\" adlanır və əlaqə yaradıldıqda mavi işarə göstərilir."}}, "v1.0.1": {"date": "1 May 2025", "changes": {"0": "IDE (Ayarlar bölməsi): Çat ayarlarında verilənlər bazası inteqrasiyası əlavə olundu.", "1": "Panel: İstifadəçi panelində səhifələmə düzəldildi."}}, "v1.0.0": {"date": "18 Aprel 2025", "changes": {"0": "🚀 Biela.dev-a Xoş Gəldiniz: Sizin Süni İntellektlə Gücləndirilmiş Kodlama Yardımçınız! 🚀", "1": "<PERSON><PERSON><PERSON> açıq b<PERSON> elan o<PERSON>u. Platformamız ilk gündən hamı üçün pulsuzdur!"}}}}}