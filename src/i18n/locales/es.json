{"meta": {"home": {"title": "biela.dev | Desarrollador de Web y Apps con IA – Construye con Prompts", "description": "Transforma tus ideas en sitios web o aplicaciones con biela.dev. <PERSON><PERSON><PERSON> prompts impulsados por IA para crear productos digitales personalizados sin esfuerzo"}, "privacy": {"title": "Política de Privacidad de biela.dev", "description": "Comprende cómo biela.dev recopila, utiliza y protege tu información personal."}, "terms": {"title": "Términos de Servicio de biela.dev", "description": "Revisa los términos y condiciones para usar la plataforma de desarrollo impulsada por IA de biela.dev"}, "changelogSection": {"title": "Registro de cambios de biela.dev - Siempre. Evolucionando. Hacia adelante.", "description": "Aprende cómo biela.dev mejora con cada versión"}, "competitionterms": {"title": "Términos y Condiciones del Concurso de Desarrollo Biela", "description": "Únete al Concurso de Desarrollo de Biela—un desafío oficial de un mes organizado por Biela.dev y el Instituto TeachMeCode. Muestra tus creaciones digitales, gana reconocimiento a través de la participación comunitaria y compite por los máximos honores. Fecha límite de entrega: 14 de mayo de 2025."}, "contest": {"title": "Competencia de Desarrollo biela.dev 2025 – Ganadores Anunciados", "description": "Conoce a los ganadores de la competencia de biela.dev y descubre los increíbles proyectos construidos con IA que obtuvieron los máximos honores"}, "howTo": {"title": "Cómo usar Biela.dev – Guía completa para crear aplicaciones impulsadas por IA", "description": "Descubre cómo usar Biela.dev para crear aplicaciones web rápidamente y sin código. Una guía paso a paso con tutoriales y consejos prácticos.", "keywords": "biela, biela.dev, cómo usar biela, crear apps IA, sin código, desarrollo rápido, gu<PERSON> biela, tutorial biela", "ogTitle": "Cómo usar Biela.dev – Guía paso a paso", "ogDescription": "Aprende a crear apps con IA usando Biela.dev. De la idea al lanzamiento, sin necesidad de programar.", "twitterTitle": "Cómo usar Biela.dev – Guía completa", "twitterDescription": "Crea apps con IA en Biela.dev sin programar. ¡Sigue esta guía paso a paso!"}}, "navbar": {"home": "<PERSON><PERSON>o", "demo": "Demo", "community": "Comunidad", "affiliate": "<PERSON><PERSON><PERSON><PERSON>", "partners": "<PERSON><PERSON><PERSON>", "contest": "Hackathon de Vibe Coding", "subscriptions": "Suscripciones", "pricing": "<PERSON><PERSON><PERSON>"}, "counter": {"days": "Días", "hours": "<PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "seconds": "<PERSON><PERSON><PERSON>", "centisec": "Centise<PERSON>dos"}, "hero": {"title": "Únete ahora para asegurar beneficios exclusivos antes del lanzamiento oficial.", "subtitle": "<PERSON> puedes <1>imaginarlo</1>, puedes <5><PERSON><PERSON><PERSON></5>.", "subtitle2": "Empieza gratis. Codifica todo. Convierte tus habilidades en cada solicitud en una oportunidad.", "timeRunningOut": "¡El tiempo se acaba!", "launchDate": "Versión WoodSnake", "experimentalVersion": "Lanzamiento beta el 15 de abril de 2025", "earlyAdopter": "Únete ahora para aprovechar las ventajas exclusivas antes del lanzamiento oficial.", "tryFree": "<PERSON><PERSON><PERSON> gratis", "seeAiAction": "Ver la IA en acción", "tellBiela": "Dile a Biela que cree", "inputPlaceholder": "Si puedes imaginarlo, BIELA puede programarlo, ¿qué haremos hoy?", "chat": "Cha<PERSON>", "code": "Código", "checklist": "Lista de verificación", "checklistTitle": "Utiliza listas de verificación para", "checklistItems": {"trackDevelopment": "Seguimiento de tareas de desarrollo", "setRequirements": "Establecer los requisitos del proyecto", "createTesting": "Crear protocolos de prueba"}, "registerNow": "Regístrate ahora", "attachFiles": "Adjunta archivos a tu prompt", "voiceInput": "Utiliza dictado por voz", "enhancePrompt": "Mejora tu prompt", "cleanupProject": "Limpia el proyecto", "suggestions": {"weatherDashboard": "Crea un panel meteorológico", "ecommercePlatform": "Construye una plataforma de comercio electrónico", "socialMediaApp": "Diseña una aplicación de redes sociales", "portfolioWebsite": "Genera un sitio web como portafolio", "taskManagementApp": "Crea una aplicación de gestión de tareas", "fitnessTracker": "Construye un rastreador de fitness", "recipeSharingPlatform": "Diseña una plataforma para compartir recetas", "travelBookingSite": "Crea un sitio de reservas de viajes", "learningPlatform": "Desarrolla una plataforma de aprendizaje", "musicStreamingApp": "Diseña una aplicación de streaming musical", "realEstateListing": "Crea un listado inmobiliario", "jobBoard": "Constituye una bolsa de trabajo"}}, "videoGallery": {"barber": {"title": "Sitio web para peluquería", "description": "Mira cómo Biela.dev crea un sitio web completo para una peliquería en minutos"}, "tictactoe": {"title": "Juego de TRES EN RAYA", "description": "Observa a la IA crear el juego de TRES EN RAYA más rápido de lo que puedes decir TRES EN RAYA"}, "coffeeShop": {"title": "Sitio web para cafetería", "description": "Mira cómo Biela.dev crea la web para una cafetería"}, "electronicsStore": {"title": "Sitio web para tienda de electrónica", "description": "Observa cómo Biela.dev crea una tienda en línea completa en minutos"}, "seoAgency": {"title": "Sitio web para agencia SEO", "description": "Observa a la IA crear un sitio web para una agencia SEO"}}, "telegram": {"title": "Únete a nuestro Telegram", "subtitle": "Conéctate con la comunidad Biela", "description": "Obtén soporte instantáneo, comparte tus proyectos y conéctate con entusiastas de la IA de todo el mundo. <br/><br/> Nuestra comunidad de Telegram es la forma más rápida de mantenerse actualizado con Biela.dev.", "stats": {"members": "Mi<PERSON><PERSON><PERSON>", "support": "Soporte", "countries": "Países", "projects": "Proyectos"}, "joinButton": "Únete a nuestra comunidad de Telegram", "communityTitle": "Comunidad Biela.dev", "membersCount": "<PERSON><PERSON><PERSON><PERSON>", "onlineCount": "en línea", "messages": {"1": "¡Acabo de lanzar mi sitio de comercio electrónico con Biela.dev en menos de una hora!", "2": "¡Se ve increíble! ¿Cómo manejaste el catálogo de productos?", "3": "¡Biela lo manejó automáticamente! Solo describí lo que quería.", "4": "Estoy construyendo una web para mi portafolio en este momento. ¡Las sugerencias de IA son increíbles!", "5": "¿Alguien ha probado las nuevas plantillas responsivas? Se ven fantásticas en el móvil."}}, "joinNetwork": {"title": "Únete a nuestra red", "subtitle": "Conecta. Crece. Gana.", "affiliateProgram": "Programa de afiliados", "registerBring": "Regístrate y trae 3 afiliados", "aiCourse": "Curso de ingeniería de IA", "courseDescription": "Curso completo gratuito de IA con TeachMeCode", "digitalBook": "Libro digital", "bookDescription": "\"El arte de la ingeniería de prompts\" edición digital exclusiva", "exclusiveBenefits": "Beneficios exclusivos", "benefitsDescription": "Ganancias de por vida y privilegios especiales para miembros", "registerNow": "Regístrate ahora", "teamEarnings": "Ganancias del equipo", "buildNetwork": "Construye tu red", "weeklyMentorship": "Mentoría semanal", "teamBonuses": "Bonificaciones de equipo", "startEarning": "Comienza a ganar hoy", "upToCommission": "HASTA Comisión"}, "partners": {"title": "Respaldado por líderes de la industria", "subtitle": "Colaborando con pioneros globales para dar forma al futuro del desarrollo impulsado por IA.", "strategicPartnerships": "Alianzas estratégicas", "joinNetwork": "Únete a la red creciente de socios y empresas que están transformando el desarrollo con BIELA."}, "demo": {"title": "Mira cómo la IA crea aplicaciones y sitios web en tiempo real", "subtitle": "Explora cómo las empresas están transformando su presencia digital con Biela.dev", "seeInAction": "Ver Biela.dev en acción", "whatKind": "¿Qué tipo de sitio web necesitas?", "describeWebsite": "Describe tu idea de sitio web (ej., 'Un portafolio para un fotógrafo')", "generatePreview": "Generar vista previa", "nextDemo": "Siguiente demo", "startBuilding": "Comienza a construir y ganar con Biela.dev hoy"}, "footer": {"quickLinks": {"title": "En<PERSON><PERSON>", "register": "Registrarse", "bielaInAction": "Ver Biela.dev en acción", "liveOnTelegram": "Estamos en vivo en Telegram", "affiliate": "Marketing de Afiliación Colaborativo", "partners": "<PERSON><PERSON><PERSON>"}, "legal": {"title": "Legal", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. Todos los derechos reservados.", "unauthorized": "El uso, reproducción o distribución no autorizada de este servicio, total o parcial, sin permiso por escrito explícito, está estrictamente prohibido."}, "reservations": "TeachMeCode Institute se reserva todos los derechos legales sobre la propiedad intelectual asociada a Biela.", "terms": "Términos del Servicio", "privacy": "Política de Privacidad"}, "bottomBar": {"left": "2025 Biela.dev. Impulsado por %s © Todos los derechos reservados.", "allRights": "Todos los derechos reservados.", "right": "Desarrollado por %s"}}, "common": {"loading": "Cargando...", "error": "Hay un error", "next": "Siguient<PERSON>", "previous": "Anterior", "submit": "Enviar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "loginNow": "Iniciar <PERSON><PERSON><PERSON> ah<PERSON>", "verifyAccount": "Verificar cuenta"}, "languageSwitcher": {"language": "Idioma"}, "contest": {"bielaDevelopment": "Hackathon de Vibe Coding", "competition": "COMPETENCIA", "firstPlace": "1.er LUGAR", "secondPlace": "2.º LUGAR", "thirdPlace": "3.er LUGAR", "currentLeader": "<PERSON><PERSON><PERSON> actual:", "footerText": "Desarrollo potenciado por IA de Biela.dev, con el respaldo del Instituto TeachMeCode®", "browseHackathonSubmissions": "Explorar en<PERSON><PERSON><PERSON>", "winnersAnnouncement": "¡Ganadores del Concurso Anunciados!", "congratulationsMessage": "¡Felicitaciones a nuestros increíbles ganadores! Gracias a todos los que participaron en este increíble hackathon.", "conditionActiveReferrals": "Tiene 3 referencias activas", "conditionLikedProject": "Le gustó otro proyecto", "winner": "Ganador", "qualificationsMet": "Calificaciones:"}, "competition": {"header": {"mainTitle": "Tu propuesta. Tu estilo. Tu campo de juego.", "timelineTitle": "CRONOGRAMA", "timelineDesc": "Un mes para crear tu mejor proyecto generado por IA", "eligibilityTitle": "ELEGIBILIDAD", "eligibilityDesc": "Cuentas activas de Biela.dev que envían un proyecto", "prizesTitle": "PREMIOS", "prizesDesc": "16.000 dólares en premios en efectivo más acceso gratuito para los ganadores", "votingTitle": "VOTACIÓN", "votingDesc": "Impulsado por la comunidad, un voto por usuario verificado", "tagline": "Deja que tu creatividad hable. Deja que el mundo vote."}, "details": {"howToEnter": "Cómo Participar", "enter": {"step1": {"title": "Crea un proyecto usando la IA de Biela.dev", "desc": "Utiliza las poderosas herramientas de IA de Biela para construir tu proyecto innovador"}, "step2": {"title": "Ve a tu panel de usuario", "desc": "<PERSON>z clic en \"Publicar para Hackathon\" junto a tu proyecto", "subdesc": "Puedes enviar hasta 3 proyectos diferentes"}, "step3": {"title": "Biela procesará tu participación:", "list1": "Toma una captura de pantalla de tu proyecto", "list2": "Verifica que tus propuestas cumplan con la elegibilidad", "list3": "Genera un resumen y descripción"}}, "prizeStructure": "Estructura de premios", "prizes": {"firstPlace": "1er <PERSON><PERSON>", "firstPlaceValue": "$10,000", "secondPlace": "2do Lugar", "secondPlaceValue": "$5,000", "thirdPlace": "3<PERSON> <PERSON>", "thirdPlaceValue": "$1,000", "fourthToTenth": "Del 4to al 10mo Lugar", "fourthToTenthValue": "30 días de acceso ilimitado"}}, "qualification": {"heading": "Requisitos de Calificación", "description": "Para optar a los 3 primeros premios ($10,000, $5,000 y $1,000), necesitas cumplir con los siguientes criterios:", "criteria1": "Tener al menos 3 referencias activas", "criteria2": "Dar like a al menos un proyecto del muro de exhibición", "proTipLabel": "Consejo profesional:", "proTipDesc": "¡Cuanto antes envíes tu proyecto, mayor será la visibilidad y los votos que recibirá!"}, "voting": {"heading": "Directrices de Votación", "oneVotePolicyTitle": "Política de Un Solo Voto", "oneVotePolicyDesc": "Solo puedes votar una vez, y no por tu propio proyecto", "accountVerificationTitle": "Verificación de la Cuenta", "accountVerificationDesc": "Solo los usuarios con cuentas verificadas de Biela.dev pueden votar", "tieBreakingTitle": "Desempate", "tieBreakingDesc": "En caso de empate, el factor decisivo será el número total de estrellas obtenidas por el creador del proyecto (usadas o no)", "howToVoteTitle": "<PERSON><PERSON><PERSON>", "howToVoteDesc": "Revisa el muro de exhibición, explora todos los proyectos generados por IA publicados y haz clic en el ícono de corazón para votar por tu proyecto favorito."}}, "contestItems": {"projectShowcase": "Vitrina de proyectos", "loading": "Cargando...", "createBy": "<PERSON><PERSON>o por ", "viewProject": "Ver proyecto", "stars": "Estrellas", "overview": "visión general", "keyFeatures": "Características clave", "exploreLiveProject": "Explorar el proyecto en vivo", "by": "por", "likeLimitReached": "Límite de me gusta al<PERSON>zado", "youCanLikeMaximumOneProject": "Solo puedes dar me gusta a un proyecto. ¿Te gustaría quitar el me gusta del actual y dar me gusta a este en su lugar?", "currentlyLikedProject": "Proyecto actualmente con me gusta", "switchMyLike": "Cambiar mi me gusta", "mustLoggedIntoLikeProject": "Debes iniciar sesión para dar me gusta a un proyecto.", "signInToBielaAccountToVote": "Por favor, inicia sesión en tu cuenta de Biela.dev para participar en el proceso de votación.", "yourAccountIsNotVerifiedYet": "Tu cuenta aún no está verificada. Por favor, verifica tu cuenta para poder dar me gusta a un proyecto.", "accountVerificationEnsureFairVoting": "La verificación de la cuenta asegura una votación justa y ayuda a mantener la integridad de la competencia.", "projectsSubmitted": "Proyectos enviados:", "unlikeThisProject": "Ya no me gusta este proyecto", "likeThisProject": "Me gusta este proyecto", "likes": "me gusta", "like": "me gusta", "somethingWentWrong": "Algo salió mal. <PERSON><PERSON> favor, inténtelo de nuevo más tarde", "voteError": "Error de votación"}, "textsLiveYoutube": {"first": {"title": "¡Estás invitado!", "description": "Transmitiremos en vivo este viernes — esta sesión trata de construir con <green>propósito</green>, <blue>precisión</blue> y <orange>pasión creativa</orange>.", "secondDescription": "Aprende a <green>dominar el vibe coding</green>, <blue>descubrir nuevas funciones</blue> antes que nadie, y <orange>ver proyectos reales</orange> construirse en vivo.", "specialText": "Haz clic aquí y pulsa \"Notificarme\" en YouTube — no te lo pierdas."}, "second": {"title": "Sé el primero en crear.", "description": "Este viernes llevamos el <green>vibe coding</green> a otro nivel — te mostramos cómo <blue>crear con intención</blue>, <orange>desbloquear nuevas funciones</orange> y lanzar <green>más rápido que nunca</green>.", "secondDescription": "<green><PERSON>s builds</green>, <blue>nuevas ideas</blue> y <orange>nuevas oportunidades</orange> para crecer.", "specialText": "Haz clic aquí para activar el recordatorio de YouTube — y sé parte de la próxima ola de creadores."}, "third": {"title": "Reserva tu lugar.", "description": "Nuestro próximo <green>Biela.dev livestream</green> será este viernes — y estás <blue>oficialmente invitado</blue>.", "secondDescription": "Nos sumergimos en <green>vibe coding</green>, revelamos <blue>poderosas nuevas funciones</blue> y te ayudamos a construir <orange>más inteligente, rápido y a gran escala</orange>.", "specialText": "Haz clic aquí y toca \"Notificarme\" en YouTube — tu próxima gran idea podría empezar aquí."}}, "stream": {"title": "<PERSON>é el <1>Primero</1> en <3>Construir</3>.", "subtitle": "Únete a nuestro livestream del viernes — características, proyectos y actualizaciones exclusivas.", "button": "Configura tu recordatorio en YouTube."}, "billings": {"modalTitle": "Elige un Plan", "modalSubtitle": "Elige entre nuestros tres planes flexibles para seguir programando sin límites.", "toggleMonthly": "<PERSON><PERSON><PERSON>", "toggleYearly": "<PERSON><PERSON> (ahorra 10%)", "mostPopular": "Más Popular", "tokensLowerCase": "tokens", "tokensUpperCase": "Tokens", "below": "abajo", "unlimited": "ILIMITADO", "10Bonus": "10% de Bonificación", "currentPlan": "Plan Actual", "upgradePlan": "Mejorar Plan", "purchaseDetails": "Detalles de la Compra", "dateLabel": "<PERSON><PERSON>", "planLabel": "Plan", "amountLabel": "Monto", "whatsNext": "¿Qué Sigue?", "yourTokensAvailable": "Tus tokens ya están disponibles en tu cuenta", "purchaseDetailsStored": "Los detalles de la compra se han guardado en tu cuenta", "viewPurchaseHistory": "Puedes ver tu historial de compras en la sección de facturación", "thankYou": "¡<PERSON><PERSON><PERSON>!", "purchaseSuccessful": "Tu compra fue exitosa", "startCoding": "Comenzar a Programar", "month": "mes", "year": "año"}, "feature": {"basic_ai": "Desarrollo básico de IA", "community_support": "Soporte comunitario", "standard_response": "Tiempo de respuesta estándar", "basic_templates": "Plantillas bás<PERSON>s", "advanced_ai": "Desarrollo avanzado de IA", "priority_support": "Soporte prioritario", "fast_response": "Tiempo de respuesta rápido", "premium_templates": "Plantillas premium", "team_collaboration": "Colaboración en equipo", "enterprise_ai": "Desarrollo de IA empresarial", "support_24_7": "Soporte prioritario 24/7", "instant_response": "Tiempo de respuesta instantáneo", "custom_templates": "Plantillas personalizadas", "advanced_team": "Funciones avanzadas para equipos", "custom_integrations": "Integraciones personalizadas", "big_context_window": "Ventana de contexto grande - 5 veces más grande que la mediana", "code_chat_ai": "Acceso a IA de código y chat", "tokens_basic": "Tokens suficientes para aprox. 3 sitios web de presentación", "standard_webcontainer": "Webcontainer <PERSON><PERSON><PERSON> de<PERSON>ado", "medium_context_window": "Ventana de contexto mediana - adecuada para un sitio web, un blog o un juego básico de navegador", "basic_support": "Soporte básico con tiempo de respuesta estándar", "supabase_integration": "Integración con Supabase", "project_sharing": "Comparte proyectos con tus amigos", "project_download": "Descarga tus proyectos", "project_deployment": "Función de despliegue", "custom_domain": "Conecta proyectos a tu propio dominio", "tokens_creator_plus": "Tokens suficientes para aprox. 7 sitios web de presentación", "advanced_support": "Soporte avanzado con tiempo de respuesta más rápido", "prioritary_support": "Soporte prioritario dedicado", "early_feature_access": "Acceso anticipado a nuevas funciones", "human_cto": "CTO humano dedicado para tu negocio", "community_promotion": "Promociona tu negocio con nuestra comunidad"}, "JoinUsLive": "Únete en vivo", "howToUseBiela": {"subtitle": "Cómo usar Biela", "title": "Explicado en minutos", "description": "Breves explicaciones de cómo funciona cada función"}, "slides": {"newHistoryFeature": "¡Descubre la nueva función de HISTORIAL de Biela!", "newHistoryFeatureDescription": "La pestaña HISTORIAL mantiene fluido tu proceso de Vibe Coding al permitirte revisar y restaurar cualquier versión anterior de tu proyecto. ¡Mantén el control total!", "fixPreview": "¡Cómo arreglar la vista previa en Biela!", "fixPreviewDescription": "Mira la guía completa y asegúrate de que todo se muestre exactamente como lo construiste.", "signUp": "¡Cómo registrarte en Biela!", "signUpDescription": "¿Nuevo en biela.dev? Mira esta guía rápida para aprender a registrarte y verificar tu cuenta.", "buildWebsite": "¡Cómo construir un sitio web con Biela!", "buildWebsiteDescription": "Aprende a construir y lanzar un sitio web desde cero usando biela.dev — sin escribir una sola línea de código.", "downloadAndImport": "¡Cómo descargar e importar proyectos y chats en Biela!", "downloadAndImportDescription": "En este recorrido paso a paso, aprenderás cómo descargar tus proyectos completados e importar chats guardados a tu espacio de trabajo — para que puedas continuar justo donde lo dejaste y mantener tu flujo de Vibe Coding en cualquier momento.", "shareProject": "¡Cómo compartir tu proyecto de Vibe Coding de BIELA!", "shareProjectDescription": "¿Listo para compartir tu proyecto como un profesional? Mira la guía completa y comienza.", "launchProject": "¡Cómo lanzar tu proyecto en tu propio dominio!", "launchProjectDescription": "En este video, recorremos los pasos simples para poner tu sitio en línea. Desde la configuración hasta la publicación final, obtendrás todo lo necesario para pasar con confianza de la construcción al lanzamiento.", "deployProject": "¡Cómo desplegar tu proyecto y ponerlo en línea!", "deployProjectDescription": "Esta guía rápida te lleva por cada paso para desplegar tu sitio, aplicación o panel — rápida y fluidamente."}, "maintenance": {"title": "Estamos trabajando en resolver el problema", "description": "Biela.dev está en modo de mantenimiento. Estamos trabajando para resolver el problema", "text": "<PERSON><PERSON><PERSON> por tu paciencia."}, "planCard": {"descriptionBeginner": "Perfecto para principiantes que están construyendo sus primeros proyectos y aprendiendo desarrollo web", "descriptionCreator": "Ideal para creadores que necesitan más potencia y flexibilidad para proyectos avanzados", "descriptionProfessional": "Solución completa para desarrolladores profesionales y equipos que crean aplicaciones", "perMillionTokens": "$ {{price}} / por cada millón de tokens", "dedicatedCto": "CTO dedicado - 24 horas", "tokens": "Tokens", "perMonth": "por mes", "perYear": "por año", "freeTokens": "{{ tokens }} Tokens gratuitos", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>", "save": "AHORRA 10%"}, "faqLabel": "Preguntas Frecuentes", "faqHeading": "Consulta nuestras preguntas más frecuentes", "faqSubheading": "Empieza. Comprende. Explora", "whatAreTokensQuestion": "¿Qué son los tokens?", "whatAreTokensAnswer1": "Los tokens son la forma en que la IA procesa tu proyecto. En Biela.dev, se utilizan tokens cuando envías un mensaje a la IA. Durante este proceso, los archivos de tu proyecto se incluyen en el mensaje para que la IA pueda entender tu código. Cuanto más grande sea tu proyecto y más larga la respuesta de la IA, más tokens se necesitarán por mensaje.", "whatAreTokensAnswer2": "Además, se descuenta una pequeña cantidad de tokens de tu cuenta por cada minuto que uses un contenedor web, para cubrir el costo del entorno de desarrollo.", "freePlanTokensQuestion": "¿Cuántos tokens recibo en el plan gratuito?", "freePlanTokensAnswer": "Cada usuario con el plan gratuito recibe 200.000 tokens por día, que se renuevan diariamente.", "outOfTokensQuestion": "¿Qué pasa si me quedo sin tokens?", "outOfTokensAnswer": "¡No te preocupes! Siempre puedes ir a Menú → Facturación y comprar más tokens al instante cuando los necesites.", "changePlanQuestion": "¿Puedo cambiar mi plan más adelante?", "changePlanAnswer": "Sí — en cualquier momento. Solo dirígete a Menú → Facturación para actualizar, reducir o cambiar de plan según tus necesidades.", "rolloverQuestion": "¿Se acumulan los tokens no usados?", "rolloverAnswer": "Actualmente, los tokens se restablecen en su fecha de vencimiento y no se acumulan, así que ¡aprovéchalos al máximo cada día!", "exploreHowToUseBiela": "Explora cómo usar Biela", "new": "Nuevo", "joinVibeCoding": "Únete al Hackathon de Vibe Coding", "whatDoYouWantToBuild": "¿QUÉ QUIERES CONSTRUIR?", "imaginePromptCreate": "Imagina. Escribelo. Crea.", "levelOfAmbition": "¿Cuál es tu nivel de ambición?", "startGrowScale": "Empieza. Crece. Escala.", "calloutBar": {"start": "Todos los planes comienzan gratis con", "tokens": "200,000 tokens por día", "end": ". Sin fricciones de registro. Solo empieza a crear."}, "atPriceOf": "al precio de", "startFreeNoCreditCard": "<PERSON><PERSON>za gratis - Sin tarjeta de crédito", "SignIn": "In<PERSON><PERSON>", "Register": "Registrarse", "Affiliates": "<PERSON><PERSON><PERSON><PERSON>", "UsernameRequired": "Se requiere nombre de usuario", "PasswordRequired": "Se requiere contraseña", "MissingUsernamePasswordOrTurnstile": "Falta nombre de usuario, contraseña o token de Turnstile", "LoginSuccess": "¡Inicio de sesión exitoso!", "LoginFailed": "Error al iniciar sesión", "EmailRequired": "Se requiere correo electrónico", "EmailInvalid": "Por favor, introduce una dirección de correo electrónico válida", "CaptchaRequired": "Por favor, completa el CAPTCHA", "ResetLinkSent": "Se ha enviado un enlace de restablecimiento a tu correo electrónico.", "ResetFailed": "No se pudo enviar el enlace de restablecimiento", "BackToLogin": "Volver al inicio de sesión", "EmailPlaceholder": "Tu correo electrónico", "SendResetLink": "Enviar enlace de restablecimiento", "loading": "Cargando", "checkingYourAuthenticity": "Verificando tu autenticidad", "ToUseBielaDev": "Para usar Biela.dev debes iniciar sesión en una cuenta existente o crear una utilizando una de las siguientes opciones", "Sign in with Google": "Iniciar se<PERSON><PERSON> con Google", "Sign in with GitHub": "Iniciar se<PERSON><PERSON> con GitHub", "Sign in with Email and Password": "Iniciar sesión con correo electrónico y contraseña", "DontHaveAnAccount": "¿No tienes una cuenta?", "SignUp": "Regístrate", "ByUsingBielaDev": "Al usar Biela.dev aceptas la recopilación de datos de uso.", "EmailOrUsernamePlaceholder": "Correo electrónico / Nombre de usuario", "passwordPlaceholder": "Contraseña", "login.loading": "Cargando", "LoginInToProfile": "Inicia sesión en tu perfil", "login.back": "Volver", "forgotPassword": "¿Olvidaste tu contraseña?", "PasswordsMismatch": "Las contraseñas no coinciden.", "PhoneRequired": "Se requiere número de teléfono", "ConfirmPasswordRequired": "Por favor, confirma tu contraseña", "AcceptTermsRequired": "Debes aceptar los Términos de Servicio y la Política de Privacidad", "RegistrationFailed": "Error en el registro", "EmailConfirmationSent": "Se ha enviado un correo electrónico de confirmación. Por favor, confirma tu correo y luego inicia sesión.", "RegistrationServerError": "El registro falló (el servidor devolvió false).", "SomethingWentWrong": "Algo salió mal", "CheckEmailConfirmRegistration": "Revisa tu correo para confirmar el registro", "EmailConfirmationSentText": "Te hemos enviado un correo con un enlace de confirmación.", "LoginToProfile": "Iniciar sesión en el perfil", "username": "Nombre de usuario", "email": "Correo electrónico", "PasswordPlaceholder": "Contraseña", "ConfirmPasswordPlaceholder": "Confirmar con<PERSON>", "AcceptTermsPrefix": "Estoy de acuerdo con los", "TermsOfService": "Términos de Servicio", "AndSeparator": "y", "PrivacyPolicy": "Política de Privacidad", "CreateAccount": "<PERSON><PERSON><PERSON> cuenta", "Back": "Volver", "SignInWithGoogle": "Iniciar se<PERSON><PERSON> con Google", "SignInWithGitHub": "Iniciar se<PERSON><PERSON> con GitHub", "SignInWithEmailAndPassword": "Iniciar sesión con correo electrónico y contraseña", "translation": {"AIModel": "Modelo de IA", "UpgradePlan": "un plan premium", "PremiumBadge": "PREMIUM", "Active": "Activo", "projectInfo.features": "Características", "Stats": "Estadísticas", "Performance": "Rendimiento", "Cost": "Costo", "UpgradeTooltip": "Desbloquea actualizando a ", "UpgradeTooltipSuffix": "paquete", "chat": "Cha<PERSON>", "code": "Código"}, "extendedThinkingTooltip": "Permite que la IA piense más profundamente antes de responder", "extendedThinkingTooltipDisabled": "Desactiva el pensamiento extendido para ahorrar recursos", "AIModel": "Modelo de IA", "UpgradePlan": "un plan premium", "PremiumBadge": "PREMIUM", "Active": "Activo", "projectInfo.features": "Características", "Stats": "Estadísticas", "Performance": "Rendimiento", "Cost": "Costo", "UpgradeTooltip": "Desbloquea actualizando a ", "UpgradeTooltipSuffix": "paquete", "HowBielaWorks": "Cómo funciona BIELA", "WatchPromptLaunch": "Mira. Escribe. Lanza.", "SearchVideos": "Buscar en la biblioteca...", "NewReleased": "NUEVO LANZAMIENTO", "Playing": "<PERSON>rod<PERSON><PERSON><PERSON>", "NoVideosFound": "No se encontraron videos", "TryAdjustingYourSearchTerms": "Intenta ajustar tus términos de búsqueda", "feedback": {"title": {"issue": "Reportar un problema", "feature": "Solicitar una función"}, "description": {"issue": "¿Encontraste un error o estás experimentando un problema? Cuéntanos y lo solucionaremos lo antes posible.", "feature": "¿Tienes una idea para una nueva función? Nos encantaría escuchar tus sugerencias para mejorar nuestra plataforma."}, "success": {"title": "¡<PERSON><PERSON><PERSON>!", "issue": "Tu informe de problema se ha enviado con éxito.", "feature": "Tu solicitud de función se ha enviado con éxito."}, "form": {"fullName": {"label": "Nombre completo", "placeholder": "Ingresa tu nombre completo"}, "email": {"label": "Correo electrónico", "placeholder": "Ingresa tu correo electrónico"}, "suggestion": {"labelIssue": "Descripción del problema", "labelFeature": "Sugerencia de función", "placeholderIssue": "Describe el problema que estás experimentando...", "placeholderFeature": "Describe la función que te gustaría ver..."}, "screenshots": {"label": "Capturas de pantalla (opcional)", "note": "Agrega capturas de pantalla para ayudarnos a entender mejor el problema", "drop": "Haz clic para subir o arrastra y suelta", "hint": "PNG, JPG, JPEG hasta 10MB cada uno", "count": "{{count}} imagen{{count > 1 ? 'es' : ''}} seleccionada(s)"}}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "submitting": "Enviando...", "submitIssue": "Enviar problema", "submitFeature": "<PERSON><PERSON><PERSON> solicitud"}, "errors": {"fullNameRequired": "El nombre completo es requerido", "fullNameNoNumbers": "El nombre completo no puede contener números", "emailRequired": "La dirección de correo es requerida", "emailInvalid": "Por favor ingresa una dirección de correo válida", "suggestionRequired": "Por favor describe tu problema o sugerencia"}}, "changelog": {"title": "Registro de cambios", "description": "Siempre. Evolucionando. Adelante.", "TotalReleases": "Lanzamientos Totales", "ActiveUsers": "Usuarios Activos", "FeaturesAdded": "Características Añadidas", "BugsFixed": "E<PERSON>res Corregidos", "totalReleases": "Lanzamientos Totales", "activeUsers": "Usuarios Activos", "featuresAdded": "Características Añadidas", "bugsFixed": "E<PERSON>res Corregidos", "shortCallText": "¿Tienes sugerencias o encontraste un error? ¡Nos encantaría saberlo!", "reportIssue": "Reportar un Problema", "requestFeature": "Solicitar una Funcionalidad", "types": {"feature": "Funcionalidad", "improvement": "<PERSON><PERSON><PERSON>", "bugfix": "Corrección de Error", "ui/ux": "UI/UX", "announcement": "<PERSON><PERSON><PERSON>", "general": "General"}, "versions": {"v3.2.3": {"date": "26 de junio de 2025", "changes": {"0": "Se corrigieron algunos problemas de UI/UX en el panel del usuario.", "1": "Se mejoró la estabilidad del WebContainer.", "2": "El modo AI Diff mejorado maneja archivos pequeños de manera diferente a los archivos grandes."}}, "v3.2.2": {"date": "24 de junio de 2025", "changes": {"0": "Se reorganizó y rediseñó el frontoffice para mejorar la experiencia del usuario.", "1": "Se implementó esta increíble página de registro de cambios.", "2": "Se mejoró el algoritmo de reconexión de WebContainer.", "3": "Se añadió soporte para subir proyectos en archivos ZIP, además de carpetas.", "4": "Mejora en el rendimiento de todas las llamadas API mediante mejor indexación."}}, "v3.2.1": {"date": "24 de junio de 2025", "changes": {"0": "IDE: La sección \"Carpetas\" del panel fue rediseñada.", "1": "Content Studio: <PERSON><PERSON> puedes copiar imágenes dentro del estudio."}}, "v3.2.0": {"date": "17 de junio de 2025", "changes": {"0": "Modo <PERSON>ff (Experimental): La IA ahora solo escribe las diferencias entre el archivo original y el actualizado. Mejora de rendimiento y uso de tokens. Se puede activar/desactivar desde Configuración → Centro de Control del proyecto."}}, "v3.1.6": {"date": "12 de junio de 2025", "changes": {"0": "IDE: Campo de código de cupón añadido para suscripciones y tokens adicionales."}}, "v3.1.5": {"date": "11 de junio de 2025", "changes": {"0": "IDE: Se aplicaron correcciones al flujo \"Compartir Proyecto\"."}}, "v3.1.4": {"date": "10 de junio de 2025", "changes": {"0": "IDE: Se revisaron y corrigieron las suscripciones a paquetes. Se resolvieron problemas con criptomonedas y Stripe."}}, "v3.1.3": {"date": "9 de junio de 2025", "changes": {"0": "IA: Integración directa con API de Gemini implementada para mejorar la latencia."}}, "v3.1.2": {"date": "6 de junio de 2025", "changes": {"0": "Afiliados: Implementadas tareas de pago con diseño coherente al nuevo panel.", "1": "Content Studio: Nueva opción para decidir si enviar imágenes a la IA junto a enlaces. Por defecto: no."}}, "v3.1.1": {"date": "5 de junio de 2025", "changes": {"0": "Content Studio: Actualización para organizar contenidos por proyecto."}}, "v3.1.0": {"date": "3 de junio de 2025", "changes": {"0": "🚀 ¡Actualización importante de BIELA.dev lanzada! 🚀", "1": "La IA ahora escribe código 5 veces más rápido.", "2": "Terminal: Mejoras UI/UX para una codificación más fluida.", "3": "Mejoras en la conexión Supabase: interfaz más clara e intuitiva.", "4": "Actualización en gestión de errores: se desactivó el envío automático.", "5": "Nuevo Centro de Control (en Configuración del Proyecto) + Interfaz de testing.", "6": "Descargas ahora esperan hasta que el código esté completamente enviado."}}, "v3.0.0": {"date": "3 de junio de 2025", "changes": {"0": "🚀 ¡Actualización de BIELA.dev! 🚀", "1": "Nuevo diseño de terminal más limpio y moderno.", "2": "Botones de acción NPM con un clic.", "3": "Manejo de errores inteligente y clasificación visual.", "4": "Navegación de archivos mejorada desde el chat al WebContainer."}}, "v2.1.1": {"date": "30 de mayo de 2025", "changes": {"0": "IDE: <PERSON><PERSON><PERSON><PERSON> bot<PERSON> de guardar para WebContainer que hace commit y push.", "1": "Panel: Corregido error al renombrar proyectos importados.", "2": "WebContainer: Corregida fuga de memoria urgente."}}, "v2.1.0": {"date": "28 de mayo de 2025", "changes": {"0": "🚀 ¡7 mejoras nuevas en BIELA.dev! 🚀", "1": "IDE: <PERSON><PERSON><PERSON> conectar tu propio dominio al proyecto.", "2": "Vista previa de proyectos desplegados.", "3": "Seguimiento de costos corregido en proyectos duplicados.", "4": "Estimación de costos por prompt en tiempo real.", "5": "Historial y restauración funcionan correctamente.", "6": "Mejoras en el autoscroll durante la generación.", "7": "<PERSON><PERSON> ahora se abren en nueva pestaña si ya estás en Labs."}}, "v2.0.4": {"date": "27 de mayo de 2025", "changes": {"0": "Dashboard: Mejora de capturas de pantalla: sin autoscroll, carga animada, scroll manual."}}, "v2.0.3": {"date": "20 de mayo de 2025", "changes": {"0": "Content Studio: Animación del robot ahora se muestra en tamaño completo.", "1": "Afiliados: Problemas visuales corregidos en Firefox."}}, "v2.0.2": {"date": "19 de mayo de 2025", "changes": {"0": "Content Studio: Límite de subida de archivos aumentado de 10 a 50."}}, "v2.0.1": {"date": "18 de mayo de 2025", "changes": {"0": "Content Studio: Corrección de etiquetas que no se actualizaban sin recargar.", "1": "Botón de edición más visible al seleccionar imagen.", "2": "Ubicación de carpeta seleccionada ahora automática.", "3": "La carpeta permanece al arrastrar y soltar.", "4": "Nuevo color para el título de pestaña.", "5": "Botón de 'insertar imagen' ahora dice 'usar en proyecto'.", "6": "Botón de subir archivos ahora es verde.", "7": "Altura de barra de búsqueda reducida.", "8": "Barra de búsqueda oculta si no hay imágenes."}}, "v2.0.0": {"date": "16 de mayo de 2025", "changes": {"0": "🚀 ¡ACTUALIZACIONES IMPORTANTES EN BIELA.DEV! 🚀", "1": "WebContainer propio desarrollado, sin dependencias externas.", "2": "Soporte para múltiples modelos LLM, incluyendo Gemini.", "3": "Content Studio: ahora puedes subir tus propias imágenes.", "4": "Compartir proyectos con otros usuarios.", "5": "Conectar varios proyectos Biela con la misma base de datos Supabase."}}, "v1.0.5": {"date": "15 de mayo de 2025", "changes": {"0": "Pagos: Integrado procesamiento con Stripe.", "1": "Content Studio: Animación mostrada cuando no hay imágenes."}}, "v1.0.4": {"date": "13 de mayo de 2025", "changes": {"0": "Content Studio: Paginación corregida."}}, "v1.0.3": {"date": "5 de mayo de 2025", "changes": {"0": "Lanzado Content Studio para gestionar medios.", "1": "Frontoffice: Añadido schema.org para apps.", "2": "Panel de equipo: notificaciones de nuevos miembros o mensajes.", "3": "IDE: Corregido error al cerrar modal en ciertas áreas del WebContainer."}}, "v1.0.2": {"date": "2 de mayo de 2025", "changes": {"0": "Afiliados: Rediseño del área de miembros.", "1": "Afiliados: Nuevos iconos en el panel de afiliados.", "2": "IDE: Botón 'Conectar Supabase' ahora dice 'Base de Datos'."}}, "v1.0.1": {"date": "1 de mayo de 2025", "changes": {"0": "IDE (Pestaña de Configuración): Integración de base de datos añadida.", "1": "Dashboard: Paginación corregida."}}, "v1.0.0": {"date": "18 de abril de 2025", "changes": {"0": "🚀 ¡Bienvenido a Biela.dev: Tu compañero de codificación con IA, gratuito para todos! 🚀", "1": "Nos complace anunciar el lanzamiento público de Biela.dev. Nuestra misión es hacer accesible el desarrollo web asistido por IA para todos."}}}}}