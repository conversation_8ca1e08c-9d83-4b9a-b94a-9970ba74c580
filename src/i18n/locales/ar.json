{"meta": {"home": {"title": "biela.dev | منشئ الويب والتطبيقات المدعوم بالذكاء الاصطناعي – البناء باستخدام الإيعازات", "description": "حوّل أفكارك إلى مواقع ويب أو تطبيقات حية مع biela.dev. استخدم الإيعازات المدعومة بالذكاء الاصطناعي لإنشاء منتجات رقمية مخصصة بسهولة"}, "privacy": {"title": "سياسة خصوصية biela.dev", "description": "افهم كيف تقوم biela.dev بجمع واستخدام وحماية معلوماتك الشخصية."}, "terms": {"title": "شروط خدمة biela.dev", "description": "راجع شروط وأحكام استخدام منصة التطوير المدعومة بالذكاء الاصطناعي من biela.dev"}, "changelog": {"title": "سجل التغييرات لـ biela.dev - مستمر التطور للأمام.", "description": "اهم Matters biela.dev مع كل إصدار جديد"}, "competitionterms": {"title": "شروط وأحكام مسابقة تطوير Biela", "description": "انضم إلى مسابقة تطوير Biela—تحدٍ رسمي لمدة شهر تنظمه Biela.dev ومعهد TeachMeCode. اعرض ابتكاراتك الرقمية، واحصل على التقدير من خلال تفاعل المجتمع، ونافس على أعلى المراتب. الموعد النهائي للتقديم هو 14 مايو 2025."}, "contest": {"title": "مسابقة تطوير biela.dev 2025 – اربح 10,000 دولار", "description": "انضم إلى مسابقة biela.dev لعرض مشاريعك المبنية بالذكاء الاصطناعي والفوز بجوائز نقدية"}, "howTo": {"title": "كيفية استخدام Biela.dev – دليل كامل لبناء تطبيقات مدعومة بالذكاء الاصطناعي", "description": "اكتشف كيفية استخدام Biela.dev لبناء تطبيقات ويب بسرعة ودون كتابة كود. دليل كامل خطوة بخطوة مع دروس ونصائح عملية.", "keywords": "biela, biela.dev, كيفية استخدام Biel<PERSON>, إنشاء تطبيقات ذكاء اصطناعي, بدون كود, تطوير سريع, دلي<PERSON> Biela, شرح Biela", "ogTitle": "كيفية استخدام Biela.dev – دليل خطوة بخطوة", "ogDescription": "تعلم كيفية بناء تطبيقات ذكاء اصطناعي باستخدام Biela.dev. من الفكرة إلى الإطلاق، دون الحاجة إلى ترميز.", "twitterTitle": "كيفية استخدام Biela.dev – دليل كامل", "twitterDescription": "أنشئ تطبيقات ذكاء اصطناعي على Biela.dev دون كتابة كود. اتبع هذا الدليل السهل خطوة بخطوة!"}}, "navbar": {"home": "الرئيسية", "demo": "العرض التجريبي", "community": "المجتمع", "affiliate": "برنامج الشراكة", "partners": "الشركاء", "contest": "مسابقة البرمجة بالذوق", "subscriptions": "الاشتراكات", "pricing": "الأسعار"}, "counter": {"days": "أيام", "hours": "ساعات", "minutes": "دقائق", "seconds": "ثوان", "centisec": "سنتي ثانية"}, "hero": {"title": "حوّل فكرتك إلى موقع ويب أو تطبيق حي خلال دقائق", "subtitle": "إذا استطعت <1>تخيل</1> ذلك، يمكنك <5>برمجته</5>.", "subtitle2": "ابدأ مجانًا. برمج كل شيء. حول مهاراتك في كل طلب إلى فرصة.", "timeRunningOut": "الوقت ينفد!", "launchDate": "إصدار وودسنيك", "experimentalVersion": "الإطلاق التجريبي في 15 أبريل 2025", "earlyAdopter": "انضم الآن لتأمين مزايا المستخدم المبكر قبل الإطلاق الرسمي", "tryFree": "جرب مجاناً", "seeAiAction": "شا<PERSON><PERSON> الذكاء الاصطناعي قيد العمل", "tellBiela": "اطلب من بيلا الإنشاء", "inputPlaceholder": "إذا استطعت تخيله، يمكن لـ BIELA برمجته، ماذا سنفعل اليوم؟", "chat": "دردشة", "code": "كود", "checklist": "قائمة التحقق", "checklistTitle": "استخدم قوائم التحقق لـ", "checklistItems": {"trackDevelopment": "تتبع مهام التطوير", "setRequirements": "تحديد متطلبات المشروع", "createTesting": "إنشاء بروتوكولات الاختبار"}, "registerNow": "سجل الآن", "attachFiles": "أرفق الملفات مع طلبك", "voiceInput": "استخدم الإدخال الصوتي", "enhancePrompt": "حسّن الطلب", "cleanupProject": "نظف المشروع", "suggestions": {"weatherDashboard": "أنشئ لوحة بيانات الطقس", "ecommercePlatform": "أنشئ منصة للتجارة الإلكترونية", "socialMediaApp": "صمم تطبيقاً لوسائل التواصل الاجتماعي", "portfolioWebsite": "أنشئ موقع معرض أعمال", "taskManagementApp": "أنشئ تطبيق إدارة المهام", "fitnessTracker": "أنشئ متتبع للياقة البدنية", "recipeSharingPlatform": "صمم منصة لمشاركة الوصفات", "travelBookingSite": "أنشئ موقع حجز السفر", "learningPlatform": "أنشئ منصة تعليمية", "musicStreamingApp": "صمم تطبيق بث الموسيقى", "realEstateListing": "أنشئ قائمة عقارية", "jobBoard": "أنشئ موقع للوظائف"}}, "videoGallery": {"barber": {"title": "موقع صالون الحلاقة", "description": "شاهد كيف يقوم Biela.dev بإنشاء موقع صالون حلاقة متكامل في دقائق"}, "tictactoe": {"title": "لعبة تيك تاك تو", "description": "شاهد كيف ينشئ الذكاء الاصطناعي لعبة تيك تاك تو أسرع مما تنطق تيك تاك تو"}, "coffeeShop": {"title": "موقع المقهى", "description": "شاهد كيف يقوم Biela.dev بإنشاء مقهى"}, "electronicsStore": {"title": "موقع متجر الإلكترونيات", "description": "شاهد كيف يقوم Biela.dev بإنشاء متجر إلكتروني متكامل في دقائق"}, "seoAgency": {"title": "موقع وكالة تحسين محركات البحث", "description": "شاهد كيف ينشئ الذكاء الاصطناعي موقع وكالة تحسين محركات البحث"}}, "telegram": {"title": "انضم إلى تلغرامنا", "subtitle": "تواصل مع مجتمع Biela", "description": "احصل على دعم فوري، وشارك مشاريعك، وتواصل مع عشاق الذكاء الاصطناعي من جميع أنحاء العالم. <br/><br/> مجتمعنا على تلغرام هو أسرع وسيلة للبقاء على اطلاع بآخر مستجدات Biela.dev.", "stats": {"members": "الأعضاء", "support": "الدعم", "countries": "الدول", "projects": "المشاريع"}, "joinButton": "انضم إلى مجتمعنا على تلغرام", "communityTitle": "مجتمع Biela.dev", "membersCount": "أعضاء", "onlineCount": "متصلون الآن", "messages": {"1": "لقد أطلقت للتو موقعي للتجارة الإلكترونية مع Biela.dev في أقل من ساعة!", "2": "يبدو رائعاً! كيف تعاملت مع كتالوج المنتجات؟", "3": "قام Biela بالتعامل مع ذلك تلقائياً! كل ما فعلته هو وصف ما أردت.", "4": "أنا الآن أبني موقع معرض أعمال. اقتراحات الذكاء الاصطناعي مذهلة!", "5": "هل جر<PERSON> أحد النماذج التفاعلية الجديدة؟ تبدو رائعة على الهواتف المحمولة."}}, "joinNetwork": {"title": "انضم إلى شبكتنا", "subtitle": "تواصل. نمّ. اربح.", "affiliateProgram": "برنامج الشراكة", "registerBring": "سجل وأحضر 3 شركاء", "aiCourse": "دورة هندسة الذكاء الاصطناعي", "courseDescription": "دورة شاملة مجانية في الذكاء الاصطناعي مع TeachMeCode", "digitalBook": "كتاب رقمي", "bookDescription": "النسخة الرقمية الحصرية لـ \"فن هندسة الطلبات\"", "exclusiveBenefits": "مزايا حصرية", "benefitsDescription": "أر<PERSON><PERSON><PERSON> مدى الحياة وامتيازات خاصة للأعضاء", "registerNow": "سجل الآن", "teamEarnings": "أ<PERSON><PERSON><PERSON><PERSON> الفريق", "buildNetwork": "ابنِ شبكتك", "weeklyMentorship": "توجيه أسبوعي", "teamBonuses": "مكافآت الفريق", "startEarning": "ابد<PERSON> بالكسب اليوم", "upToCommission": "حتى العمولات"}, "partners": {"title": "موثوق من قِبل رواد الصناعة", "subtitle": "نتعاون مع مبتكرين عالميين لتشكيل مستقبل تطوير الذكاء الاصطناعي", "strategicPartnerships": "شراكات استراتيجية", "joinNetwork": "انضم إلى شبكة الشركاء والشركات المتنامية التي تُحدث تحولاً مع BIELA"}, "demo": {"title": "شا<PERSON><PERSON> الذكاء الاصطناعي يبني التطبيقات والمواقع في الوقت الحقيقي", "subtitle": "اكتشف كيف تُحوّل الشركات حضورها الرقمي مع Biela.dev", "seeInAction": "شا<PERSON><PERSON> B<PERSON>.dev قيد العمل", "whatKind": "ما نوع الموقع الذي تحتاجه؟", "describeWebsite": "صف فكرة موقعك (مثلاً: 'معرض أعمال لمصور')", "generatePreview": "أنشئ معاينة", "nextDemo": "العرض التجريبي التالي", "startBuilding": "ابد<PERSON> بالبناء والكسب مع Biela.dev اليوم"}, "footer": {"quickLinks": {"title": "روابط سريعة", "register": "التسجيل", "bielaInAction": "شا<PERSON><PERSON> B<PERSON>.dev قيد العمل", "liveOnTelegram": "نحن متواجدون على تلغرام", "affiliate": "التسويق بالعمولة التعاوني", "partners": "الشركاء"}, "legal": {"title": "قانوني", "glassCard": {"copyright": "© 2025 معهد TeachMeCode. جميع الحقوق محفوظة.", "unauthorized": "يُمنع منعاً باتاً الاستخدام أو الاستنساخ أو التوزيع غير المصرح به لهذه الخدمة، كلياً أو جزئياً، دون إذن خطي صريح."}, "reservations": "يحتفظ معهد TeachMeCode بجميع الحقوق القانونية للملكية الفكرية المتعلقة بـ Biela."}, "bottomBar": {"left": "2025 Biela.dev. مدعوم بواسطة %s © جميع الحقوق محفوظة.", "allRights": "جميع الحقوق محفوظة.", "right": "تم التطوير بواسطة %s"}}, "common": {"loading": "جار التحميل...", "error": "<PERSON><PERSON><PERSON>", "next": "التالي", "previous": "السابق", "submit": "إرسال", "cancel": "إلغاء", "close": "إغلاق", "loginNow": "تسجيل الدخول الآن", "verifyAccount": "تحقق من الحساب"}, "languageSwitcher": {"language": "اللغة"}, "contest": {"bielaDevelopment": "هاكاثون ترميز الأجواء", "competition": "المسابقة", "firstPlace": "المركز الأول", "secondPlace": "المركز الثاني", "thirdPlace": "المركز الثالث", "currentLeader": "القائد الحالي:", "footerText": "تطوير مدعوم بالذكاء الاصطناعي من Biela.dev، بدعم من معهد TeachMeCode®", "browseHackathonSubmissions": "تصفح مشاركات الهاكاثون"}, "competition": {"header": {"mainTitle": "مهمتك. أسلوبك. ملعبك.", "timelineTitle": "الجدول الزمني", "timelineDesc": "شهر واحد لابتكار أفضل مشروع يولده الذكاء الاصطناعي", "eligibilityTitle": "الأهلية", "eligibilityDesc": "الحسابات النشطة على Biela.dev التي تقدم مشروعاً", "prizesTitle": "الجوائز", "prizesDesc": "16,000 دولار أمريكي من الجوائز النقدية بالإضافة إلى وصول مجاني للفائزين", "votingTitle": "التصويت", "votingDesc": "مدعوم من المجتمع، صوت واحد لكل مستخدم موثوق", "tagline": "دع إبداعك يتحدث. ودع العالم يصوت."}, "details": {"howToEnter": "كيفية المشاركة", "enter": {"step1": {"title": "أنشئ مشروعاً باستخدام ذكاء Biela.dev الاصطناعي", "desc": "استخدم أدوات الذكاء الاصطناعي القوية من Biela لبناء مشروعك الابتكاري"}, "step2": {"title": "انتقل إلى لوحة المستخدم الخاصة بك", "desc": "انقر على \"نشر للمسابقة\" بجانب مشروعك", "subdesc": "يمكنك تقديم ما يصل إلى 3 مشاريع مختلفة"}, "step3": {"title": "ستقوم Biela بمعالجة مشاركتك:", "list1": "التقط لقطة شاشة لمشروعك", "list2": "تحقق من مطابقة طلباتك لمعايير الأهلية", "list3": "أنشئ ملخصاً ووصفاً"}}, "prizeStructure": "هيكل الجوائز", "prizes": {"firstPlace": "المركز الأول", "firstPlaceValue": "$10,000", "secondPlace": "المركز الثاني", "secondPlaceValue": "$5,000", "thirdPlace": "المركز الثالث", "thirdPlaceValue": "$1,000", "fourthToTenth": "من المركز الرابع إلى العاشر", "fourthToTenthValue": "30 يوماً من الوصول غير المحدود"}}, "qualification": {"heading": "متطلبات التأهل", "description": "للتأهل لأفضل 3 جوائز (10,000 دولار، 5,000 دولار، و1,000 دولار)، يجب أن تستوفي المعايير التالية:", "criteria1": "امتلك على الأقل 3 إحالات نشطة", "criteria2": "أعجب بمشروع واحد من جدار العرض", "proTipLabel": "نصيحة احترافية:", "proTipDesc": "كلما قدمت مشروعك مبكراً، ستحظى بمزيد من الرؤية والأصوات!"}, "voting": {"heading": "إرشادات التصويت", "oneVotePolicyTitle": "سياسة التصويت الواحد", "oneVotePolicyDesc": "يمكنك التصويت مرة واحدة فقط، ولا يمكنك التصويت لمشروعك الخاص", "accountVerificationTitle": "التحقق من الحساب", "accountVerificationDesc": "يمكن فقط للمستخدمين الذين لديهم حسابات Biela.dev موثقة التصويت", "tieBreakingTitle": "كسر التعادل", "tieBreakingDesc": "في حالة التعادل، سيكون العامل الحاسم هو العدد الإجمالي للنجوم المكتسبة من قبل منشئ المشروع (المستخدمة أو غير المستخدمة)", "howToVoteTitle": "كيفية التصويت", "howToVoteDesc": "تصفح جدار العرض، واستكشف جميع المشاريع المنشورة المولدة بالذكاء الاصطناعي، واضغط على أيقونة القلب للتصويت لمشروعك المفضل!"}}, "contestItems": {"projectShowcase": "عرض المشروع", "loading": "جار التحميل...", "createBy": "تم الإنشاء بواسطة ", "viewProject": "عرض المشروع", "stars": "نجوم", "overview": "نظرة عامة", "keyFeatures": "الميزات الرئيسية", "exploreLiveProject": "استكشف المشروع المباشر", "by": "بواسطة", "likeLimitReached": "تم الوصول إلى الحد الأقصى للإعجابات", "youCanLikeMaximumOneProject": "يمكنك الإعجاب بمشروع واحد فقط. هل ترغب في إلغاء الإعجاب بالمشروع الحالي والإعجاب بهذا بدلاً من ذلك؟", "currentlyLikedProject": "المشروع الذي تم الإعجاب به حاليًا", "switchMyLike": "تبديل إعجابي", "mustLoggedIntoLikeProject": "يجب أن تكون مسجلاً للإعجاب بمشروع.", "signInToBielaAccountToVote": "يرجى تسجيل الدخول إلى حساب Biela.dev الخاص بك للمشاركة في عملية التصويت.", "yourAccountIsNotVerifiedYet": "لم يتم التحقق من حسابك بعد. يرجى التحقق من حسابك لتتمكن من الإعجاب بمشروع.", "accountVerificationEnsureFairVoting": "يضمن التحقق من الحساب تصويتًا عادلًا ويساعد في الحفاظ على نزاهة المسابقة.", "projectsSubmitted": "المشاريع المقدمة:", "unlikeThisProject": "إلغاء الإعجاب بهذا المشروع", "likeThisProject": "الإعجاب بهذا المشروع", "likes": "الإعجابات", "like": "الإعجاب", "somethingWentWrong": "حدث خطأ ما. يرجى المحاولة مرة أخرى لاحقًا", "voteError": "خطأ في التصويت"}, "textsLiveYoutube": {"first": {"title": "أنت مدعو!", "description": "سنكون في بث مباشر هذا الجمعة — وهذه الجلسة تدور حول البناء بـ<green>الغرض</green>، <blue>الدقة</blue>، و<orange>الأجواء الخالصة</orange>.", "secondDescription": "تعلم كيف <green>تتقن البرمجة بالإيقاع</green>، <blue>تكتشف ميزات جديدة</blue> قبل أي شخص، و<orange>تشاهد مشاريع حقيقية تُبنى مباشرة</orange>.", "specialText": "انقر هنا واضغط \"إخطاري\" على YouTube — لا تفوت ذلك."}, "second": {"title": "كن أول من يبني.", "description": "هذا الجمعة، نرفع مستوى <green>البرمجة بالإيقاع</green> إلى مستوى جديد — نريك كيف <blue>تبدع بنية</blue>، <orange>تفتح ميزات جديدة</orange>، وتنطلق <green>بسرعة لم يسبق لها مثيل</green>.", "secondDescription": "<green>بُنى جديدة</green>، <blue>أفكار جديدة</blue>، و<orange>فرص جديدة</orange> للنمو.", "specialText": "انقر هنا لتعيين تذكير YouTube — وكن جزءًا من الموجة القادمة من المبدعين."}, "third": {"title": "ا<PERSON><PERSON><PERSON> مكانك.", "description": "بثنا التالي على Biela.dev سيكون هذا الجمعة — وأنت <blue>مدعو رسميًا</blue> لـ<green>البث المباشر</green>.", "secondDescription": "نغوص عميقًا في <green>البرمجة بالإيقاع</green>، نكشف <blue>ميزات جديدة قوية</blue>، ونساعدك على البناء <orange>أذكى، أسرع، وأكبر</orange>.", "specialText": "انقر هنا واضغط \"إخطاري\" على YouTube — قد تبدأ فكرتك الكبيرة القادمة من هنا."}}, "stream": {"title": "كن <1>الأول</1> في <3>البناء</3>.", "subtitle": "تابع بثنا المباشر يوم الجمعة — ميزات، مشاريع، وتحديثات حصرية.", "button": "اضبط تذكيرك على YouTube."}, "billings": {"modalTitle": "ا<PERSON><PERSON><PERSON> خطة", "modalSubtitle": "اختر من بين خططنا الثلاثة المرنة للاستمرار في البرمجة بدون حدود.", "toggleMonthly": "شهري", "toggleYearly": "سنوي (وفر 10%)", "mostPopular": "الأكثر شيوعًا", "tokensLowerCase": "رموز", "tokensUpperCase": "رموز", "below": "أدناه", "unlimited": "<PERSON>ير محدود", "10Bonus": "10% بونص", "currentPlan": "الخطة الحالية", "upgradePlan": "ترقية الخطة", "purchaseDetails": "تفاصيل الشراء", "dateLabel": "التاريخ", "planLabel": "الخطة", "amountLabel": "المبلغ", "whatsNext": "ما الخطوة التالية", "yourTokensAvailable": "الرموز أصبحت متاحة في حسابك", "purchaseDetailsStored": "تفاصيل الشراء محفوظة في حسابك", "viewPurchaseHistory": "يمكنك عرض سجل مشترياتك في قسم الفوترة", "thankYou": "شكرًا لك!", "purchaseSuccessful": "تم الشراء بنجاح", "startCoding": "اب<PERSON><PERSON> البرمجة", "month": "شهر", "year": "سنة"}, "feature": {"basic_ai": "تطوير الذكاء الاصطناعي الأساسي", "community_support": "دعم المجتمع", "standard_response": "وقت الاستجابة القياسي", "basic_templates": "قوالب أساسية", "advanced_ai": "تطوير الذكاء الاصطناعي المتقدم", "priority_support": "دعم أولوية", "fast_response": "وقت استجابة سريع", "premium_templates": "قوالب مميزة", "team_collaboration": "التعاون الجماعي", "enterprise_ai": "تطوير الذكاء الاصطناعي المؤسسي", "support_24_7": "دعم أولوية على مدار 24/7", "instant_response": "وقت استجابة فوري", "custom_templates": "قوالب مخصصة", "advanced_team": "ميزات الفريق المتقدمة", "custom_integrations": "تكاملات مخصصة", "big_context_window": "نافذة سياق كبيرة - أكبر 5 مرات من النافذة المتوسطة", "code_chat_ai": "الوصول إلى الذكاء الاصطناعي للشفرة والدردشة", "tokens_basic": "عدد كافٍ من الرموز لحوالي 3 مواقع تقديم", "standard_webcontainer": "حاوية ويب قياسية مخصصة", "medium_context_window": "نافذة سياق متوسطة - مناسبة لموقع إلكتروني أو مدونة أو لعبة متصفح أساسية", "basic_support": "دعم أساسي مع وقت استجابة قياسي", "supabase_integration": "تكامل Supabase", "project_sharing": "مشاركة المشاريع مع أصدقائك", "project_download": "تحميل مشاريعك", "project_deployment": "ميزة النشر", "custom_domain": "ربط المشاريع بنطاقك الخاص", "tokens_creator_plus": "عدد كافٍ من الرموز لحوالي 7 مواقع تقديم", "advanced_support": "دعم متقدم مع وقت استجابة أسرع", "prioritary_support": "دعم مخصص بأولوية", "early_feature_access": "الوصول المبكر إلى الميزات الجديدة", "human_cto": "رئيس تقني بشري مخصص لأعمالك", "community_promotion": "روّج لأعمالك مع مجتمعنا"}, "JoinUsLive": "انضم إلينا مباشرة", "howToUseBiela": {"subtitle": "كيفية استخدام Biela", "title": "مشروح في دقائق", "description": "شروحات موجزة حول كيفية عمل كل ميزة"}, "slides": {"newHistoryFeature": "اكتشف ميزة السجل الجديدة في Biela!", "newHistoryFeatureDescription": "تحافظ علامة تبويب السجل على سير عملك في الترميز بالإيقاع سلسًا من خلال السماح لك بمراجعة واستعادة أي إصدار سابق من مشروعك، لتبقى دائمًا مسيطراً!", "fixPreview": "كيفية إصلاح المعاينة على Biela!", "fixPreviewDescription": "شا<PERSON><PERSON> الدليل الكامل وتأكد من أن كل شيء يظهر تمامًا كما أنشأته.", "signUp": "كيفية التسجيل في Biela!", "signUpDescription": "جديد على biela.dev؟ شاهد هذا الدليل السريع لتتعلم كيفية التسجيل وتفعيل حسابك!", "buildWebsite": "كيفية إنشاء موقع ويب باستخدام Biela!", "buildWebsiteDescription": "تعلم كيفية إنشاء ونشر موقع ويب من البداية باستخدام biela.dev — دون كتابة سطر واحد من الكود.", "downloadAndImport": "كيفية تنزيل واستيراد المشاريع والدردشات على Biela!", "downloadAndImportDescription": "في هذا الدليل التفصيلي، ستتعلم كيفية تنزيل مشاريعك المكتملة واستيراد الدردشات المحفوظة إلى مساحة العمل الخاصة بك — لتتمكن من المتابعة من حيث توقفت ومواصلة الترميز بالإيقاع في أي وقت.", "shareProject": "كيفية مشاركة مشروع BIELA الخاص بك!", "shareProjectDescription": "هل أنت جاهز لمشاركة مشروعك باحتراف؟ شاهد الدليل الكامل وابدأ الآن.", "launchProject": "كيفية إطلاق مشروعك على نطاقك الخاص!", "launchProjectDescription": "في هذا الفيديو، نعرض الخطوات البسيطة لنشر موقعك من الإعداد حتى الإطلاق النهائي، وستحصل على كل ما تحتاجه للانتقال بثقة من البناء إلى الإطلاق.", "deployProject": "كيفية نشر مشروعك وتشغيله!", "deployProjectDescription": "يرشدك هذا الدليل السريع خلال كل خطوة لنشر موقعك أو تطبيقك أو لوحة التحكم بسرعة وسلاسة."}, "maintenance": {"title": "سنعود قريباً", "description": "موقعنا يعمل حالياً على إصلاح المشكلة. نحن نعمل على إصلاح المشكلة", "text": "شكراً لك على الصبر."}, "planCard": {"descriptionBeginner": "مثالي للمبتدئين الذين يقومون ببناء مشاريعهم الأولى ويتعلمون تطوير الويب", "descriptionCreator": "مثالي للمبدعين الذين يحتاجون إلى مزيد من القوة والمرونة للمشاريع المتقدمة", "descriptionProfessional": "حل كامل للمطورين المحترفين والفرق التي تبني تطبيقات", "perMillionTokens": "$ {{price}} / لكل مليون رمز", "dedicatedCto": "رئيس تقني مخصص - 24 ساعة", "tokens": "رموز", "perMonth": "شهريًا", "perYear": "سنوياً", "freeTokens": "{{ tokens }} رموز مجانية", "monthly": "شهري", "yearly": "سنوي", "save": "وفر 10٪"}, "faqLabel": "الأسئلة الشائعة", "faqHeading": "اطّلع على أكثر الأسئلة طرحًا", "faqSubheading": "ابدأ. افهم. استكشف", "whatAreTokensQuestion": "ما هي التوكنات؟", "whatAreTokensAnswer1": "التوكنات هي الطريقة التي تستخدمها الذكاء الاصطناعي لمعالجة مشروعك. على Biela.dev، تُستخدم التوكنات عندما ترسل أمرًا إلى الذكاء الاصطناعي. يتم تضمين ملفات مشروعك في الرسالة حتى يتمكن الذكاء الاصطناعي من فهم الكود. كلما زاد حجم المشروع وطالت الاستجابة، زاد عدد التوكنات المستخدمة لكل رسالة.", "whatAreTokensAnswer2": "بالإضافة إلى ذلك، يتم خصم عدد صغير من التوكنات من حسابك مقابل كل دقيقة تستخدم فيها حاوية الويب لتغطية تكلفة بيئة التطوير.", "freePlanTokensQuestion": "كم عدد التوكنات التي أحصل عليها في الخطة المجانية؟", "freePlanTokensAnswer": "كل مستخدم في الخطة المجانية يحصل على 200,000 توكن يوميًا، ويتم تجديدها يوميًا.", "outOfTokensQuestion": "ماذا يحدث إذا نفدت التوكنات؟", "outOfTokensAnswer": "لا تقلق! يمكنك دائمًا الذهاب إلى القائمة → الفوترة وشراء المزيد من التوكنات فورًا عند الحاجة.", "changePlanQuestion": "هل يمكنني تغيير الخطة لاحقًا؟", "changePlanAnswer": "نعم — في أي وقت. ما عليك سوى التوجه إلى القائمة → الفوترة للترقية أو التخفيض أو تبديل الخطة حسب حاجتك.", "rolloverQuestion": "هل تنتقل التوكنات غير المستخدمة للشهر التالي؟", "rolloverAnswer": "حاليًا، تتم إعادة تعيين التوكنات عند تاريخ انتهاء الصلاحية ولا تُرحل — لذا تأكد من الاستفادة منها يوميًا!", "exploreHowToUseBiela": "استكشف كيفية استخدام Biela", "new": "جديد", "joinVibeCoding": "انضم إلى هاكاثون Vibe Coding", "whatDoYouWantToBuild": "ماذا تريد أن تبني؟", "imaginePromptCreate": "تخيل. اطلب. أنشئ.", "levelOfAmbition": "ما هو مستوى طموحك؟", "startGrowScale": "ابدأ. نمِّ. توسّع.", "calloutBar": {"start": "جميع الخطط تبدأ مجانًا مع", "tokens": "200,000 توكن يوميًا", "end": ". لا حاجة للتسجيل. ابدأ البناء فورًا."}, "atPriceOf": "بسعر", "startFreeNoCreditCard": "ابدأ مجانًا - بدون بطاقة ائتمان", "SignIn": "تسجيل الدخول", "Register": "إنشاء حساب", "Affiliates": "الشركاء", "UsernameRequired": "Username is required", "PasswordRequired": "Password is required", "MissingUsernamePasswordOrTurnstile": "Missing username, password or Turnstile token", "LoginSuccess": "Login successful!", "LoginFailed": "<PERSON><PERSON> failed", "EmailRequired": "Email is required", "EmailInvalid": "Please enter a valid email address", "CaptchaRequired": "Please complete the CAPTCHA", "ResetLinkSent": "A reset link has been sent to your email.", "ResetFailed": "فشل في إرسال رابط إعادة التعيين", "BackToLogin": "العودة إلى تسجيل الدخول", "EmailPlaceholder": "بريدك الإلكتروني", "SendResetLink": "أرسل رابط إعادة التعيين", "loading": "جارٍ التحميل", "checkingYourAuthenticity": "جارٍ التحقق من هويتك", "ToUseBielaDev": "لاستخدام Biela.dev، يجب عليك تسجيل الدخول إلى حساب موجود أو إنشاء حساب باستخدام إحدى الخيارات أدناه", "Sign in with Google": "تسجيل الدخول باستخدام Google", "Sign in with GitHub": "تسجيل الدخول باستخدام GitHub", "Sign in with Email and Password": "تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور", "DontHaveAnAccount": "ليس لديك حساب؟", "SignUp": "إنشاء حساب", "ByUsingBielaDev": "باستخدامك Biela.dev، فإنك توافق على جمع بيانات الاستخدام.", "EmailOrUsernamePlaceholder": "البريد الإلكتروني/اسم المستخدم", "passwordPlaceholder": "كلمة المرور", "login.loading": "جارٍ التحميل", "LoginInToProfile": "تسجيل الدخول إلى ملفك الشخصي", "login.back": "رجوع", "forgotPassword": "نسيت كلمة المرور؟", "PasswordsMismatch": "كلمات المرور غير متطابقة.", "PhoneRequired": "رقم الها<PERSON><PERSON> مطلوب", "ConfirmPasswordRequired": "يرجى تأكيد كلمة المرور", "AcceptTermsRequired": "يج<PERSON> قبول شروط الخدمة وسياسة الخصوصية", "RegistrationFailed": "فشل التسجيل", "EmailConfirmationSent": "تم إرسال تأكيد عبر البريد الإلكتروني. يرجى تأكيد بريدك الإلكتروني ثم تسجيل الدخول.", "RegistrationServerError": "فشل التسجيل (أرجع الخادم نتيجة خاطئة).", "SomethingWentWrong": "حد<PERSON> خطأ ما", "CheckEmailConfirmRegistration": "تحقق من بريدك الإلكتروني لتأكيد التسجيل", "EmailConfirmationSentText": "لقد أرسلنا إليك رسالة بريد إلكتروني تحتوي على رابط تأكيد.", "LoginToProfile": "تسجيل الدخول إلى الملف الشخصي", "username": "اسم المستخدم", "email": "الب<PERSON>يد الإلكتروني", "PasswordPlaceholder": "كلمة المرور", "ConfirmPasswordPlaceholder": "تأكيد كلمة المرور", "AcceptTermsPrefix": "أ<PERSON><PERSON><PERSON><PERSON> على", "TermsOfService": "شروط الخدمة", "AndSeparator": "و", "PrivacyPolicy": "سياسة الخصوصية", "CreateAccount": "إنشاء حساب", "Back": "رجوع", "SignInWithGoogle": "تسجيل الدخول باستخدام Google", "SignInWithGitHub": "تسجيل الدخول باستخدام GitHub", "SignInWithEmailAndPassword": "تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور", "translation": {"AIModel": "نموذج الذكاء الاصطناعي", "UpgradePlan": "خطة متميزة", "PremiumBadge": "مميز", "Active": "نشط", "projectInfo.features": "الميزات", "Stats": "الإحصائيات", "Performance": "الأداء", "Cost": "التكلفة", "UpgradeTooltip": "افت<PERSON> بالترقية إلى ", "UpgradeTooltipSuffix": "الخطة", "chat": "دردشة", "code": "كود"}, "extendedThinkingTooltip": "فعّل التفكير العميق للذكاء الاصطناعي قبل الرد", "extendedThinkingTooltipDisabled": "عطّل التفكير العميق لتوفير الموارد", "AIModel": "نموذج الذكاء الاصطناعي", "UpgradePlan": "خطة متميزة", "PremiumBadge": "مميز", "Active": "نشط", "projectInfo.features": "الميزات", "Stats": "الإحصائيات", "Performance": "الأداء", "Cost": "التكلفة", "UpgradeTooltip": "افت<PERSON> بالترقية إلى ", "UpgradeTooltipSuffix": "الخطة", "HowBielaWorks": "كيف تعمل BIELA", "WatchPromptLaunch": "شاهد. اكتب. أطلق.", "SearchVideos": "ابحث في المكتبة...", "NewReleased": "الإصدار الجديد", "Playing": "يتم التشغيل", "NoVideosFound": "لم يتم العثور على فيديوهات", "TryAdjustingYourSearchTerms": "حاول تعديل كلمات البحث", "feedback": {"title": {"issue": "الإبلاغ عن مشكلة", "feature": "طل<PERSON> ميزة"}, "description": {"issue": "هل وجدت خطأ أو تواجه مشكلة؟ أخبرنا وسنقوم بإصلاحها في أقرب وقت ممكن.", "feature": "هل لديك فكرة لميزة جديدة؟ نود سماع اقتراحاتك لتحسين منصتنا."}, "success": {"title": "شكرًا لك!", "issue": "تم إرسال تقرير المشكلة بنجاح.", "feature": "تم إرسال طلب الميزة بنجاح."}, "form": {"fullName": {"label": "الاسم الكامل", "placeholder": "أد<PERSON>ل اسمك الكامل"}, "email": {"label": "عنوان البريد الإلكتروني", "placeholder": "أدخل عنوان بريدك الإلكتروني"}, "suggestion": {"labelIssue": "وصف المشكلة", "labelFeature": "اقتراح الميزة", "placeholderIssue": "صف المشكلة التي تواجهها...", "placeholderFeature": "صف الميزة التي تود رؤيتها..."}, "screenshots": {"label": "لقطات الشاشة (اختياري)", "note": "أضف لقطات شاشة لمساعدتنا على فهم المشكلة بشكل أفضل", "drop": "انقر للتحميل أو اسحب وأسقط الملفات هنا", "hint": "PNG، JPG، JPEG حتى 10MB لكل ملف", "count": "{{count}} صورة{{count > 1 ? 's' : ''}} محددة"}}, "buttons": {"cancel": "إلغاء", "submitting": "جارٍ الإرسال...", "submitIssue": "إرسال المشكلة", "submitFeature": "إرسال الطلب"}, "errors": {"fullNameRequired": "الاسم الكامل مطلوب", "fullNameNoNumbers": "لا يمكن أن يحتوي الاسم الكامل على أرقام", "emailRequired": "عنوان البريد الإلكتروني مطلوب", "emailInvalid": "ير<PERSON>ى إدخال عنوان بريد إلكتروني صالح", "suggestionRequired": "يرجى وصف مشكلتك أو اقتراحك"}}, "changelog": {"title": "سجل التغييرات", "description": "دائمًا في تطور. إلى الأمام.", "TotalReleases": "إجمالي الإصدارات", "ActiveUsers": "المستخدمون النشطون", "FeaturesAdded": "الميزات المضافة", "BugsFixed": "الأخطاء التي تم إصلاحها", "shortCallText": "هل لديك اقتراحات أو وجدت خطأ؟ يسعدنا أن نسمع منك!", "reportIssue": "الإبلاغ عن مشكلة", "requestFeature": "طل<PERSON> ميزة", "totalReleases": "إجمالي الإصدارات", "activeUsers": "المستخدمون النشطون", "featuresAdded": "الميزات المضافة", "bugsFixed": "الأخطاء المصححة", "types": {"feature": "ميزة", "improvement": "تحسين", "bugfix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ui/ux": "واجهة المستخدم/تجربة المستخدم", "announcement": "إعلان", "general": "عام"}, "versions": {"v3.2.3": {"date": "26 يونيو 2025", "changes": {"0": "تم إصلاح بعض مشاكل واجهة المستخدم/تجربة المستخدم في لوحة تحكم المستخدم.", "1": "تم تحسين استقرار WebContainer.", "2": "تم تحسين وضع AI Diff للتعامل مع الملفات الصغيرة بشكل مختلف عن الملفات الكبيرة."}}, "v3.2.2": {"date": "24 يونيو 2025", "changes": {"0": "تم إعادة تنظيم وإعادة تصميم المكتب الأمامي لتجربة مستخدم أفضل.", "1": "تم تنفيذ صفحة سجل التغييرات الرائعة هذه.", "2": "تم تحسين خوارزمية إعادة الاتصال لـ WebContainer.", "3": "تم إضافة دعم لرفع المشاريع عبر ملفات ZIP، بالإضافة إلى رفع المجلدات.", "4": "تم تحسين أداء جميع استدعاءات API من خلال الاستفادة من فهرسة أفضل."}}, "v3.2.1": {"date": "24 يونيو 2025", "changes": {"0": "IDE: تم إعادة تصميم قسم \"المجلدات\" في لوحة تحكم المستخدم للحصول على تجربة محسنة.", "1": "Content Studio: يمكنك الآن نسخ الصور داخل Content Studio."}}, "v3.2.0": {"date": "17 يونيو 2025", "changes": {"0": "وضع AI Diff (تجريبي): يكتب الذكي الاصطناعي الآن فقط الاختلافات بين الملف الأصلي والنسخة المحدثة، بدلاً من إعادة توليد الملف بأكمله. هذا يحسن الأداء بشكل كبير ويحسن استخدام الرموز المميزة. يمكنك تمكين أو تعطيل هذه الميزة التجريبية من منطقة الإعدادات ← مركز التحكم في مشروعك."}}, "v3.1.6": {"date": "12 يونيو 2025", "changes": {"0": "IDE: تم إضافة حقل رمز القسيمة للاشتراكات والرموز المميزة الإضافية."}}, "v3.1.5": {"date": "11 يونيو 2025", "changes": {"0": "IDE: تم تطبيق إصلاحات مختلفة على تدفق \"مشاركة مشروع\"."}}, "v3.1.4": {"date": "10 يونيو 2025", "changes": {"0": "IDE: تم إعادة فحص وتصحيح اشتراكات الحزم، معالجة مشاكل تضمين العملات المشفرة وتنفيذ Stripe."}}, "v3.1.3": {"date": "9 يونيو 2025", "changes": {"0": "AI: تم تنفيذ واختبار التكامل المباشر لـ API لـ Gemini لتحقيق زمن استجابة أفضل."}}, "v3.1.2": {"date": "6 يونيو 2025", "changes": {"0": "التسويق بالعمولة: تم تنفيذ مهام دفع التسويق بالعمولة مع اتساق التصميم مع نافذة معلومات لوحة التحكم الجديدة (تاريخ المدفوعات).", "1": "Content Studio: تم إضافة خيار جديد للمستخدمين لتحديد ما إذا كانوا سيرسلون صوراً إلى الذكي الاصطناعي مع الروابط. افتراضياً، هذا مضبوط على \"لا\"."}}, "v3.1.1": {"date": "5 يونيو 2025", "changes": {"0": "Content Studio: تم إجراء تحديثات لتنظيم Content Studio حسب المشروع."}}, "v3.1.0": {"date": "3 يونيو 2025", "changes": {"0": "🚀 تحديث BIELA.dev الكبير وصل للتو! 🚀", "1": "الذكي الاصطناعي يكتب الآن الكود بسرعة 5 أضعاف من هذا الصباح. نعم، قرأت بشكل صحيح. لقد قمنا بشحن أداء BIELA بقوة حتى تتمكن من الانتقال من الفكرة إلى الكود العامل في لمحة.", "2": "Terminal: الآن أفضل: بناءً على التجديد من هذا الصباح، أضفنا لمسة إضافية لواجهة المستخدم/تجربة المستخدم لتدفق برمجة أكثر سلاسة.", "3": "تحسينات اتصال Supabase: واجهة أكثر نظافة وبديهية تجعل الإعداد والتكامل سهلاً.", "4": "تحديث معالجة الأخطاء (المعالجة التلقائية للأخطاء): تم سحب الإبلاغ التلقائي عن الأخطاء. نحن نعد إعداداً جديداً حتى تتمكن من الاختيار بين المعالجة اليدوية والمساعدة بالذكي الاصطناعي.", "5": "مركز التحكم الجديد (في إعدادات المشروع) وواجهة اختبار الوحدة: الميزة الأولى: تبديل terminal اختبار الوحدة. المزيد من التحكمات قادمة!", "6": "منطق تحميل محسن: التحميلات تنتظر الآن حتى يتم دفع الكود بالكامل — مما يضمن تصديرات كاملة ودقيقة في كل مرة."}}, "v3.0.0": {"date": "3 يونيو 2025", "changes": {"0": "🚀 تحديث BIELA.dev - حصل للتو على دفعة قوة! 🚀", "1": "واجهة Terminal محدثة: تصميم أكثر أناقة ونظافة يجعل العمل في terminal متعة.", "2": "إجراءات NPM بنقرة واحدة: تشغيل npm install، npm run build، أو npm run dev فوراً بأزرار إجراء جديدة — لا حاجة للكتابة! (الميزة مدمجة في terminal)", "3": "معالجة أخطاء أذكى وتصنيف ألوان الأخطاء: أخطاء المعاينة ترسل الآن تلقائياً إلى الذكي الاصطناعي حتى يتمكن من مساعدتك في إصلاح الأخطاء بشكل أسرع. (ستتعرض لإصلاح أخطاء سلس بواسطة BIELA أثناء تطوير مشاريعك)", "4": "تحسين التنقل في الملفات: النقر على اسم ملف في المحادثة يفتحه الآن مباشرة في WebContainer. فقط اضغط وحرر!"}}, "v2.1.1": {"date": "30 مايو 2025", "changes": {"0": "IDE: تم إضافة زر حفظ لـ WebContainer، يظهر عند إجراء تحديثات يدوية، مما يؤدي إلى commit و push إلى GitHub.", "1": "لوحة التحكم: تم إصلاح خطأ حدث عند إعادة تسمية المشاريع المستوردة من لوحة تحكم المستخدم.", "2": "WebContainer: تم تنفيذ إصلاح عاجل لتسريب الذاكرة."}}, "v2.1.0": {"date": "28 مايو 2025", "changes": {"0": "🚀 تحديث BIELA.dev — 7 تحسينات جديدة وصلت للتو! 🚀", "1": "IDE: أضفنا إمكانية ربط نطاقك الخاص بمشروع biela الخاص بك، مباشرة من الواجهة.", "2": "معاينات المشاريع المنشورة: يمكنك الآن معاينة مشاريعك المنشورة بنقرة واحدة على أيقونة العين — لا حاجة لفتح كل واحد لمعرفة ما بداخله.", "3": "تتبع التكلفة المصحح في المشاريع المكررة: لقد صححنا مشكلة حيث كانت المشاريع المكررة ترث تقديرات التكلفة من المشروع الأصلي. الآن تبدأ من الصفر، مما يمنحك تتبعاً دقيقاً لكل مشروع.", "4": "تقدير التكلفة المباشر (لكل موجه): تحديثات تقديرات التكلفة الآن بعد كل موجه، وليس مرة واحدة فقط في اليوم. هذا يمنحك ملاحظات في الوقت الفعلي حول ما سيكلفه تطوير نفس الشيء مع وكالة تطوير — وكم توفر مع BIELA.", "5": "تبويب التاريخ والتراجع مصحح: يمكنك الآن التراجع بأمان في تاريخ المشروع مرة أخرى. بعد تحديثاتنا الكبيرة الأخيرة، كان هذا يواجه مشاكل — الآن سلس جداً.", "6": "تحسينات التمرير التلقائي: لا مزيد من الشاشات المجمدة أثناء البرمجة! أصلحنا المشكلة حيث كان التمرير يتأخر أثناء التوليد. استمتع بتدفقات سلسة.", "7": "صفحات القائمة تفتح الآن في تبويبات جديدة: إذا كنت تبرمج في Labs ونقرت على عناصر قائمة أخرى، فإنها تفتح الآن في تبويب جديد حتى لا تفقد عملك الجاري أبداً."}}, "v2.0.4": {"date": "27 مايو 2025", "changes": {"0": "لوحة التحكم: تحسينات لقطات الشاشة في لوحة تحكم المستخدم تشمل إزالة التمرير التلقائي، السماح بالتمرير المتحكم فيه من المستخدم، وإضافة رسوم متحركة لتحميل لقطات الشاشة."}}, "v2.0.3": {"date": "20 مايو 2025", "changes": {"0": "Content Studio: رسوم الروبوت المتحركة في Content Studio تعرض الآن بالحجم الكامل.", "1": "التسويق بالعمولة: تم إصلاح مشاكل العرض في لوحة تحكم التسويق بالعمولة عند المشاهدة في Firefox."}}, "v2.0.2": {"date": "19 مايو 2025", "changes": {"0": "Content Studio: تم زيادة العدد الأقصى للملفات المسموح بها مرة واحدة في Content Studio إلى 50 (من 10)."}}, "v2.0.1": {"date": "18 مايو 2025", "changes": {"0": "Content Studio: تم إصلاح مشكلة حيث كانت علامات الملفات المتعددة لا تحدث إلا إذا تم تحديث الصفحة.", "1": "Content Studio: زر التحرير عند اختيار صورة أصبح الآن أكثر وضوحاً.", "2": "Content Studio: عن<PERSON> الرفع، موقع المجلد يختار الآن تلقائياً آخر مجلد تم إنشاؤه أو آخر مجلد تم اختياره.", "3": "Content Studio: الم<PERSON><PERSON><PERSON> المختار يحتفظ الآن عند سحب وإسقاط الصور.", "4": "Content Studio: عنوان التبويب يستخدم الآن لوناً جديداً (بدلاً من الأخضر).", "5": "Content Studio: تم إعادة تسمية زر \"إدراج صورة\" إلى \"استخدام في المشروع\".", "6": "Content Studio: زر رفع الملفات له الآن خلفية خضراء.", "7": "Content Studio: تم تقليل ارتفاع شريط البحث لمحاذاة أفضل.", "8": "Content Studio: شريط البحث لم يعد يظهر إذا لم تكن هناك صور للمستخدم."}}, "v2.0.0": {"date": "16 مايو 2025", "changes": {"0": "🚀 تحديثات كبيرة في BIELA.DEV! 🚀", "1": "تقنية حاوية الويب الخاصة بنا: لقد بنينا تقنية حاوية الويب الخاصة بنا والتي هي مستقلة تماماً عن أي موردين خارجيين! هذا يمنحنا مرونة غير مسبوقة لتطوير ميزات جديدة بشكل أسرع وأفضل من أي وقت مضى.", "2": "خيارات LLM متعددة لتوليد الكود: نحن ندعم الآن نماذج ذكي اصطناعي متعددة لتوليد الكود ما بعد Anthropic! يمكنك الآن استخدام Gemini من Google لمشاريع البرمجة الخاصة بك، مما يجلب بعض المزايا الكبيرة. Gemini يقدم نافذة سياق ضخمة من 1,000,000 رمز مميز!", "3": "Content Studio: صورك، مشاريعك: عبر عن نفسك بالكامل مع Content Studio الجديد الخاص بنا! يمكنك الآن رفع صورك الخاصة لاستخدامها في مشاريع Vibe Coding الخاصة بك.", "4": "مشاركة المشاريع: التعاون أصبح للتو أفضل! يمكنك الآن مشاركة مشاريعك مع مستخدمين آخرين.", "5": "الاتصال بمشاريع Supabase الموجودة: هذه الميزة الجديدة القوية تسمح لك بربط مشاريع Biela.dev متعددة بنفس قاعدة بيانات Supabase."}}, "v1.0.5": {"date": "15 مايو 2025", "changes": {"0": "المدفوعات: تم تنفيذ معالجة مدفوعات Stripe.", "1": "Content Studio: رسوم متحركة (مثل الروبوت) تعرض الآن عندما لا توجد صورة في الصفحة الأولى."}}, "v1.0.4": {"date": "13 مايو 2025", "changes": {"0": "Content Studio: تم إصلاح مشاكل التصفح في Content Studio."}}, "v1.0.3": {"date": "5 مايو 2025", "changes": {"0": "إطلاق Content Studio - مكان لحفظ وإدارة جميع وسائطك.", "1": "المكتب الأمامي: تم تنفيذ schema.org لتطبيقات البرامج على biela_frontoffice.", "2": "لوحة تحكم الفريق: تم إضافة إشعارات إلى لوحة تحكم الفريق (مثل نقطة حمراء عند انضمام عضو جديد أو ظهور رسالة جديدة في المحادثة).", "3": "IDE: تم إصلا<PERSON> خطأ حيث لم تكن النافذة المنبثقة من القائمة تغلق بشكل صحيح عند النقر داخل WebContainer في مناطق معينة أثناء إعطاء موجه."}}, "v1.0.2": {"date": "2 مايو 2025", "changes": {"0": "التسويق بالعمولة: تم تنفيذ إعادة تصميم منطقة أعضاء الفريق.", "1": "التسويق بالعمولة: تم تحديث أيقونات لوحة تحكم التسويق بالعمولة.", "2": "IDE: تم تغيير نص زر \"اتصال Supabase\" إلى \"قاعدة البيانات\" مع أيقونة جديدة، وهو الآن يعرض \"قاعدة البيانات متصلة\" باللون الأزرق عند الاتصال."}}, "v1.0.1": {"date": "1 مايو 2025", "changes": {"0": "IDE (تبويب الإعدادات): تم تنفيذ تكامل قاعدة البيانات في محادثة الإعدادات.", "1": "لوحة التحكم: تم إصلاح التصفح في لوحة تحكم المستخدم."}}, "v1.0.0": {"date": "18 أبريل 2025", "changes": {"0": "🚀 مرحباً بك في Biela.dev: رفيقك في البرمجة المدعوم بالذكي الاصطناعي، مجاناً للجميع! 🚀", "1": "نحن متحمسون للإعلان عن الإصدار العام الأولي لـ Biela.dev! نحن نؤمن بجعل تطوير الويب المتطور المساعد بالذكي الاصطناعي متاحاً للجميع، ولهذا السبب منصتنا مجانية تماماً من اليوم الأول."}}}}}