{"meta": {"home": {"title": "biela.dev | AI-Powered Web & App Builder – Build with Prompts", "description": "Transform your ideas into live websites or apps with biela.dev. Use AI-driven prompts to build custom digital products effortlessly"}, "privacy": {"title": "biela.dev Privacy Policy", "description": "Understand how biela.dev collects, uses, and protects your personal information."}, "terms": {"title": "biela.dev Terms of Service", "description": "Review the terms and conditions for using biela.dev's AI-powered development platform"}, "changelogSection": {"title": "biela.dev Changelog - Always. Evolving. Forward.", "description": "Understand how biela.dev improves with every release"}, "competitionterms": {"title": "Biela Development Competition Terms & Conditions", "description": "Join the Biela Development Competition—an official one-month challenge by Biela.dev and TeachMeCode Institute. Showcase your digital builds, earn recognition through community engagement, and compete for top honors. Entries due by May 14, 2025."}, "contest": {"title": "biela.dev Development Competition 2025 – Winners Announced", "description": "See the winners of the biela.dev competition and discover the amazing AI-built projects that earned top honors"}, "howTo": {"title": "How to use Biela.dev – Complete Guide to Building AI-Powered Apps", "description": "Discover how to use Biela.dev to build web apps quickly and without code. A complete step-by-step guide with tutorials and practical tips.", "keywords": "biela, biela.dev, how to biela, create AI apps, no code, fast development, biela guide, biela tutorial", "ogTitle": "How to use Biela.dev – Step-by-Step Guide", "ogDescription": "Learn how to build AI apps with Biela.dev. From idea to launch, no coding required.", "twitterTitle": "How to use Biela.dev – Complete Guide", "twitterDescription": "Build AI apps on Biela.dev without coding. Follow this easy step-by-step guide!"}}, "navbar": {"home": "Home", "demo": "Demo", "community": "Community", "affiliate": "Affiliate", "partners": "Partners", "contest": "Vibe Coding Hackathon", "subscriptions": "Subscriptions", "pricing": "Pricing"}, "counter": {"days": "Days", "hours": "Hours", "minutes": "Minutes", "seconds": "Seconds", "centisec": "Centisec"}, "hero": {"title": "Turn Your Idea into a Live Website or App in Minutes", "subtitle": "If you can <1>imagine</1> it, you can <5>code</5> it.", "subtitle2": "Start for free. Code anything. Turn your skills into opportunity with every prompt.", "timeRunningOut": "Time is Running Out!", "launchDate": "Version WoodSnake", "experimentalVersion": "Beta Launch on 15th April 2025", "earlyAdopter": "Join now to lock in early adopter benefits before the official launch", "tryFree": "Try for Free", "seeAiAction": "See AI in Action", "tellBiela": "Tell Biela to create", "inputPlaceholder": "If you can imagine it, BIELA can code it, what shall we do today?", "chat": "Cha<PERSON>", "code": "Code", "checklist": "Checklist", "checklistTitle": "Use checklists to", "checklistItems": {"trackDevelopment": "Track development tasks", "setRequirements": "Set project requirements", "createTesting": "Create testing protocols"}, "registerNow": "Register now", "attachFiles": "Attach files to your prompt", "voiceInput": "Use voice input", "enhancePrompt": "Enhance prompt", "cleanupProject": "Clean-up project", "suggestions": {"weatherDashboard": "Create a weather dashboard", "ecommercePlatform": "Build an e-commerce platform", "socialMediaApp": "Design a social media app", "portfolioWebsite": "Generate a portfolio website", "taskManagementApp": "Create a task management app", "fitnessTracker": "Build a fitness tracker", "recipeSharingPlatform": "Design a recipe sharing platform", "travelBookingSite": "Create a travel booking site", "learningPlatform": "Build a learning platform", "musicStreamingApp": "Design a music streaming app", "realEstateListing": "Create a real estate listing", "jobBoard": "Build a job board"}}, "videoGallery": {"barber": {"title": "Barber Shop Website", "description": "See how Biela.dev creates a complete Barber Shop Website in minutes"}, "tictactoe": {"title": "<PERSON><PERSON>", "description": "Watch AI create a Tic <PERSON>c Toe faster than you say Tic Tac <PERSON>e"}, "coffeeShop": {"title": "Coffee Shop Website", "description": "Look at Biela.dev creating a Coffee Shop"}, "electronicsStore": {"title": "Electronics Store Website", "description": "See how Biela.dev creates a complete online store in minutes"}, "seoAgency": {"title": "SEO Agency Website", "description": "Watch AI create an SEO Agency Website"}}, "telegram": {"title": "Join Our Telegram", "subtitle": "Connect with the Biela community", "description": "Get instant support, share your projects, and connect with AI enthusiasts from around the world. <br/><br/> Our Telegram community is the fastest way to stay updated with Biela.dev.", "stats": {"members": "Members", "support": "Support", "countries": "Countries", "projects": "Projects"}, "joinButton": "Join Our Telegram Community", "communityTitle": "Biela.dev Community", "membersCount": "members", "onlineCount": "online now", "messages": {"1": "Just launched my e-commerce site with Biela.dev in under an hour!", "2": "That looks amazing! How did you handle the product catalog?", "3": "<PERSON><PERSON><PERSON> handled it automatically! I just described what I wanted.", "4": "I'm building a portfolio site right now. The AI suggestions are incredible!", "5": "Anyone tried the new responsive templates? They look fantastic on mobile."}}, "joinNetwork": {"title": "Join Our Network", "subtitle": "Connect. Grow. Earn.", "affiliateProgram": "Affiliate Program", "registerBring": "Register & Bring 3 Affiliates", "aiCourse": "AI Engineering Course", "courseDescription": "Free comprehensive AI course with TeachMeCode", "digitalBook": "Digital Book", "bookDescription": "\"The Art of Prompt Engineering\" exclusive digital edition", "exclusiveBenefits": "Exclusive Benefits", "benefitsDescription": "Lifetime earnings and special member privileges", "registerNow": "Register Now", "teamEarnings": "Team Earnings", "buildNetwork": "Build your network", "weeklyMentorship": "Weekly mentorship", "teamBonuses": "Team bonuses", "startEarning": "Start Earning Today", "upToCommission": "UP TO Commission"}, "partners": {"title": "Trusted by Industry Leaders", "subtitle": "Collaborating with global innovators to shape the future of AI development", "strategicPartnerships": "Strategic Partnerships", "joinNetwork": "Join the growing network of partners and companies transforming development with BIELA"}, "demo": {"title": "Watch AI Build Apps & Websites in Real-Time", "subtitle": "Explore how businesses are transforming their digital presence with Biela.dev", "seeInAction": "See Biela.dev in Action", "whatKind": "What kind of website do you need?", "describeWebsite": "Describe your website idea (e.g., 'A portfolio for a photographer')", "generatePreview": "Generate Preview", "nextDemo": "Next Demo", "startBuilding": "Start building & earning with Biela.dev Today"}, "footer": {"quickLinks": {"title": "Quick Links", "register": "Register", "bielaInAction": "See Biela.dev in Action", "liveOnTelegram": "We are Live on Telegram", "affiliate": "Collaborative Affiliate Marketing", "partners": "Partners"}, "legal": {"title": "Legal", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. All rights reserved.", "unauthorized": "Unauthorized use, reproduction, or distribution of this service, in whole or in part, without explicit written permission is strictly prohibited."}, "reservations": "TeachMeCode Institute reserves all legal rights to the intellectual property associated with Biela.", "terms": "Terms of Service", "privacy": "Privacy Policy"}, "bottomBar": {"left": "2025 Biela.dev. Powered by %s © All rights reserved.", "allRights": "All rights reserved.", "right": "Developed by %s"}}, "login": {"emailOrUsernamePlaceholder": "Email/Username", "passwordPlaceholder": "Password", "loading": "Loading", "back": "Back", "forgotPassword": "Forgot password?"}, "common": {"loading": "Loading...", "error": "An error occurred", "next": "Next", "previous": "Previous", "submit": "Submit", "cancel": "Cancel", "close": "Close", "loginNow": "Login Now", "verifyAccount": "Verify Account"}, "languageSwitcher": {"language": "Language", "english": "English", "spanish": "Español", "french": "Français", "german": "De<PERSON>ch", "russian": "Русский", "arabic": "العربية", "azerbaijani": "Azərbaycan"}, "contest": {"bielaDevelopment": "Vibe Coding Hackathon", "competition": "COMPETITION", "firstPlace": "1st PLACE", "secondPlace": "2nd PLACE", "thirdPlace": "3rd PLACE", "currentLeader": "Current Leader:", "footerText": "AI-powered development by Biela.dev, powered by TeachMeCode® Institute", "browseHackathonSubmissions": "<PERSON><PERSON>e Hackathon Submissions", "winnersAnnouncement": "Contest Winners Announced!", "congratulationsMessage": "Congratulations to our amazing winners! Thank you to everyone who participated in this incredible hackathon.", "conditionActiveReferrals": "Has 3 active referrals", "conditionLikedProject": "Liked one other project", "winner": "Winner", "qualificationsMet": "Qualifications:", "noQualifiedParticipant": "No Qualified Participant", "requirementsNotMet": "Requirements not met", "noQualificationDescription": "No participant met the qualification requirements for this position.", "qualifiedWinners": "🏆 Qualified Winners", "qualifiedWinnersMessage": "Congratulations to the participants who met all qualification requirements!", "noTopPrizesQualified": "No Participants Qualified for Top Prizes", "noTopPrizesMessage": "Unfortunately, no participants met the qualification requirements for the first three prize positions. All participants who qualified will be awarded from the remaining prize pool.", "noTopPrizesMessageShort": "Unfortunately, no participants met the qualification requirements for the first three prize positions."}, "competition": {"header": {"mainTitle": "Your prompt. Your style. Your playground.", "timelineTitle": "TIMELINE", "timelineDesc": "One month to create your best AI-generated project", "eligibilityTitle": "ELIGIBILITY", "eligibilityDesc": "Active Biela.dev accounts who submit a project", "prizesTitle": "PRIZES", "prizesDesc": "$16,000 in cash prizes plus free access for winners", "votingTitle": "VOTING", "votingDesc": "Community-driven, one vote per verified user", "tagline": "Let your creativity speak. Let the world vote."}, "details": {"howToEnter": "How to Enter", "enter": {"step1": {"title": "Create a project using Biela.dev AI", "desc": "Use Biela's powerful AI tools to build your innovative project"}, "step2": {"title": "Go to your User Dashboard", "desc": "Click \"Publish for Hackathon\" next to your project", "subdesc": "You can submit up to 3 different projects"}, "step3": {"title": "Biela will process your entry:", "list1": "Take a screenshot of your project", "list2": "Check your prompts for eligibility", "list3": "Generate summary & description"}}, "prizeStructure": "Prize Structure", "prizes": {"firstPlace": "1st Place", "firstPlaceValue": "$10,000", "secondPlace": "2nd Place", "secondPlaceValue": "$5,000", "thirdPlace": "3rd Place", "thirdPlaceValue": "$1,000", "fourthToTenth": "4th–10th Place", "fourthToTenthValue": "30 days unlimited access"}}, "qualification": {"heading": "Qualification Requirements", "description": "To qualify for the Top 3 prizes ($10,000, $5,000, and $1,000), you need to meet the following criteria:", "criteria1": "Have at least 3 active referrals", "criteria2": "Like 1 project from the showcase wall", "proTipLabel": "Pro Tip:", "proTipDesc": "The earlier you submit your project, the more visibility and votes it will receive!"}, "voting": {"heading": "Voting Guidelines", "oneVotePolicyTitle": "One Vote Policy", "oneVotePolicyDesc": "You can vote only once, and not for your own project", "accountVerificationTitle": "Account Verification", "accountVerificationDesc": "Only users with verified Biela.dev accounts can vote", "tieBreakingTitle": "Tie-Breaking", "tieBreakingDesc": "In case of a tie, the deciding factor will be the total number of Stars earned by the project's creator (used or unused)", "howToVoteTitle": "How to Vote", "howToVoteDesc": "Browse the Showcase Wall, explore all the published AI-generated projects, and click the heart icon to vote for your favorite!"}}, "contestItems": {"projectShowcase": "Project Showcase", "loading": "Loading...", "createBy": "Created by ", "viewProject": "View Project", "stars": "Stars", "overview": "Overview", "keyFeatures": "Key Features", "exploreLiveProject": "Explore the live project", "by": "by", "likeLimitReached": "Like Limit Reached", "youCanLikeMaximumOneProject": "You can like maximum one project. Would you like to unlike the current one and like this one instead?", "currentlyLikedProject": "Currently Liked Project", "switchMyLike": "Switch My Like", "mustLoggedIntoLikeProject": "You must be logged in to like a project.", "signInToBielaAccountToVote": "Please sign in to your Biela.dev account to participate in the voting process.", "yourAccountIsNotVerifiedYet": "Your account is not yet verified. Please verify your account to be able to like a project.", "accountVerificationEnsureFairVoting": "Account verification ensures fair voting and helps maintain the integrity of the competition.", "projectsSubmitted": "Projects submitted:", "unlikeThisProject": "Unlike this project", "likeThisProject": "Like this project", "likes": "likes", "like": "like", "somethingWentWrong": "Something went wrong. Please try again later", "voteError": "Vote error"}, "textsLiveYoutube": {"first": {"title": "You're Invited!", "description": "We're going live this Friday— and this session is all about building with <green>purpose</green>, <blue>precision</blue>, and <orange>pure vibe</orange>.", "secondDescription": "Learn how to <green>master vibe coding</green>, <blue>discover new features</blue> before anyone else, and <orange>see real projects</orange get built live.", "specialText": "Click here and hit \"Notify Me\" on YouTube — don't miss it."}, "second": {"title": "Be First to Build.", "description": "This Friday, we're taking <green>vibe coding</green> to a whole new level — showing you how to <blue>create with intent</blue>, <orange>unlock new features</orange>, and launch <green>faster than ever</green>.", "secondDescription": "<green>New builds</green>, <blue>new ideas</blue>, and <orange>new opportunities</orange> to grow.", "specialText": "Click here to set your YouTube reminder — and be part of the next wave of creators."}, "third": {"title": "Save Your Spot.", "description": "Our next <green>Biela.dev livestream</green> is happening this Friday — and you're <blue>officially invited</blue>.", "secondDescription": "We're diving deep into <green>vibe coding</green>, revealing <blue>powerful new features</blue>, and helping you build <orange>smarter, faster, and bigger<orange>.", "specialText": "Click here and tap \"Notify Me\" on YouTube — your next big idea could start here."}}, "stream": {"title": "Be <1>First</1> to <3>Build</3>.", "subtitle": "Catch our Friday livestream — features, projects, and exclusive updates.", "button": "Set Your YouTube Reminder."}, "billings": {"modalTitle": "Choose a Plan", "modalSubtitle": "Pick from our three flexible plans to keep coding without limits.", "toggleMonthly": "Monthly", "toggleYearly": "Yearly (save 10%)", "mostPopular": "Most Popular", "tokensLowerCase": "tokens", "tokensUpperCase": "Tokens", "below": "below", "unlimited": "UNLIMITED", "10Bonus": "10% Bonus", "currentPlan": "Current Plan", "upgradePlan": "Upgrade Plan", "purchaseDetails": "Purchase Details", "dateLabel": "Date", "planLabel": "Plan", "amountLabel": "Amount", "whatsNext": "What's Next", "yourTokensAvailable": "Your tokens are now available in your account", "purchaseDetailsStored": "Purchase details are stored in your account", "viewPurchaseHistory": "You can view your purchase history in the billing section", "thankYou": "Thank you!", "purchaseSuccessful": "Your purchase was successful", "startCoding": "Start Coding", "month": "month", "year": "year"}, "feature": {"basic_ai": "Basic AI Development", "community_support": "Community Support", "standard_response": "Standard Response Time", "basic_templates": "Basic Templates", "advanced_ai": "Advanced AI Development", "priority_support": "Priority Support", "fast_response": "Fast Response Time", "premium_templates": "Premium Templates", "team_collaboration": "Team Collaboration", "enterprise_ai": "Enterprise AI Development", "support_24_7": "24/7 Priority Support", "instant_response": "Instant Response Time", "custom_templates": "Custom Templates", "advanced_team": "Advanced Team Features", "custom_integrations": "Custom Integrations", "big_context_window": "Big Context Window - 5x Larger than the Medium one", "code_chat_ai": "Access to the Code & Chat AI", "tokens_basic": "Enough tokens for approx. 3 presentation websites", "standard_webcontainer": "Dedicated Standard Webcontainer", "medium_context_window": "Medium Context Window - Suitable for a website, a blog, or basic browser game", "basic_support": "Basic Support with Standard Response Time", "supabase_integration": "Supabase Integration", "project_sharing": "Share Projects with Your Friends", "project_download": "Download Your Projects", "project_deployment": "Deployment Feature", "custom_domain": "Connect Projects to Your Own Domain", "tokens_creator_plus": "Enough tokens for approx. 7 presentation websites", "advanced_support": "Advanced Support with Faster Response Time", "prioritary_support": "Dedicated Prioritary Support", "early_feature_access": "Early Access to New Features", "human_cto": "Dedicated Human CTO for Your Business", "community_promotion": "Promote Your Business with Our Community"}, "JoinUsLive": "Join Us Live", "howToUseBiela": {"subtitle": "How to use Biela", "title": "Explained in Minutes", "description": "Brief explanations of how each feature works"}, "slides": {"newHistoryFeature": "Discover Biela’s New HISTORY Feature!", "newHistoryFeatureDescription": "The HISTORY TAB keeps your vibe coding process seamless by letting you revisit and restore any previous version of your project, so you can stay in control!", "fixPreview": "How to Fix the Preview on Biela!", "fixPreviewDescription": "Watch the full guide and make sure everything shows up exactly how you built it.", "signUp": "How to Sign Up on Biela!", "signUpDescription": "New to biela.dev?Watch this quick guide to learn how to register and verify your account!", "buildWebsite": "How to Build a Website with Biela!", "buildWebsiteDescription": "Learn how to build and launch a website from scratch using biela.dev — without writing a single line of code.", "downloadAndImport": "How to Download and Import Projects & Chats on Biela!", "downloadAndImportDescription": "In this step-by-step walkthrough, you’ll learn how to download your completed projects and import saved chats into your workspace—so you can pick up right where you left off and keep your Vibe Coding flow going anytime.", "shareProject": "How to Share Your Vibe Code BIELA Project!", "shareProjectDescription": "Ready to share your project like a pro? Watch the full guide and get started.", "launchProject": "How to Launch your Project on Your Own Domain!", "launchProjectDescription": "In this video, we walk through the simple steps to take your website live from setup to final publish, you’ll get everything you need to confidently move from build to launch.", "deployProject": "How to Deploy Your Project and Go Live!", "deployProjectDescription": "This quick guide walks you through every step to deploy your site, app, or dashboard—fast and smooth."}, "maintenance": {"title": "We'll be back soon", "description": "Our site is currently undergoing scheduled maintenance. We're working hard to bring everything back online as soon as possible.", "text": "Thank you for your patience."}, "planCard": {"descriptionBeginner": "Perfect for beginners building their first projects and learning web development", "descriptionCreator": "Ideal for creators who need more power and flexibility for advanced projects", "descriptionProfessional": "Complete solution for professional developers and teams building applications", "perMillionTokens": "$ {{price}} / 1M tokens", "dedicatedCto": "Dedicated CTO - 24 Hours", "tokens": "Tokens", "perMonth": "per month", "perYear": "per year", "freeTokens": "{{ tokens }} Free Tokens", "monthly": "Monthly", "yearly": "Yearly", "save": "SAVE 10%"}, "faqLabel": "Frequently Asked Questions", "faqHeading": "See our most asked questions", "faqSubheading": "Start. Understand. Explore", "exploreHowToUseBiela": "Explore how to use Biela", "new": "New", "whatAreTokensQuestion": "What are tokens?", "whatAreTokensAnswer1": "Tokens are how the AI processes your project. On Biela.dev, tokens are used when you send a prompt to the AI. During this process, your project files are included in the message so the AI can understand your code. The larger your project and the longer the AI’s response, the more tokens are needed per message.", "whatAreTokensAnswer2": "Additionally, a small number of tokens are deducted from your account for every minute you use a webcontainer, to cover the cost of the development environment.", "freePlanTokensQuestion": "How many tokens do I get on the free plan?", "freePlanTokensAnswer": "Every user on the free plan gets 200,000 tokens per day, refreshed daily.", "outOfTokensQuestion": "What happens if I run out of tokens?", "outOfTokensAnswer": "No worries! You can always go to Menu → Billing and buy more tokens instantly when needed.", "changePlanQuestion": "Can I change my plan later?", "changePlanAnswer": "Yes — at any time. Just head to Menu → Billing to upgrade, downgrade, or switch plans based on your needs.", "rolloverQuestion": "Do unused tokens roll over?", "rolloverAnswer": "Currently, tokens reset at their expiration date and don’t roll over — so be sure to make the most of them each day!", "joinVibeCoding": "Join <PERSON> Hack<PERSON>on", "whatDoYouWantToBuild": "WHAT DO YOU WANT TO BUILD?", "imaginePromptCreate": "Imagine. Prompt. Create.", "levelOfAmbition": "What's your level of ambition", "startGrowScale": "Start. Grow. Scale.", "calloutBar": {"start": "All plans start free with", "tokens": "200,000 tokens per day", "end": ". No signup friction. Just start building."}, "atPriceOf": "at price of", "startFreeNoCreditCard": "Start Free - No Credit Card", "SignIn": "Sign In", "Register": "Register", "Affiliates": "Affiliates", "UsernameRequired": "Username is required", "PasswordRequired": "Password is required", "MissingUsernamePasswordOrTurnstile": "Missing username, password or Turnstile token", "LoginSuccess": "Login successful!", "LoginFailed": "<PERSON><PERSON> failed", "EmailRequired": "Email is required", "EmailInvalid": "Please enter a valid email address", "CaptchaRequired": "Please complete the CAPTCHA", "ResetLinkSent": "A reset link has been sent to your email.", "ResetFailed": "Failed to send reset link", "BackToLogin": "Back to login", "EmailPlaceholder": "Your email", "SendResetLink": "Send reset link", "loading": "Loading", "checkingYourAuthenticity": "Checking your authenticity", "ToUseBielaDev": "To use Biela.dev you must log into an existing account or create one using one of the options below", "Sign in with Google": "Sign in with Google", "Sign in with GitHub": "Sign in with GitHub", "Sign in with Email and Password": "Sign in with <PERSON><PERSON> and <PERSON><PERSON>", "DontHaveAnAccount": "Don't have an account?", "SignUp": "Sign me up", "ByUsingBielaDev": "By using Biela.dev you consent to usage data collection.", "EmailOrUsernamePlaceholder": "Email/Username", "passwordPlaceholder": "Password", "login.loading": "Loading", "LoginInToProfile": "Log In To Your Profile", "login.back": "Back", "forgotPassword": "Forgot password?", "PasswordsMismatch": "Passwords do not match.", "PhoneRequired": "Phone number is Required", "ConfirmPasswordRequired": "Please confirm your password", "AcceptTermsRequired": "You must accept the Terms of Service and Privacy Policy", "RegistrationFailed": "Registration failed", "EmailConfirmationSent": "Email confirmation was sent. Please confirm your email and then log in.", "RegistrationServerError": "Registration failed (server returned false).", "SomethingWentWrong": "Something went wrong", "CheckEmailConfirmRegistration": "Check your email to confirm your registration", "EmailConfirmationSentText": "We've sent you an email with a confirmation link.", "LoginToProfile": "Login to profile", "username": "Username", "email": "Email", "PasswordPlaceholder": "Password", "ConfirmPasswordPlaceholder": "Confirm Password", "AcceptTermsPrefix": "I agree to the", "TermsOfService": "Terms of Service", "AndSeparator": "&", "PrivacyPolicy": "Privacy Policy", "CreateAccount": "Create Account", "Back": "Back", "SignInWithGoogle": "Sign in with Google", "SignInWithGitHub": "Sign in with GitHub", "SignInWithEmailAndPassword": "Sign in with <PERSON><PERSON> and <PERSON><PERSON>", "translation": {"AIModel": "AI Model", "UpgradePlan": "a premium plan", "PremiumBadge": "PREMIUM", "Active": "Active", "projectInfo.features": "Features", "Stats": "Stats", "Performance": "Performance", "Cost": "Cost", "UpgradeTooltip": "Unlock by upgrading to ", "UpgradeTooltipSuffix": "package", "chat": "Cha<PERSON>", "code": "Code"}, "extendedThinkingTooltip": "Enable AI to think more deeply before responding", "extendedThinkingTooltipDisabled": "Disable extended thinking to save resources", "AIModel": "AI Model", "UpgradePlan": "a premium plan", "PremiumBadge": "PREMIUM", "Active": "Active", "projectInfo.features": "Features", "Stats": "Stats", "Performance": "Performance", "Cost": "Cost", "UpgradeTooltip": "Unlock by upgrading to ", "UpgradeTooltipSuffix": "package", "HowBielaWorks": "How BIELA Works", "WatchPromptLaunch": "Watch. Prompt. Launch.", "SearchVideos": "Search on library...", "NewReleased": "NEW RELEASED", "Playing": "Playing", "NoVideosFound": "No videos found", "TryAdjustingYourSearchTerms": "Try adjusting your search terms", "feedback": {"title": {"issue": "Report an Issue", "feature": "Request a Feature"}, "description": {"issue": "Found a bug or experiencing an issue? Let us know and we'll fix it as soon as possible.", "feature": "Have an idea for a new feature? We'd love to hear your suggestions to improve our platform."}, "success": {"title": "Thank you!", "issue": "Your issue report has been submitted successfully.", "feature": "Your feature request has been submitted successfully."}, "form": {"fullName": {"label": "Full Name", "placeholder": "Enter your full name"}, "email": {"label": "Email Address", "placeholder": "Enter your email address"}, "suggestion": {"labelIssue": "Issue Description", "labelFeature": "Feature Suggestion", "placeholderIssue": "Describe the issue you're experiencing...", "placeholderFeature": "Describe the feature you'd like to see..."}, "screenshots": {"label": "Screenshots (Optional)", "note": "Add screenshots to help us understand the issue better", "drop": "Click to upload or drag and drop", "hint": "PNG, JPG, JPEG up to 10MB each", "count": "{{count}} image{{count > 1 ? 's' : ''}} selected"}}, "buttons": {"cancel": "Cancel", "submitting": "Submitting...", "submitIssue": "Submit Issue", "submitFeature": "Submit Request"}, "errors": {"fullNameRequired": "Full name is required", "fullNameNoNumbers": "Full name cannot contain numbers", "emailRequired": "Email address is required", "emailInvalid": "Please enter a valid email address", "suggestionRequired": "Please describe your issue or suggestion"}}, "changelog": {"title": "Changelog", "description": "Always. Evolving. Forward", "TotalReleases": "Total Releases", "ActiveUsers": "Active Users", "FeaturesAdded": "Features Added", "BugsFixed": "Bugs Fixed", "totalReleases": "Total Releases", "activeUsers": "Active Users", "featuresAdded": "Features Added", "bugsFixed": "Bugs Fixed", "shortCallText": "Have suggestions or found a bug? We would love to hear from you!", "reportIssue": "Report an Issue", "requestFeature": "Request a Feature", "types": {"feature": "Feature", "improvement": "Improvement", "bugfix": "Bug Fix", "ui/ux": "UI/UX", "announcement": "Announcement", "general": "General"}, "versions": {"v3.2.3": {"date": "June 26, 2025", "changes": {"0": "Fixed some UI/UX issues on the user dashboard.", "1": "Improved WebContainer stability.", "2": "Improved AI Diff Mode to handle small files differently than large files."}}, "v3.2.2": {"date": "June 24, 2025", "changes": {"0": "Reorganized and Redesigned the frontoffice for a better user experience.", "1": "Implemented this ass-kicking changelog page.", "2": "Improved the reconnect algorithm of the WebContainer.", "3": "Added support for uploading projects via ZIP files, in addition to folder uploads.", "4": "Improved performance of all API calls by leveraging better indexing."}}, "v3.2.1": {"date": "June 24, 2025", "changes": {"0": "IDE: The user Dashboard's \"Folders\" section has been redesigned for an improved experience.", "1": "Content Studio: You can now copy images within the Content Studio."}}, "v3.2.0": {"date": "June 17, 2025", "changes": {"0": "AI Diff Mode (Experimental): The AI now writes only the differences between the original file and the updated version, rather than regenerating the entire file. This significantly improves performance and optimizes token usage. You can enable or disable this experimental feature from the Settings → Control Center area of your project."}}, "v3.1.6": {"date": "June 12, 2025", "changes": {"0": "IDE: A coupon code field has been added for subscriptions and extra tokens."}}, "v3.1.5": {"date": "June 11, 2025", "changes": {"0": "IDE: Various fixes have been applied to the \"Share a Project\" flow."}}, "v3.1.4": {"date": "June 10, 2025", "changes": {"0": "IDE: Package subscriptions have been rechecked and corrected, addressing crypto inclusion and Stripe implementation issues."}}, "v3.1.3": {"date": "June 9, 2025", "changes": {"0": "AI: Implemented and tested direct API integration for Gemini to achieve better latency."}}, "v3.1.2": {"date": "June 6, 2025", "changes": {"0": "Affiliate: Implemented Affiliate Payout Tasks with design consistency with the new dashboard's information modal (payout history).", "1": "Content Studio: A new option has been added for users to decide whether to send images to the AI along with links. By default, this is set to \"no.\""}}, "v3.1.1": {"date": "June 5, 2025", "changes": {"0": "Content Studio: Updates have been made to organize the Content Studio per project."}}, "v3.1.0": {"date": "June 3, 2025", "changes": {"0": "🚀 Major BIELA.dev Update Just Landed! 🚀", "1": "The AI now writes code 5x faster than this morning. Yes, you read that right. We've supercharged BIELA's performance so you can go from idea to working code in a flash.", "2": "Terminal: Now Even Better: Building on this morning's revamp, we've added extra UI/UX polish for a smoother coding flow.", "3": "Supabase Connection Improvements: A cleaner, more intuitive interface makes setup and integration effortless.", "4": "Error Handling Update (Auto-handling of Errors): Automatic error reporting has been rolled back. We're prepping a new setting so you can choose between manual and AI-assisted handling.", "5": "New Control Center (in Project Settings) & Unit Testing Interface: First feature: toggle the Unit Testing Terminal. Many more controls are on the way!", "6": "Improved Download Logic: Downloads now wait until your code is fully pushed — ensuring complete and accurate exports every time."}}, "v3.0.0": {"date": "June 3, 2025", "changes": {"0": "🚀 BIELA.dev Update - Just Got a Power Boost! 🚀", "1": "Revamped Terminal UI: A sleeker, cleaner design that makes working in the terminal a pleasure.", "2": "One-Click NPM Actions: Run npm install, npm run build, or npm run dev instantly with new action buttons—no typing needed! (the feature is integrated into the terminal)", "3": "Smarter Error Handling & Error Color Classification: Preview errors are now automatically sent to the AI so it can help you debug faster. (you will experience smooth bug fixing by BIELA while you develop your projects)", "4": "Improved File Navigation: Clicking on a file name in the chat now opens it directly in the WebContainer. Just tap and edit!"}}, "v2.1.1": {"date": "May 30, 2025", "changes": {"0": "IDE: A save button has been added for the WebContainer, appearing when manual updates are made, triggering a commit and push to GitHub.", "1": "Dashboard: Fixed an error that occurred when renaming imported projects from the user dashboard.", "2": "WebContainer: An urgent fix for a memory leak has been implemented."}}, "v2.1.0": {"date": "May 28, 2025", "changes": {"0": "🚀 BIELA.dev Update — 7 New Improvements Just Landed! 🚀", "1": "IDE: We added the possibility to link your own domain to your biela project, directly from the interface.", "2": "Deployed Project Previews: You can now preview your deployed projects with a single click on the eye icon — no need to open each one to know what's inside.", "3": "Fixed Cost Tracking in Duplicated Projects: We've corrected an issue where duplicated projects were inheriting cost estimations from the original project. Now they start from zero, giving you accurate tracking per project.", "4": "Live Cost Estimation (Per Prompt): Cost estimations now update after every prompt, not just once a day. This gives you real-time feedback on what it would cost to develop the same with a dev agency — and how much you save with BIELA.", "5": "History Tab & Rollback Fixed: You can now safely rollback project history again. After our recent major updates, this had issues — now it's buttery smooth.", "6": "Autoscroll Improvements: No more frozen screens while coding! We've fixed the issue where scrolling would lag behind during generation. Enjoy seamless flows.", "7": "Menu Pages Now Open in New Tabs: If you're coding in Labs and click on other menu items, they now open in a new tab so you never lose your current work-in-progress."}}, "v2.0.4": {"date": "May 27, 2025", "changes": {"0": "Dashboard: Screenshot improvements on the user dashboard include removing auto-scroll, allowing user-controlled scrolling, and adding an animation for loading screenshots."}}, "v2.0.3": {"date": "May 20, 2025", "changes": {"0": "Content Studio: The robot animation in Content Studio now displays at full size.", "1": "Affiliate: Fixed display issues on the Affiliate Dashboard when viewed in Firefox."}}, "v2.0.2": {"date": "May 19, 2025", "changes": {"0": "Content Studio: The maximum number of files allowed at once in Content Studio has been increased to 50 (from 10)."}}, "v2.0.1": {"date": "May 18, 2025", "changes": {"0": "Content Studio: Fixed an issue where tags for multiple files were not updating unless the page was refreshed.", "1": "Content Studio: The edit button when selecting an image is now more visible.", "2": "Content Studio: When uploading, the folder location now automatically selects the last created or last selected folder.", "3": "Content Studio: The selected folder is now retained when dragging and dropping images.", "4": "Content Studio: The tab title now uses a new color (instead of green).", "5": "Content Studio: The \"insert image\" button has been renamed to \"use in project.\"", "6": "Content Studio: The upload files button now has a green background.", "7": "Content Studio: The height of the search bar has been decreased for better alignment.", "8": "Content Studio: The search bar is no longer shown if there are no images for the user."}}, "v2.0.0": {"date": "May 16, 2025", "changes": {"0": "🚀 MAJOR UPDATES AT BIELA.DEV! 🚀", "1": "Our Own Web Container Technology: We've built our very own web container technology that's completely independent from any external providers! This gives us unprecedented flexibility to develop new features faster and better than ever before.", "2": "Multiple LLM Options for Code Generation: We now support multiple AI models for code generation beyond Anthropic! You can now use Google's Gemini for your coding projects, which brings some major advantages. Gemini offers a massive 1,000,000 token context window!", "3": "Content Studio: Your Photos, Your Projects: Express yourself fully with our new Content Studio! Now you can upload your own photos to use in your Vibe Coding projects.", "4": "Project Sharing: Collaboration just got better! Now you can share your projects with other users.", "5": "Connect to Existing Supabase Projects: This powerful new feature allows you to connect multiple Biela.dev projects to the same Supabase database."}}, "v1.0.5": {"date": "May 15, 2025", "changes": {"0": "Payments: Stripe payment processing has been implemented.", "1": "Content Studio: An animation (like the robot) is now displayed when there is no image on the first page."}}, "v1.0.4": {"date": "May 13, 2025", "changes": {"0": "Content Studio: Fixed pagination issues in Content Studio."}}, "v1.0.3": {"date": "May 5, 2025", "changes": {"0": "Launched Content Studio - a place to store and manage all your media.", "1": "Frontoffice: Implemented schema.org for Software apps on the biela_frontoffice.", "2": "Team Dashboard: Notifications have been added to the team dashboard (e.g., a red dot when a new member joins or a new message appears in chat).", "3": "IDE: Fixed a bug where the modal from the menu would not close properly when clicking inside the WebContainer in certain areas while giving a prompt."}}, "v1.0.2": {"date": "May 2, 2025", "changes": {"0": "Affiliate: Implemented the redesign of the team member area.", "1": "Affiliate: Affiliate Dashboard Icons have been updated.", "2": "IDE: The \"Supabase Connect\" button text has been changed to \"Database\" with a new icon, and it now displays \"Database Connected\" in blue when connected."}}, "v1.0.1": {"date": "May 1, 2025", "changes": {"0": "IDE (Settings Tab): Implemented database integration on the settings chat.", "1": "Dashboard: Fixed pagination on the user dashboard."}}, "v1.0.0": {"date": "April 18, 2025", "changes": {"0": "🚀 Welcome to Biela.dev: Your AI-Powered Coding Companion, Free for Everyone! 🚀", "1": "We're thrilled to announce the initial public release of Biela.dev! We believe in making cutting-edge AI-assisted web development accessible to all, which is why our platform is completely free to use from day one."}}}}}