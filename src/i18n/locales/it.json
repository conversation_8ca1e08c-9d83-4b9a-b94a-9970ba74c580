{"meta": {"home": {"title": "biela.dev | Costruttore di Web & App Basato su IA – Costruisci con i Prompt", "description": "Trasforma le tue idee in siti web o app funzionanti con biela.dev. Utilizza prompt guidati dall'IA per creare prodotti digitali personalizzati senza sforzo"}, "privacy": {"title": "Politica sulla Privacy di biela.dev", "description": "Scopri come biela.dev raccoglie, utilizza e protegge le tue informazioni personali."}, "terms": {"title": "Termini di Servizio di biela.dev", "description": "Esamina i termini e le condizioni per l'utilizzo della piattaforma di sviluppo basata su IA di biela.dev"}, "changelog": {"title": "Registro delle modifiche di biela.dev - Sempre. In evoluzione. In avanti.", "description": "Scopri come biela.dev si evolve con ogni versione"}, "competitionterms": {"title": "Termini e Condizioni del Concorso di Sviluppo Biela", "description": "Partecipa al Concorso di Sviluppo Biela—una sfida ufficiale di un mese organizzata da Biela.dev e dall'Istituto TeachMeCode. Mostra le tue creazioni digitali, ottieni riconoscimento grazie al coinvolgimento della community e gareggia per i primi posti. Scadenza delle iscrizioni: 14 maggio 2025."}, "contest": {"title": "Concorso di Sviluppo biela.dev 2025 – Vincitori Annunciati", "description": "Scopri i vincitori del concorso biela.dev ed esplora gli incredibili progetti costruiti con l'IA che hanno ottenuto i massimi riconoscimenti"}, "howTo": {"title": "Come usare Biela.dev – Guida completa per creare app con l'IA", "description": "Scopri come utilizzare Biela.dev per creare applicazioni web rapidamente e senza scrivere codice. Una guida completa passo dopo passo con tutorial e consigli pratici.", "keywords": "biela, biela.dev, come usare biela, creare app IA, no code, svil<PERSON><PERSON> rapido, guida biela, tutorial biela", "ogTitle": "Come usare Biela.dev – Guida passo passo", "ogDescription": "Impara a creare app IA con Biela.dev. <PERSON><PERSON>’idea al lancio, senza bisogno di programmare.", "twitterTitle": "Come usare Biela.dev – Guida completa", "twitterDescription": "Crea app IA su Biela.dev senza programmare. Segui questa guida semplice e completa!"}}, "navbar": {"home": "Home", "demo": "Demo", "community": "Comunità", "affiliate": "<PERSON><PERSON><PERSON><PERSON>", "partners": "Partner", "contest": "Hackathon di Codifica Vibe", "subscriptions": "Abbonamenti", "pricing": "<PERSON><PERSON>"}, "counter": {"days": "<PERSON><PERSON><PERSON>", "hours": "Ore", "minutes": "Minuti", "seconds": "Secondi", "centisec": "Centisecondi"}, "hero": {"title": "Trasforma la tua idea in un sito web o app live in pochi minuti", "subtitle": "Se puoi <1>immaginare</1> qualcosa, puoi <5>program<PERSON><PERSON></5>.", "subtitle2": "Inizia gratuitamente. Codifica tutto. Trasforma le tue capacità in ogni richiesta in un'opportunità.", "timeRunningOut": "Il tempo sta per scadere!", "launchDate": "<PERSON><PERSON>", "experimentalVersion": "Lancio beta il 15 aprile 2025", "earlyAdopter": "Is<PERSON>riviti ora per assicurarti i vantaggi da early adopter prima del lancio ufficiale", "tryFree": "<PERSON><PERSON>", "seeAiAction": "Guarda l'IA in Azione", "tellBiela": "Dì a Biela di creare", "inputPlaceholder": "Se puoi immaginarlo, BIELA può programmarlo. Cosa facciamo oggi?", "chat": "Cha<PERSON>", "code": "Codice", "checklist": "Lista di controllo", "checklistTitle": "Usa le liste di controllo per", "checklistItems": {"trackDevelopment": "Monitorare le attività di sviluppo", "setRequirements": "Stabilire i requisiti del progetto", "createTesting": "Creare protocolli di test"}, "registerNow": "Registrati ora", "attachFiles": "Allega file al tuo prompt", "voiceInput": "Utilizza input vocale", "enhancePrompt": "Mi<PERSON>ora il prompt", "cleanupProject": "Pulire il progetto", "suggestions": {"weatherDashboard": "Crea un cruscotto meteo", "ecommercePlatform": "Costruisci una piattaforma di e-commerce", "socialMediaApp": "Progetta un'app di social media", "portfolioWebsite": "Genera un sito portfolio", "taskManagementApp": "Crea un'app per la gestione delle attività", "fitnessTracker": "Costruisci un tracker per il fitness", "recipeSharingPlatform": "Progetta una piattaforma per la condivisione di ricette", "travelBookingSite": "Crea un sito per prenotazioni di viaggi", "learningPlatform": "Costruisci una piattaforma di apprendimento", "musicStreamingApp": "Progetta un'app di streaming musicale", "realEstateListing": "Crea un annuncio immobiliare", "jobBoard": "Costruisci un portale per annunci di lavoro"}}, "videoGallery": {"barber": {"title": "<PERSON><PERSON>", "description": "Guarda come Biela.dev crea un sito completo per barbieri in pochi minuti"}, "tictactoe": {"title": "Gioco del Tris", "description": "Guarda l'IA creare un gioco del tris più velocemente di quanto tu possa dire 'Tris'"}, "coffeeShop": {"title": "Sito per Caffetteria", "description": "Guarda Biela.dev mentre crea un sito per una caffetteria"}, "electronicsStore": {"title": "Sito per Negozio di Elettronica", "description": "Guarda come Biela.dev crea un negozio online completo in pochi minuti"}, "seoAgency": {"title": "Sito per Agenzia SEO", "description": "Guarda l'IA creare un sito per un'agenzia SEO"}}, "telegram": {"title": "Unisciti al nostro Telegram", "subtitle": "Connettiti con la community di Biela", "description": "Ricevi supporto immediato, condividi i tuoi progetti e connettiti con appassionati di IA da tutto il mondo. <br/><br/> La nostra community su Telegram è il modo più rapido per rimanere aggiornato su Biela.dev.", "stats": {"members": "<PERSON><PERSON><PERSON>", "support": "Supporto", "countries": "<PERSON><PERSON>", "projects": "<PERSON><PERSON><PERSON>"}, "joinButton": "Unisciti alla nostra community su Telegram", "communityTitle": "Community di Biela.dev", "membersCount": "membri", "onlineCount": "online ora", "messages": {"1": "Ho appena lanciato il mio sito e-commerce con Biela.dev in meno di un'ora!", "2": "Se<PERSON>ra fantastico! Come hai gestito il catalogo dei prodotti?", "3": "Biela se ne è occupata automaticamente! Ho semplicemente descritto ciò che volevo.", "4": "Sto creando un sito portfolio proprio ora. I suggerimenti dell'IA sono incredibili!", "5": "Qualcuno ha provato i nuovi template responsivi? Stanno benissimo sui dispositivi mobili."}}, "joinNetwork": {"title": "Unisciti alla nostra rete", "subtitle": "Connettiti. Cresci. Guadagna.", "affiliateProgram": "Programma di Affiliazione", "registerBring": "Registrati e porta 3 affiliati", "aiCourse": "Corso di Ingegneria IA", "courseDescription": "Corso completo di IA gratuito con TeachMeCode", "digitalBook": "Libro Digitale", "bookDescription": "Edizione digitale esclusiva di \"The Art of Prompt Engineering\"", "exclusiveBenefits": "Benefici Esclusivi", "benefitsDescription": "Guadagni a vita e privilegi speciali per i membri", "registerNow": "Registrati Ora", "teamEarnings": "Guadagni del Team", "buildNetwork": "Costruisci la tua rete", "weeklyMentorship": "Mentorship settimanale", "teamBonuses": "Bonus di team", "startEarning": "Inizia a guadagnare oggi", "upToCommission": "FINO A Commissione"}, "partners": {"title": "Fidato dai leader del settore", "subtitle": "Collaborando con innovatori globali per plasmare il futuro dello sviluppo dell'IA", "strategicPartnerships": "Partnership Strategiche", "joinNetwork": "Unisciti alla rete in crescita di partner e aziende che stanno trasformando lo sviluppo con BIELA"}, "demo": {"title": "Guarda l'IA costruire app e siti in tempo reale", "subtitle": "Scopri come le aziende stanno trasformando la loro presenza digitale con Biela.dev", "seeInAction": "Guarda Biela.dev in azione", "whatKind": "Che tipo di sito ti serve?", "describeWebsite": "Descrivi l'idea per il tuo sito (es. 'Un portfolio per un fotografo')", "generatePreview": "Genera Anteprima", "nextDemo": "Prossima Demo", "startBuilding": "Inizia a costruire e a guadagnare con Biela.dev oggi"}, "footer": {"quickLinks": {"title": "Link Rapidi", "register": "Registrati", "bielaInAction": "Guarda Biela.dev in azione", "liveOnTelegram": "Siamo live su Telegram", "affiliate": "Marketing di affiliazione collaborativo", "partners": "Partner"}, "legal": {"title": "Legale", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. Tutti i diritti riservati.", "unauthorized": "L'uso, la riproduzione o la distribuzione non autorizzata di questo servizio, in tutto o in parte, senza esplicita autorizzazione scritta è severamente vietata."}, "reservations": "Il TeachMeCode Institute si riserva tutti i diritti legali relativi alla proprietà intellettuale associata a Biela."}, "bottomBar": {"left": "2025 Biela.dev. Realizzato da %s © Tutti i diritti riservati.", "allRights": "<PERSON>tti i diritti riservati.", "right": "Sviluppato da %s"}}, "common": {"loading": "Caricamento...", "error": "Si è verificato un errore", "next": "<PERSON><PERSON>", "previous": "Precedente", "submit": "Invia", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON>", "loginNow": "Accedi ora", "verifyAccount": "Verifica account"}, "languageSwitcher": {"language": "<PERSON><PERSON>"}, "contest": {"bielaDevelopment": "Hackathon di Codifica Vibe", "competition": "COMPETIZIONE", "firstPlace": "1° POSTO", "secondPlace": "2° POSTO", "thirdPlace": "3° POSTO", "currentLeader": "Leader attuale:", "footerText": "Sviluppo con AI a cura di Biela.dev, supportato dall’Istituto TeachMeCode®", "browseHackathonSubmissions": "Sfoglia le iscrizioni all'Hackathon", "winnersAnnouncement": "🎉 Vincitori del Concorso Annunciati! 🎉", "congratulationsMessage": "Congratulazioni ai nostri incredibili vincitori! Grazie a tutti coloro che hanno partecipato a questo fantastico hackathon.", "conditionActiveReferrals": "Ha 3 referral attivi", "conditionLikedProject": "Ha messo mi piace a un altro progetto", "winner": "Vincitore", "qualificationsMet": "Qualifiche:", "noQualifiedParticipant": "<PERSON><PERSON><PERSON>", "requirementsNotMet": "Requisiti non soddisfatti", "noQualificationDescription": "<PERSON><PERSON>un partecipante ha soddisfatto i requisiti di qualificazione per questa posizione.", "qualifiedWinners": "🏆 Vincitori Qualificati", "qualifiedWinnersMessage": "Congratulazioni ai partecipanti che hanno soddisfatto tutti i requisiti di qualificazione!", "noTopPrizesQualified": "<PERSON><PERSON>un <PERSON> Qualificato per i Premi Principali", "noTopPrizesMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>, nessun partecipante ha soddisfatto i requisiti di qualificazione per le prime tre posizioni di premio. Tutti i partecipanti qualificati riceveranno premi dal pool di premi rimanente.", "noTopPrizesMessageShort": "<PERSON><PERSON><PERSON><PERSON><PERSON>, nessun partecipante ha soddisfatto i requisiti di qualificazione per le prime tre posizioni di premio."}, "competition": {"header": {"mainTitle": "Il tuo spunto. Il tuo stile. Il tuo campo da gioco.", "timelineTitle": "CALENDARIO", "timelineDesc": "Un mese per creare il tuo miglior progetto generato dall'IA", "eligibilityTitle": "AMMISSIBILITÀ", "eligibilityDesc": "Account attivi su Biela.dev che inviano un progetto", "prizesTitle": "PREMI", "prizesDesc": "16.000 $ in premi in denaro più accesso gratuito per i vincitori", "votingTitle": "VOTAZIONE", "votingDesc": "Guidato dalla comunità, un voto per ogni utente verificato", "tagline": "<PERSON>cia che la tua creatività parli. Lascia che il mondo voti."}, "details": {"howToEnter": "Come Partecipare", "enter": {"step1": {"title": "Crea un progetto utilizzando l'IA di Biela.dev", "desc": "Utilizza i potenti strumenti di IA di Biela per realizzare il tuo progetto innovativo"}, "step2": {"title": "Accedi al tuo pannello utente", "desc": "Fai clic su \"Pubblica per Hackathon\" accanto al tuo progetto", "subdesc": "Puoi inviare fino a 3 progetti diversi"}, "step3": {"title": "Biela elaborerà la tua partecipazione:", "list1": "Fai uno screenshot del tuo progetto", "list2": "Verifica che i tuoi spunti soddisfino i criteri di ammissibilità", "list3": "Genera un riepilogo e una descrizione"}}, "prizeStructure": "Struttura dei Premi", "prizes": {"firstPlace": "1° Posto", "firstPlaceValue": "$10,000", "secondPlace": "2° Posto", "secondPlaceValue": "$5,000", "thirdPlace": "3° Posto", "thirdPlaceValue": "$1,000", "fourthToTenth": "Dal 4° al 10° Posto", "fourthToTenthValue": "Accesso illimitato per 30 giorni"}}, "qualification": {"heading": "Requisiti di Qualificazione", "description": "Per qualificarti per i primi 3 premi ($10,000, $5,000 e $1,000), devi soddisfare i seguenti criteri:", "criteria1": "Devi avere almeno 3 referenze attive", "criteria2": "<PERSON><PERSON> piace ad almeno un progetto dalla vetrina", "proTipLabel": "Consiglio professionale:", "proTipDesc": "Prima invii il tuo progetto, maggiore sarà la visibilità e il numero di voti ricevuti!"}, "voting": {"heading": "Linee Guida per la Votazione", "oneVotePolicyTitle": "Regola del Voto Unico", "oneVotePolicyDesc": "Puoi votare una sola volta e non per il tuo stesso progetto", "accountVerificationTitle": "Verifica dell'Account", "accountVerificationDesc": "Solo gli utenti con account verificati su Biela.dev possono votare", "tieBreakingTitle": "Criterio di Spareggio", "tieBreakingDesc": "In caso di parità, il fattore decisivo sarà il numero totale di stelle ottenute dal creatore del progetto (utilizzate o meno)", "howToVoteTitle": "Come <PERSON>", "howToVoteDesc": "Sfoglia la vetrina, esplora tutti i progetti generati dall'IA pubblicati e clicca sull'icona del cuore per votare il tuo progetto preferito!"}}, "contestItems": {"projectShowcase": "Vetrina del Progetto", "loading": "Caricamento...", "createBy": "<PERSON><PERSON><PERSON> <PERSON> ", "viewProject": "Visualizza Progetto", "stars": "Stelle", "overview": "panoramica", "keyFeatures": "Caratteristiche principali", "exploreLiveProject": "Esplora il progetto live", "by": "da", "likeLimitReached": "Limite di like raggi<PERSON>o", "youCanLikeMaximumOneProject": "Puoi mettere mi piace a un solo progetto. Vuoi togliere il mi piace a quello attuale e mettere mi piace a questo?", "currentlyLikedProject": "Progetto attualmente apprezzato", "switchMyLike": "Cambia il mio mi piace", "mustLoggedIntoLikeProject": "Devi essere connesso per mettere mi piace a un progetto.", "signInToBielaAccountToVote": "Accedi al tuo account Biela.dev per partecipare al processo di voto.", "yourAccountIsNotVerifiedYet": "Il tuo account non è ancora verificato. Verifica il tuo account per poter mettere mi piace a un progetto.", "accountVerificationEnsureFairVoting": "La verifica dell'account garantisce un voto equo e aiuta a mantenere l'integrità della competizione.", "projectsSubmitted": "Progetti inviati:", "unlikeThisProject": "Non mi piace più questo progetto", "likeThisProject": "Mi piace questo progetto", "likes": "mi piace", "like": "mi piace", "somethingWentWrong": "Qualcosa è andato storto. Per favore riprova più tardi", "voteError": "Errore di voto"}, "textsLiveYoutube": {"first": {"title": "Sei invitato!", "description": "Saremo in diretta questo venerdì — questa sessione parla di costruire con <green>scopo</green>, <blue>precisione</blue> e <orange>pura vibe</orange>.", "secondDescription": "Scopri come <green>padroneggiare il vibe coding</green>, <blue>scoprire nuove funzionalità</blue> prima di chiunque altro e <orange>vedere progetti reali</orange> prendere forma in diretta.", "specialText": "<PERSON><PERSON><PERSON> qui e premi \"Notifica\" su YouTube — non mancarlo."}, "second": {"title": "Sii il primo a creare.", "description": "Questo venerdì portiamo il <green>vibe coding</green> a un livello completamente nuovo — mostrandoti come <blue>creare con intenzione</blue>, <orange>sbloccare nuove funzionalità</orange> e lanciare <green>più veloce che mai</green>.", "secondDescription": "<green>Nuove build</green>, <blue>nuove idee</blue> e <orange>nuove opportunità</orange> di crescita.", "specialText": "<PERSON><PERSON><PERSON> qui per impostare il promemoria YouTube — e fai parte della prossima ondata di creator."}, "third": {"title": "Riserva il tuo posto.", "description": "Il nostro prossimo <green>Biela.dev livestream</green> si terrà questo venerdì — e sei <blue>ufficialmente invitato</blue>.", "secondDescription": "Ci immergiamo nel <green>vibe coding</green>, riveliamo <blue>potenti nuove funzionalità</blue> e ti aiutiamo a costruire <orange>più intelligente, veloce e su larga scala</orange>.", "specialText": "<PERSON>lic<PERSON> qui e tocca \"Notifica\" su YouTube — la tua prossima grande idea potrebbe iniziare qui."}}, "stream": {"title": "<PERSON>i il <1>Primo</1> a <3>Costruire</3>.", "subtitle": "Partecipa al nostro livestream di venerdì — funzionalità, progetti e aggiornamenti esclusivi.", "button": "Imposta il tuo promemoria su YouTube."}, "billings": {"modalTitle": "<PERSON><PERSON>li un Piano", "modalSubtitle": "Scegli uno dei nostri tre piani flessibili per continuare a programmare senza limiti.", "toggleMonthly": "<PERSON><PERSON><PERSON>", "toggleYearly": "Annuale (risparmia il 10%)", "mostPopular": "Il Più Popolare", "tokensLowerCase": "token", "tokensUpperCase": "Token", "below": "sotto", "unlimited": "ILLIMITATO", "10Bonus": "10% di Bonus", "currentPlan": "Piano Attuale", "upgradePlan": "Aggiorna il Piano", "purchaseDetails": "Dettagli dell'Acquisto", "dateLabel": "Data", "planLabel": "Piano", "amountLabel": "Importo", "whatsNext": "E Ora?", "yourTokensAvailable": "I tuoi token sono ora disponibili nel tuo account", "purchaseDetailsStored": "I dettagli dell'acquisto sono salvati nel tuo account", "viewPurchaseHistory": "Puoi visualizzare la cronologia degli acquisti nella sezione fatturazione", "thankYou": "Grazie!", "purchaseSuccessful": "Acquisto completato con successo", "startCoding": "Inizia a programmare", "month": "mese", "year": "anno"}, "feature": {"basic_ai": "Sviluppo IA Base", "community_support": "Supporto Comunitario", "standard_response": "Tempo di Risposta Standard", "basic_templates": "Template Base", "advanced_ai": "Sviluppo IA Avanzato", "priority_support": "Supporto Prioritario", "fast_response": "Tempo di Risposta Rapido", "premium_templates": "Template Premium", "team_collaboration": "Collaborazione di Squadra", "enterprise_ai": "Sviluppo IA Aziendale", "support_24_7": "Supporto Prioritario 24/7", "instant_response": "Tempo di Risposta Istantaneo", "custom_templates": "Template <PERSON><PERSON><PERSON><PERSON>", "advanced_team": "Funzionalità Avanzate per il Team", "custom_integrations": "Integrazioni Personalizzate", "big_context_window": "Grande Finestra di Contesto - 5 volte più grande di quella media", "code_chat_ai": "Accesso all'IA per Codice e Chat", "tokens_basic": "Token sufficienti per circa 3 siti di presentazione", "standard_webcontainer": "Webcontainer Standard Dedicato", "medium_context_window": "Finestra di Contesto Media - Adatta per un sito, un blog o un gioco browser di base", "basic_support": "Supporto Base con Tempo di Risposta Standard", "supabase_integration": "Integrazione Supabase", "project_sharing": "Condividi i Progetti con i Tuoi Amici", "project_download": "Scarica i Tuoi Progetti", "project_deployment": "Funzionalità di Deployment", "custom_domain": "Collega i Progetti al Tuo Dominio Personalizzato", "tokens_creator_plus": "Token sufficienti per circa 7 siti di presentazione", "advanced_support": "Supporto Avanzato con Tempo di Risposta Più Veloce", "prioritary_support": "Supporto Prioritario Dedicato", "early_feature_access": "Accesso Anticipato alle Nuove Funzionalità", "human_cto": "CTO Umano Dedicato per la Tua Azienda", "community_promotion": "Promuovi la Tua Azienda con la Nostra Comunità"}, "JoinUsLive": "Unisciti a noi in diretta", "howToUseBiela": {"subtitle": "Come usare Biela", "title": "Spiegato in pochi minuti", "description": "Brevi spiegazioni su come funziona ogni funzione"}, "slides": {"newHistoryFeature": "Scopri la nuova funzione CRONOLOGIA di Biela!", "newHistoryFeatureDescription": "La scheda CRONOLOGIA mantiene fluido il tuo processo di vibe coding permettendoti di rivedere e ripristinare qualsiasi versione precedente del tuo progetto, così resti sempre in controllo!", "fixPreview": "Come correggere l'anteprima su Biela!", "fixPreviewDescription": "Guarda la guida completa e assicurati che tutto appaia esattamente come l'hai costruito.", "signUp": "Come registrarsi su Biela!", "signUpDescription": "Nuovo su biela.dev? Guarda questa guida rapida per imparare a registrarti e verificare il tuo account!", "buildWebsite": "Come creare un sito web con Biela!", "buildWebsiteDescription": "Sc<PERSON><PERSON> come creare e pubblicare un sito web da zero usando biela.dev — senza scrivere nemmeno una riga di codice.", "downloadAndImport": "Come scaricare e importare progetti e chat su Biela!", "downloadAndImportDescription": "In questa guida passo passo, imparerai a scaricare i tuoi progetti completati e importare le chat salvate nel tuo workspace — così potrai riprendere esattamente da dove avevi lasciato.", "shareProject": "Come condividere il tuo progetto Vibe Code di BIELA!", "shareProjectDescription": "Pronto a condividere il tuo progetto come un professionista? Guarda la guida completa e inizia subito.", "launchProject": "Come lanciare il tuo progetto sul tuo dominio personalizzato!", "launchProjectDescription": "In questo video, ti mostriamo i semplici passaggi per rendere il tuo sito online — dalla configurazione alla pubblicazione finale, tutto ciò che ti serve per passare con sicurezza dalla costruzione al lancio.", "deployProject": "Come distribuire il tuo progetto e andare online!", "deployProjectDescription": "Questa guida rapida ti accompagna in ogni fase per distribuire il tuo sito, app o dashboard — in modo rapido e fluido."}, "maintenance": {"title": "Stiamo lavorando per risolvere il problema", "description": "Biela.dev è in modalità di manutenzione. Stiamo lavorando per risolvere il problema", "text": "Grazie per la tua pazienza."}, "planCard": {"descriptionBeginner": "Perfetto per i principianti che stanno costruendo i loro primi progetti e imparando lo sviluppo web", "descriptionCreator": "Ideale per i creatori che necessitano di maggiore potenza e flessibilità per progetti a<PERSON>ti", "descriptionProfessional": "Soluzione completa per sviluppatori professionisti e team che costruiscono applicazioni", "perMillionTokens": "$ {{price}} / per milione di token", "dedicatedCto": "CTO dedicato - 24 ore", "tokens": "Token", "perMonth": "al mese", "perYear": "all'anno", "freeTokens": "{{ tokens }} Token gratuiti", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "Annuale", "save": "RISPARMIA 10%"}, "faqLabel": "<PERSON><PERSON><PERSON>", "faqHeading": "Consulta le nostre domande più frequenti", "faqSubheading": "Inizia. Comprendi. Esplora", "whatAreTokensQuestion": "Cosa sono i token?", "whatAreTokensAnswer1": "I token rappresentano il modo in cui l'IA elabora il tuo progetto. Su Biela.dev, vengono utilizzati quando invii una richiesta all'IA. Durante questo processo, i file del tuo progetto vengono inclusi nel messaggio affinché l'IA possa comprendere il tuo codice. Più grande è il progetto e più lunga è la risposta dell'IA, più token saranno necessari per ogni messaggio.", "whatAreTokensAnswer2": "<PERSON><PERSON><PERSON>, una piccola quantità di token viene scalata dal tuo account per ogni minuto di utilizzo di un webcontainer, per coprire i costi dell'ambiente di sviluppo.", "freePlanTokensQuestion": "Quanti token ricevo con il piano gratuito?", "freePlanTokensAnswer": "Ogni utente con il piano gratuito riceve 200.000 token al giorno, rinnovati quotidianamente.", "outOfTokensQuestion": "Cosa succede se finisco i token?", "outOfTokensAnswer": "Nessun problema! Puoi sempre andare su Menu → Fatturazione e acquistare immediatamente altri token quando necessario.", "changePlanQuestion": "<PERSON>sso cambiare piano in un secondo momento?", "changePlanAnswer": "Sì — in qualsiasi momento. Basta andare su Menu → Fatturazione per aggiornare, declassare o cambiare piano in base alle tue esigenze.", "rolloverQuestion": "I token inutilizzati si accumulano?", "rolloverAnswer": "At<PERSON><PERSON><PERSON><PERSON>, i token vengono azzerati alla data di scadenza e non si accumulano — quindi assicurati di utilizzarli ogni giorno!", "exploreHowToUseBiela": "<PERSON><PERSON><PERSON> come usare Biela", "new": "Nuovo", "joinVibeCoding": "Partecipa all'Hackathon Vibe Coding", "whatDoYouWantToBuild": "COSA VUOI COSTRUIRE?", "imaginePromptCreate": "Immagina. Suggerisci. Crea.", "levelOfAmbition": "Qual è il tuo livello di ambizione", "startGrowScale": "Inizia. Cresci. Scala.", "calloutBar": {"start": "<PERSON>tti i piani iniziano gratuitamente con", "tokens": "200.000 token al giorno", "end": ". Nessuna frizione nella registrazione. Inizia subito a creare."}, "atPriceOf": "al prezzo di", "startFreeNoCreditCard": "Inizia Gratis - Nessuna Carta di Credito", "SignIn": "Accedi", "Register": "Registrati", "Affiliates": "<PERSON><PERSON><PERSON><PERSON>", "UsernameRequired": "Il nome utente è obbligatorio", "PasswordRequired": "La password è obbligatoria", "MissingUsernamePasswordOrTurnstile": "<PERSON><PERSON> utente, password o token <PERSON><PERSON><PERSON> mancanti", "LoginSuccess": "Accesso effettuato con successo!", "LoginFailed": "Accesso non riuscito", "EmailRequired": "L'email è obbligatoria", "EmailInvalid": "Inserisci un indirizzo email valido", "CaptchaRequired": "Completa il CAPTCHA", "ResetLinkSent": "Un link per il reset è stato inviato alla tua email.", "ResetFailed": "Invio del link per il reset non riuscito", "BackToLogin": "Torna al login", "EmailPlaceholder": "La tua email", "SendResetLink": "Invia link per il reset", "loading": "Caricamento", "checkingYourAuthenticity": "Verifica dell'autenticità in corso", "ToUseBielaDev": "Per usare Biela.dev devi accedere a un account esistente o crearne uno utilizzando una delle opzioni qui sotto", "Sign in with Google": "Accedi con Google", "Sign in with GitHub": "Accedi con GitHub", "Sign in with Email and Password": "Accedi con email e password", "DontHaveAnAccount": "Non hai un account?", "SignUp": "Registrati", "ByUsingBielaDev": "Utilizzando Biela.dev acconsenti alla raccolta dei dati di utilizzo.", "EmailOrUsernamePlaceholder": "Email/Nome utente", "passwordPlaceholder": "Password", "login.loading": "Caricamento", "LoginInToProfile": "Accedi al tuo profilo", "login.back": "Indietro", "forgotPassword": "Password dimenticata?", "PasswordsMismatch": "Le password non corrispondono.", "PhoneRequired": "Il numero di telefono è obbligatorio", "ConfirmPasswordRequired": "Conferma la tua password", "AcceptTermsRequired": "Devi accettare i Termini di Servizio e l'Informativa sulla Privacy", "RegistrationFailed": "Registrazione non riuscita", "EmailConfirmationSent": "Conferma email inviata. Controlla la tua email e accedi dopo la conferma.", "RegistrationServerError": "Registrazione non riuscita (il server ha restituito false).", "SomethingWentWrong": "Qualcosa è andato storto", "CheckEmailConfirmRegistration": "Controlla la tua email per confermare la registrazione", "EmailConfirmationSentText": "Ti abbiamo inviato un'email con un link di conferma.", "LoginToProfile": "Accedi al profilo", "username": "Nome utente", "email": "Email", "PasswordPlaceholder": "Password", "ConfirmPasswordPlaceholder": "Conferma Password", "AcceptTermsPrefix": "Accetto i", "TermsOfService": "Termini di Servizio", "AndSeparator": "e", "PrivacyPolicy": "Informativa sulla Privacy", "CreateAccount": "<PERSON><PERSON> Account", "Back": "Indietro", "SignInWithGoogle": "Accedi con Google", "SignInWithGitHub": "Accedi con GitHub", "SignInWithEmailAndPassword": "Accedi con email e password", "translation": {"AIModel": "Modello IA", "UpgradePlan": "un piano premium", "PremiumBadge": "PREMIUM", "Active": "Attivo", "projectInfo.features": "Funzionalità", "Stats": "Statistiche", "Performance": "Prestazioni", "Cost": "Costo", "UpgradeTooltip": "Sblocca effettuando l'upgrade a ", "UpgradeTooltipSuffix": "p<PERSON><PERSON><PERSON>", "chat": "Cha<PERSON>", "code": "Codice"}, "extendedThinkingTooltip": "Abilita il pensiero approfondito dell'IA prima di rispondere", "extendedThinkingTooltipDisabled": "Disabilita il pensiero approfondito per risparmiare risorse", "AIModel": "Modello IA", "UpgradePlan": "un piano premium", "PremiumBadge": "PREMIUM", "Active": "Attivo", "projectInfo.features": "Funzionalità", "Stats": "Statistiche", "Performance": "Prestazioni", "Cost": "Costo", "UpgradeTooltip": "Sblocca effettuando l'upgrade a ", "UpgradeTooltipSuffix": "p<PERSON><PERSON><PERSON>", "HowBielaWorks": "Come funziona BIELA", "WatchPromptLaunch": "Guarda. Scrivi. Avvia.", "SearchVideos": "Cerca nella libreria...", "NewReleased": "NUOVO RILASCIO", "Playing": "In riproduzione", "NoVideosFound": "Nessun video trovato", "TryAdjustingYourSearchTerms": "Prova a modificare i termini di ricerca", "feedback": {"title": {"issue": "Se<PERSON>la un problema", "feature": "Richiedi una funzionalità"}, "description": {"issue": "Hai trovato un bug o stai riscontrando un problema? Facci sapere e lo risolveremo al più presto.", "feature": "Hai un'idea per una nuova funzionalità? Ci piacerebbe conoscere i tuoi suggerimenti per migliorare la nostra piattaforma."}, "success": {"title": "Grazie!", "issue": "Il tuo problema è stato inviato con successo.", "feature": "La tua richiesta di funzionalità è stata inviata con successo."}, "form": {"fullName": {"label": "Nome completo", "placeholder": "Inserisci il tuo nome completo"}, "email": {"label": "Indirizzo email", "placeholder": "Inserisci il tuo indirizzo email"}, "suggestion": {"labelIssue": "Descrizione del problema", "labelFeature": "Suggerimento per una funzionalità", "placeholderIssue": "Descrivi il problema che stai riscontrando...", "placeholderFeature": "Descrivi la funzionalità che vorresti vedere..."}, "screenshots": {"label": "Screenshot (opzionale)", "note": "Aggiungi screenshot per aiutarci a comprendere meglio il problema", "drop": "Clicca per caricare o trascina i file qui", "hint": "PNG, JPG, JPEG fino a 10MB ciascuno", "count": "{{count}} immagine selezionata"}}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "submitting": "Invio in corso...", "submitIssue": "Invia problema", "submitFeature": "Invia richiesta"}, "errors": {"fullNameRequired": "Il nome completo è richiesto", "fullNameNoNumbers": "Il nome completo non può contenere numeri", "emailRequired": "L'indirizzo email è richiesto", "emailInvalid": "Inserisci un indirizzo email valido", "suggestionRequired": "Descrivi il tuo problema o suggerimento"}}, "changelog": {"title": "Registro delle modifiche", "description": "Sempre. In evoluzione. Avanti.", "TotalReleases": "Rilasci totali", "ActiveUsers": "<PERSON><PERSON><PERSON> attivi", "FeaturesAdded": "Funzionalità aggiunte", "BugsFixed": "<PERSON><PERSON> corretti", "shortCallText": "Hai suggerimenti o hai trovato un bug? Ci farebbe piacere sentire la tua opinione!", "reportIssue": "Se<PERSON>la un problema", "requestFeature": "Richiedi una funzionalità", "totalReleases": "Rilasci Totali", "activeUsers": "<PERSON><PERSON><PERSON>", "featuresAdded": "Funzionalità Aggiunte", "bugsFixed": "<PERSON><PERSON>", "types": {"feature": "Funzionalità", "improvement": "Miglioramento", "bugfix": "Correzione Bug", "ui/ux": "UI/UX", "announcement": "<PERSON><PERSON><PERSON>", "general": "Generale"}, "versions": {"v3.2.3": {"date": "26 giugno 2025", "changes": {"0": "Risolti alcuni problemi UI/UX nella dashboard utente.", "1": "Migliorata la stabilità del WebContainer.", "2": "Migliorata la modalità AI Diff per gestire i file piccoli diversamente dai file grandi."}}, "v3.2.2": {"date": "24 giugno 2025", "changes": {"0": "Riorganizzato e ridisegnato il frontoffice per una migliore esperienza utente.", "1": "Implementata questa fantastica pagina del registro delle modifiche.", "2": "Migliorato l'algoritmo di riconnessione del WebContainer.", "3": "Aggiunto supporto per caricare progetti tramite file ZIP, oltre ai caricamenti di cartelle.", "4": "Migliorata la performance di tutte le chiamate API sfruttando una migliore indicizzazione."}}, "v3.2.1": {"date": "24 giugno 2025", "changes": {"0": "IDE: La sezione \"Cartelle\" della Dashboard Utente è stata riprogettata per un'esperienza migliorata.", "1": "Content Studio: Ora puoi copiare immagini all'interno del Content Studio."}}, "v3.2.0": {"date": "17 giugno 2025", "changes": {"0": "Modalità AI Diff (Sperimentale): L'IA ora scrive solo le differenze tra il file originale e la versione aggiornata, invece di rigenerare l'intero file. Questo migliora significativamente le prestazioni e ottimizza l'uso dei token. Puoi abilitare o disabilitare questa funzionalità sperimentale dall'area Impostazioni → Centro di Controllo del tuo progetto."}}, "v3.1.6": {"date": "12 giugno 2025", "changes": {"0": "IDE: È stato aggiunto un campo codice coupon per abbonamenti e token extra."}}, "v3.1.5": {"date": "11 giugno 2025", "changes": {"0": "IDE: Sono state applicate varie correzioni al flusso \"Condividi un Progetto\"."}}, "v3.1.4": {"date": "10 giugno 2025", "changes": {"0": "IDE: Gli abbonamenti ai pacchetti sono stati ricontrollati e corretti, affrontando problemi di inclusione crypto e implementazione Stripe."}}, "v3.1.3": {"date": "9 giugno 2025", "changes": {"0": "AI: Implementata e testata l'integrazione diretta dell'API per Gemini per ottenere una migliore latenza."}}, "v3.1.2": {"date": "6 giugno 2025", "changes": {"0": "Affiliati: Implementati i Task di Pagamento Affiliati con coerenza di design con il modal informativo della nuova dashboard (cronologia pagamenti).", "1": "Content Studio: È stata aggiunta una nuova opzione per gli utenti per decidere se inviare immagini all'IA insieme ai link. Di default, questo è impostato su \"no\"."}}, "v3.1.1": {"date": "5 giugno 2025", "changes": {"0": "Content Studio: Sono stati effettuati aggiornamenti per organizzare il Content Studio per progetto."}}, "v3.1.0": {"date": "3 giugno 2025", "changes": {"0": "🚀 Importante aggiornamento BIELA.dev appena arrivato! 🚀", "1": "L'IA ora scrive codice 5 volte più velocemente di stamattina. <PERSON>ì, hai letto bene. Abbiamo sovralimentato le prestazioni di BIELA così puoi andare dall'idea al codice funzionante in un lampo.", "2": "Terminal: Ora ancora meglio: Basandoci sulla ristrutturazione di stamattina, abbiamo aggiunto ulteriore rifinitura UI/UX per un flusso di codifica più fluido.", "3": "Miglioramenti della connessione Supabase: Un'interfaccia più pulita e intuitiva rende la configurazione e l'integrazione senza sforzo.", "4": "Aggiornamento gestione errori (Gestione automatica degli errori): La segnalazione automatica degli errori è stata ritirata. Stiamo preparando una nuova impostazione così puoi scegliere tra gestione manuale e assistita dall'IA.", "5": "Nuovo Centro di Controllo (nelle Impostazioni Progetto) e Interfaccia Unit Testing: Prima funzionalità: attivare il Terminal Unit Testing. Molti altri controlli stanno arrivando!", "6": "Logica di download migliorata: I download ora aspettano finché il tuo codice è completamente inviato, assicurando esportazioni complete e accurate ogni volta."}}, "v3.0.0": {"date": "3 giugno 2025", "changes": {"0": "🚀 Aggiornamento BIELA.dev - Ha appena ricevuto una spinta di potenza! 🚀", "1": "UI Terminal rinnovata: Un design più elegante e pulito che rende il lavoro nel terminal un piacere.", "2": "Azioni NPM con un clic: Esegui npm install, npm run build, o npm run dev istantaneamente con nuovi pulsanti di azione, non serve digitare! (la funzionalità è integrata nel terminal)", "3": "Gestione errori più intelligente e classificazione colori errori: Gli errori di anteprima vengono ora automaticamente inviati all'IA così può aiutarti a fare debug più velocemente. (sperimenterai correzione fluida dei bug da BIELA mentre sviluppi i tuoi progetti)", "4": "Navigazione file migliorata: Cliccare su un nome file nella chat ora lo apre direttamente nel WebContainer. Basta toccare e modificare!"}}, "v2.1.1": {"date": "30 maggio 2025", "changes": {"0": "IDE: È stato aggiunto un pulsante di salvataggio per il WebContainer, che appare quando vengono effettuati aggiornamenti manuali, attivando un commit e push a GitHub.", "1": "Dashboard: <PERSON><PERSON><PERSON><PERSON> un errore che si verificava durante la ridenominazione di progetti importati dalla dashboard utente.", "2": "WebContainer: È stata implementata una correzione urgente per una perdita di memoria."}}, "v2.1.0": {"date": "28 maggio 2025", "changes": {"0": "🚀 Aggiornamento BIELA.dev — 7 nuovi miglioramenti appena arrivati! 🚀", "1": "IDE: A<PERSON><PERSON><PERSON> aggiunto la possibilità di collegare il tuo dominio al tuo progetto biela, direttamente dall'interfaccia.", "2": "Anteprime progetti distribuiti: Ora puoi visualizzare in anteprima i tuoi progetti distribuiti con un singolo clic sull'icona dell'occhio, non serve aprire ognuno per sapere cosa c'è dentro.", "3": "Tracciamento costi corretto nei progetti duplicati: A<PERSON><PERSON><PERSON> corretto un problema dove i progetti duplicati ereditavano le stime dei costi dal progetto originale. <PERSON>a iniziano da zero, dandoti un tracciamento accurato per progetto.", "4": "Stima costi live (per prompt): Le stime dei costi ora si aggiornano dopo ogni prompt, non solo una volta al giorno. Questo ti dà feedback in tempo reale su quanto costerebbe sviluppare lo stesso con un'agenzia di sviluppo e quanto risparmi con BIELA.", "5": "Tab cronologia e rollback corretto: Ora puoi tornare indietro nella cronologia del progetto in sicurezza di nuovo. Dopo i nostri recenti aggiornamenti importanti, questo aveva problemi, ora è super fluido.", "6": "Miglioramenti autoscroll: Niente più schermate bloccate mentre programmi! Abbiamo risolto il problema dove lo scrolling rimaneva indietro durante la generazione. Goditi flussi fluidi.", "7": "Le pagine del menu ora si aprono in nuove schede: Se stai programmando in Labs e clicchi su altri elementi del menu, ora si aprono in una nuova scheda così non perdi mai il tuo lavoro in corso."}}, "v2.0.4": {"date": "27 maggio 2025", "changes": {"0": "Dashboard: I miglioramenti degli screenshot nella dashboard utente includono la rimozione dell'auto-scroll, permettendo scrolling controllato dall'utente e aggiungendo un'animazione per caricare gli screenshot."}}, "v2.0.3": {"date": "20 maggio 2025", "changes": {"0": "Content Studio: L'animazione del robot nel Content Studio ora viene visualizzata a dimensione piena.", "1": "Affiliati: Risolti problemi di visualizzazione nella Dashboard Affiliati quando visualizzata in Firefox."}}, "v2.0.2": {"date": "19 maggio 2025", "changes": {"0": "Content Studio: Il numero massimo di file consentiti contemporaneamente nel Content Studio è stato aumentato a 50 (da 10)."}}, "v2.0.1": {"date": "18 maggio 2025", "changes": {"0": "Content Studio: R<PERSON>olto un problema dove i tag per file multipli non si aggiornavano a meno che la pagina non fosse ricaricata.", "1": "Content Studio: Il pulsante di modifica quando si seleziona un'immagine è ora più visibile.", "2": "Content Studio: <PERSON><PERSON><PERSON> il caricamento, la posizione della cartella ora seleziona automaticamente l'ultima cartella creata o l'ultima selezionata.", "3": "Content Studio: La cartella selezionata ora viene mantenuta quando si trascinano e rilasciano immagini.", "4": "Content Studio: Il titolo della scheda ora usa un nuovo colore (invece del verde).", "5": "Content Studio: Il pulsante \"inserisci immagine\" è stato rinominato in \"usa nel progetto\".", "6": "Content Studio: Il pulsante carica file ora ha uno sfondo verde.", "7": "Content Studio: L'altezza della barra di ricerca è stata diminuita per un migliore allineamento.", "8": "Content Studio: La barra di ricerca non viene più mostrata se non ci sono immagini per l'utente."}}, "v2.0.0": {"date": "16 maggio 2025", "changes": {"0": "🚀 AGGIORNAMENTI IMPORTANTI SU BIELA.DEV! 🚀", "1": "La nostra tecnologia di contenitore web: Abbiam<PERSON> costruito la nostra tecnologia di contenitore web che è completamente indipendente da qualsiasi fornitore esterno! Questo ci dà flessibilità senza precedenti per sviluppare nuove funzionalità più velocemente e meglio che mai.", "2": "Opzioni LLM multiple per la generazione di codice: Ora supportiamo modelli AI multipli per la generazione di codice oltre ad Anthropic! Ora puoi usare Gemini di Google per i tuoi progetti di codifica, che porta alcuni vantaggi importanti. Gemini offre una finestra di contesto massiva di 1.000.000 di token!", "3": "Content Studio: Le tue foto, i tuoi progetti: Esprimiti completamente con il nostro nuovo Content Studio! Ora puoi caricare le tue foto da usare nei tuoi progetti Vibe Coding.", "4": "Condivisione progetti: La collaborazione è appena migliorata! Ora puoi condividere i tuoi progetti con altri utenti.", "5": "Connetti a progetti Supabase esistenti: Questa potente nuova funzionalità ti permette di connettere progetti Biela.dev multipli allo stesso database Supabase."}}, "v1.0.5": {"date": "15 maggio 2025", "changes": {"0": "Pagamenti: È stata implementata l'elaborazione pagamenti Stripe.", "1": "Content Studio: Un'animazione (come il robot) viene ora visualizzata quando non c'è immagine nella prima pagina."}}, "v1.0.4": {"date": "13 maggio 2025", "changes": {"0": "Content Studio: Risolti problemi di paginazione nel Content Studio."}}, "v1.0.3": {"date": "5 maggio 2025", "changes": {"0": "Lanciato Content Studio - un posto per archiviare e gestire tutti i tuoi media.", "1": "Frontoffice: Implementato schema.org per app software sul biela_frontoffice.", "2": "Dashboard team: Sono state aggiunte notifiche alla dashboard team (es. un punto rosso quando un nuovo membro si unisce o appare un nuovo messaggio nella chat).", "3": "IDE: <PERSON><PERSON><PERSON><PERSON> un bug dove il modal dal menu non si chiudeva correttamente quando si cliccava dentro il WebContainer in certe aree mentre si dava un prompt."}}, "v1.0.2": {"date": "2 maggio 2025", "changes": {"0": "Affiliati: Implementato il redesign dell'area membri del team.", "1": "Affiliati: Le icone della Dashboard Affiliati sono state aggiornate.", "2": "IDE: Il testo del pulsante \"Connetti Supabase\" è stato cambiato in \"Database\" con una nuova icona, e ora mostra \"Database Connesso\" in blu quando connesso."}}, "v1.0.1": {"date": "1 maggio 2025", "changes": {"0": "IDE (Tab Impostazioni): Implementata l'integrazione database nella chat delle impostazioni.", "1": "Dashboard: <PERSON><PERSON><PERSON><PERSON> la paginazione nella dashboard utente."}}, "v1.0.0": {"date": "18 aprile 2025", "changes": {"0": "🚀 Benvenuto su Biela.dev: Il tuo compagno di codifica alimentato dall'IA, gratis per tutti! 🚀", "1": "Siamo entusiasti di annunciare il rilascio pubblico iniziale di Biela.dev! Crediamo nel rendere lo sviluppo web assistito dall'IA all'avanguardia accessibile a tutti, ecco perché la nostra piattaforma è completamente gratuita dal primo giorno."}}}}}