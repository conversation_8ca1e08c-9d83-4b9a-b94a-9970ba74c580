{"meta": {"home": {"title": "biela.dev | Конструктор веб-сайтов и приложений на ИИ – Создавайте с помощью промптов", "description": "Превратите свои идеи в рабочие веб-сайты или приложения с biela.dev. Используйте промпты на базе ИИ для создания индивидуальных цифровых продуктов без усилий"}, "privacy": {"title": "Политика конфиденциальности biela.dev", "description": "Узнайте, как biela.dev собирает, использует и защищает вашу личную информацию."}, "terms": {"title": "Условия использования biela.dev", "description": "Ознакомьтесь с правилами и условиями использования платформы разработки на базе ИИ biela.dev"}, "changelog": {"title": "<PERSON>у<PERSON><PERSON>л изменений biela.dev – Всегда. Развивается. Вперед.", "description": "Узнайте, как biela.dev совершенствуется с каждым выпуском"}, "competitionterms": {"title": "Правила и условия конкурса по разработке Biela", "description": "Примите участие в официальном месячном конкурсе разработки от Biela.dev и института TeachMeCode. Представьте свои цифровые проекты, получите признание через вовлеченность сообщества и поборитесь за победу. Срок подачи заявок — до 14 мая 2025 года."}, "contest": {"title": "Конкурс разработки biela.dev 2025 – Выиграйте $10,000", "description": "Присоединяйтесь к конкурсу biela.dev, чтобы представить свои проекты, созданные с помощью ИИ, и выиграть денежные призы"}, "howTo": {"title": "Как использовать Biela.dev – Полное руководство по созданию приложений на базе ИИ", "description": "Узнайте, как использовать Biela.dev для быстрой разработки веб-приложений без написания кода. Полное пошаговое руководство с учебными материалами и практическими советами.", "keywords": "biela, biela.dev, как использовать biela, создать ИИ-приложения, без кода, быстрая разработка, руководство biela, учебник biela", "ogTitle": "Как использовать Biela.dev – Пошаговое руководство", "ogDescription": "Научитесь создавать ИИ-приложения с помощью Biela.dev. От идеи до запуска — без программирования.", "twitterTitle": "Как использовать Biela.dev – Полное руководство", "twitterDescription": "Создавайте ИИ-приложения на Biela.dev без кода. Следуйте этому простому пошаговому руководству!"}}, "navbar": {"home": "Главная", "demo": "Демо", "community": "Сообщество", "affiliate": "Партнерская программа", "partners": "Партнеры", "contest": "Хак<PERSON><PERSON>он Vibe Coding", "subscriptions": "Подписки", "pricing": "Цены"}, "counter": {"days": "<PERSON><PERSON>и", "hours": "<PERSON>а<PERSON>ы", "minutes": "Минуты", "seconds": "Секунды", "centisec": "Сотые секунды"}, "hero": {"title": "Превратите вашу идею в рабочий веб-сайт или приложение за считанные минуты", "timeRunningOut": "Время на исходе!", "subtitle": "Если вы можете <1>вообразить</1> это, вы можете <5>закодировать</5> это.", "subtitle2": "Начните бесплатно. Кодируйте всё. Превратите свои навыки с каждым запросом в возможность.", "launchDate": "Версия WoodSnake", "experimentalVersion": "Бета-запуск 15 апреля 2025 года", "earlyAdopter": "Присоединяйтесь сейчас, чтобы получить преимущества раннего доступа до официального запуска", "tryFree": "Попробуйте бесплатно", "seeAiAction": "Смотрите, как работает ИИ", "tellBiela": "Попросите Biela создать", "inputPlaceholder": "Если вы можете это представить, BIELA может это запрограммировать. Что мы будем делать сегодня?", "chat": "Чат", "code": "<PERSON>од", "checklist": "Чек-лист", "checklistTitle": "Используйте чек-листы для", "checklistItems": {"trackDevelopment": "Отслеживайте задачи разработки", "setRequirements": "Определяйте требования проекта", "createTesting": "Создавайте протоколы тестирования"}, "registerNow": "Зарегистрируйтесь сейчас", "attachFiles": "Прикрепите файлы к вашему запросу", "voiceInput": "Используйте голосовой ввод", "enhancePrompt": "Улучшите запрос", "cleanupProject": "Очистите проект", "suggestions": {"weatherDashboard": "Создайте панель погоды", "ecommercePlatform": "Создайте платформу для электронной коммерции", "socialMediaApp": "Разработайте приложение для социальных сетей", "portfolioWebsite": "Создайте сайт-портфолио", "taskManagementApp": "Создайте приложение для управления задачами", "fitnessTracker": "Создайте фитнес-трекер", "recipeSharingPlatform": "Разработайте платформу для обмена рецептами", "travelBookingSite": "Создайте сайт для бронирования путешествий", "learningPlatform": "Создайте образовательную платформу", "musicStreamingApp": "Разработайте приложение для потоковой передачи музыки", "realEstateListing": "Создайте каталог недвижимости", "jobBoard": "Создайте сайт с вакансиями"}}, "videoGallery": {"barber": {"title": "Сайт парикмахерской", "description": "Смотрите, как Biela.dev за считанные минуты создает полноценный сайт парикмахерской"}, "tictactoe": {"title": "Игра Крестики-нолики", "description": "Смотрите, как ИИ создает игру 'Крестики-нолики' быстрее, чем вы успеете её назвать"}, "coffeeShop": {"title": "Сайт кофейни", "description": "Посмотрите, как Biela.dev создает кофейню"}, "electronicsStore": {"title": "Сайт магазина электроники", "description": "Смотрите, как Biela.dev за считанные минуты создает полноценный интернет-магазин"}, "seoAgency": {"title": "Сайт SEO-агентства", "description": "Смотрите, как ИИ создает сайт SEO-агентства"}}, "telegram": {"title": "Присоединяйтесь к нашему Telegram", "subtitle": "Свяжитесь с сообществом Biela", "description": "Получайте мгновенную поддержку, делитесь своими проектами и общайтесь с энтузиастами ИИ со всего мира. <br/><br/> Наше сообщество в Telegram – самый быстрый способ быть в курсе новостей Biela.dev.", "stats": {"members": "Участники", "support": "Поддержка", "countries": "Страны", "projects": "Проекты"}, "joinButton": "Присоединиться к нашему сообществу в Telegram", "communityTitle": "Сообщество Biela.dev", "membersCount": "уча<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineCount": "сейчас онлайн", "messages": {"1": "Только что запустил свой интернет-магазин с Biela.dev менее чем за час!", "2": "Выглядит потрясающе! Как вы справились с каталогом товаров?", "3": "Biela сделала это автоматически! Я просто описал, что хотел.", "4": "Сейчас я создаю сайт-портфолио. Предложения от ИИ невероятны!", "5": "Кто-нибудь пробовал новые адаптивные шаблоны? Они выглядят фантастически на мобильных устройствах."}}, "joinNetwork": {"title": "Присоединяйтесь к нашей сети", "subtitle": "Связаться. Развиваться. Зарабатывать.", "affiliateProgram": "Партнерская программа", "registerBring": "Зарегистрируйтесь и приведите 3 партнеров", "aiCourse": "Курс по инженерии ИИ", "courseDescription": "Бесплатный комплексный курс по ИИ от TeachMeCode", "digitalBook": "Цифровая книга", "bookDescription": "Эксклюзивное цифровое издание «Искусство создания запросов»", "exclusiveBenefits": "Эксклюзивные преимущества", "benefitsDescription": "Пожизненный доход и особые привилегии для участников", "registerNow": "Зарегистрируйтесь сейчас", "teamEarnings": "Заработок команды", "buildNetwork": "Создайте свою сеть", "weeklyMentorship": "Еженедельное наставничество", "teamBonuses": "Бонусы для команды", "startEarning": "Начните зарабатывать уже сегодня", "upToCommission": "до комиссионных"}, "partners": {"title": "Доверяют лидеры отрасли", "subtitle": "Сотрудничаем с мировыми инноваторами, чтобы формировать будущее разработки ИИ", "strategicPartnerships": "Стратегические партнерства", "joinNetwork": "Присоединяйтесь к растущей сети партнеров и компаний, меняющих развитие вместе с BIELA"}, "demo": {"title": "Смотрите, как ИИ создает приложения и сайты в реальном времени", "subtitle": "Узнайте, как бизнесы меняют своё цифровое присутствие с помощью Biela.dev", "seeInAction": "Смотрите Biela.dev в действии", "whatKind": "Какой сайт вам нужен?", "describeWebsite": "Опишите идею вашего сайта (например, 'Портфолио фотографа')", "generatePreview": "Сгенерировать предварительный просмотр", "nextDemo": "Следующее демо", "startBuilding": "Начните создавать и зарабатывать с Biela.dev уже сегодня"}, "footer": {"quickLinks": {"title": "Быстрые ссылки", "register": "Регистрация", "bielaInAction": "Смотрите Biela.dev в действии", "liveOnTelegram": "Мы в Telegram", "affiliate": "Совместный партнерский маркетинг", "partners": "Партнеры"}, "legal": {"title": "Юридическая информация", "glassCard": {"copyright": "© 2025 Институт TeachMeCode. Все права защищены.", "unauthorized": "Несанкционированное использование, воспроизведение или распространение данного сервиса, полностью или частично, без явного письменного разрешения строго запрещено."}, "reservations": "Институт TeachMeCode сохраняет за собой все юридические права на интеллектуальную собственность, связанную с Biela."}, "bottomBar": {"left": "2025 Biela.dev. Работает на %s © Все права защищены.", "allRights": "Все права защищены.", "right": "Разработано %s"}}, "common": {"loading": "Загрузка...", "error": "Произошла ошибка", "next": "Далее", "previous": "Назад", "submit": "Отправить", "cancel": "Отмена", "close": "Закрыть", "loginNow": "Войти сейчас", "verifyAccount": "Подтвердить аккаунт"}, "languageSwitcher": {"language": "Язык"}, "contest": {"bielaDevelopment": "Хак<PERSON><PERSON>он Vibe Coding", "competition": "СОРЕВНОВАНИЕ", "firstPlace": "1 МЕСТО", "secondPlace": "2 МЕСТО", "thirdPlace": "3 МЕСТО", "currentLeader": "Текущий лидер:", "footerText": "Разработка на базе ИИ от Biela.dev при поддержке института TeachMeCode®", "browseHackathonSubmissions": "Просмотреть заявки на хакатон"}, "competition": {"header": {"mainTitle": "Ваш запрос. Ва<PERSON> стиль. Ваша игровая площадка.", "timelineTitle": "Расписание", "timelineDesc": "Один месяц, чтобы создать ваш лучший проект, сгенерированный ИИ", "eligibilityTitle": "Право участия", "eligibilityDesc": "Активные аккаунты Biela.dev, представляющие проект", "prizesTitle": "Призы", "prizesDesc": "16 000 долларов в виде денежных призов плюс бесплатный доступ для победителей", "votingTitle": "Голосование", "votingDesc": "Один голос на проверенного пользователя, поддерживаемое сообществом", "tagline": "Пусть ваше творчество говорит. Пусть мир голосует."}, "details": {"howToEnter": "Как участвовать", "enter": {"step1": {"title": "Создайте проект с использованием ИИ от Biela.dev", "desc": "Используйте мощные инструменты ИИ от Biela для создания вашего инновационного проекта"}, "step2": {"title": "Перейдите в вашу панель пользователя", "desc": "Нажмите \"Опубликовать для хакатона\" рядом с вашим проектом", "subdesc": "Вы можете отправить до 3 различных проектов"}, "step3": {"title": "Biela обработает вашу заявку:", "list1": "Сделайте скриншот вашего проекта", "list2": "Проверьте, соответствует ли ваш запрос критериям участия", "list3": "Создайте краткое описание и подробное описание"}}, "prizeStructure": "Структура призов", "prizes": {"firstPlace": "1 место", "firstPlaceValue": "$10,000", "secondPlace": "2 место", "secondPlaceValue": "$5,000", "thirdPlace": "3 место", "thirdPlaceValue": "$1,000", "fourthToTenth": "с 4 по 10 место", "fourthToTenthValue": "30 дней неограниченного доступа"}}, "qualification": {"heading": "Требования к квалификации", "description": "Чтобы претендовать на топ-3 призов ($10,000, $5,000 и $1,000), вы должны соответствовать следующим критериям:", "criteria1": "Иметь как минимум 3 активных реферала", "criteria2": "Поставить лайк хотя бы одному проекту на выставочной стене", "proTipLabel": "Совет эксперта:", "proTipDesc": "Чем раньше вы отправите свой проект, тем больше он получит просмотров и голосов!"}, "voting": {"heading": "Правила голосования", "oneVotePolicyTitle": "Правило одного голоса", "oneVotePolicyDesc": "Вы можете проголосовать только один раз и не за свой собственный проект", "accountVerificationTitle": "Верификация аккаунта", "accountVerificationDesc": "Голосовать могут только пользователи с подтвержденными аккаунтами Biela.dev", "tieBreakingTitle": "Разрешение равенства", "tieBreakingDesc": "В случае равенства решающим фактором станет общее количество звезд, заработанных создателем проекта (использованных или неиспользованных)", "howToVoteTitle": "Как голосовать", "howToVoteDesc": "Просмотрите выставочную стену, изучите все опубликованные проекты, сгенерированные ИИ, и нажмите на иконку сердца, чтобы отдать голос за ваш любимый проект!"}}, "contestItems": {"projectShowcase": "Витрина проекта", "loading": "Загрузка...", "createBy": "Создано ", "viewProject": "Просмотреть проект", "stars": "Звёзды", "overview": "обзор", "keyFeatures": "Ключевые особенности", "exploreLiveProject": "Изучить живой проект", "by": "от", "likeLimitReached": "Достигнут лимит лайков", "youCanLikeMaximumOneProject": "Вы можете поставить лайк только одному проекту. Хотите убрать лайк с текущего проекта и поставить на этот?", "currentlyLikedProject": "Текущий понравившийся проект", "switchMyLike": "Переключить мой лайк", "mustLoggedIntoLikeProject": "Вы должны войти в систему, чтобы поставить лайк проекту.", "signInToBielaAccountToVote": "Пожалуйста, войдите в свой аккаунт Biela.dev, чтобы участвовать в голосовании.", "yourAccountIsNotVerifiedYet": "Ваш аккаунт еще не подтвержден. Пожалуйста, подтвердите свой аккаунт, чтобы поставить лайк проекту.", "accountVerificationEnsureFairVoting": "Подтверждение аккаунта обеспечивает честное голосование и помогает поддерживать целостность конкурса.", "projectsSubmitted": "Отправленные проекты:", "unlikeThisProject": "Перестать лайкать этот проект", "likeThisProject": "Лайкнуть этот проект", "likes": "лайки", "like": "ла<PERSON>к", "somethingWentWrong": "Что-то пошло не так. Пожалуйста, попробуйте позже", "voteError": "Ошибка голосования"}, "textsLiveYoutube": {"first": {"title": "Вы приглашены!", "description": "В эту пятницу мы будем в прямом эфире — эта сессия посвящена созданию с <green>целью</green>, <blue>точностью</blue> и <orange>чистым вайбом</orange>.", "secondDescription": "Узнайте, как <green>освоить vibe coding</green>, <blue>открывать новые функции</blue> раньше всех и <orange>видеть, как реальные проекты</orange> строятся в прямом эфире.", "specialText": "Нажмите здесь и выберите «Уведомить меня» на YouTube — не пропустите."}, "second": {"title": "Будьте первыми в разработке.", "description": "В эту пятницу мы поднимаем <green>vibe coding</green> на совершенно новый уровень — покажем, как <blue>творить с намерением</blue>, <orange>разблокировать новые функции</orange> и запускаться <green>быстрее, чем когда-либо</green>.", "secondDescription": "<green>Новые сборки</green>, <blue>новые идеи</blue> и <orange>новые возможности</orange> для роста.", "specialText": "Нажмите здесь, чтобы установить напоминание на YouTube — станьте частью следующей волны создателей."}, "third": {"title": "Забронируйте ваше место.", "description": "Наш следующий <green>прямой эфир <PERSON>.dev</green> состоится в эту пятницу — и вы <blue>официально приглашены</blue>.", "secondDescription": "Мы погружаемся в <green>vibe coding</green>, раскрываем <blue>мощные новые функции</blue> и помогаем вам строить <orange>умнее, быстрее и масштабнее</orange>.", "specialText": "Нажмите здесь и выберите «Уведомить меня» на YouTube — ваша следующая большая идея может начаться здесь."}}, "stream": {"title": "Будь <1>Первым</1>, кто <3>Создаст</3>.", "subtitle": "Присоединяйтесь к нашему пятничному стриму — функции, проекты и эксклюзивные обновления.", "button": "Установите напоминание на YouTube."}, "billings": {"modalTitle": "Выберите план", "modalSubtitle": "Выберите один из трёх гибких планов, чтобы продолжать кодить без ограничений.", "toggleMonthly": "Ежемесячно", "toggleYearly": "Ежегодно (экономия 10%)", "mostPopular": "Самый популярный", "tokensLowerCase": "токены", "tokensUpperCase": "Токены", "below": "ниже", "unlimited": "НЕОГРАНИЧЕННО", "10Bonus": "Бонус 10%", "currentPlan": "Текущий план", "upgradePlan": "Обновить план", "purchaseDetails": "Детали покупки", "dateLabel": "Дата", "planLabel": "<PERSON><PERSON><PERSON><PERSON>", "amountLabel": "Сумма", "whatsNext": "Что дальше", "yourTokensAvailable": "Ваши токены теперь доступны в вашем аккаунте", "purchaseDetailsStored": "Данные о покупке сохранены в вашем аккаунте", "viewPurchaseHistory": "Вы можете просмотреть историю покупок в разделе оплаты", "thankYou": "Спасибо!", "purchaseSuccessful": "Покупка прошла успешно", "startCoding": "Начать кодить", "month": "мес<PERSON><PERSON>", "year": "год"}, "feature": {"basic_ai": "Базовая разработка ИИ", "community_support": "Поддержка сообщества", "standard_response": "Стандартное время ответа", "basic_templates": "Базовые шаблоны", "advanced_ai": "Продвинутая разработка ИИ", "priority_support": "Приоритетная поддержка", "fast_response": "Быстрое время ответа", "premium_templates": "Премиум-шаблоны", "team_collaboration": "Командное сотрудничество", "enterprise_ai": "Корпоративная разработка ИИ", "support_24_7": "Приоритетная поддержка 24/7", "instant_response": "Мгновенное время ответа", "custom_templates": "Пользовательские шаблоны", "advanced_team": "Расширенные функции команды", "custom_integrations": "Пользовательские интеграции", "big_context_window": "Большое окно контекста – в 5 раз больше среднего", "code_chat_ai": "Доступ к ИИ для кода и чата", "tokens_basic": "Достаточно токенов примерно для 3 презентационных сайтов", "standard_webcontainer": "Выделенный стандартный веб-контейнер", "medium_context_window": "Среднее окно контекста – подходит для сайта, блога или простой браузерной игры", "basic_support": "Базовая поддержка со стандартным временем ответа", "supabase_integration": "Интеграция Supabase", "project_sharing": "Делитесь проектами с друзьями", "project_download": "Скачивайте свои проекты", "project_deployment": "Функция развертывания", "custom_domain": "Подключайте проекты к своему собственному домену", "tokens_creator_plus": "Достаточно токенов примерно для 7 презентационных сайтов", "advanced_support": "Продвинутая поддержка с более быстрым временем ответа", "prioritary_support": "Выделенная приоритетная поддержка", "early_feature_access": "Ранний доступ к новым функциям", "human_cto": "Выделенный человеческий CTO для вашего бизнеса", "community_promotion": "Продвигайте свой бизнес с нашей поддержкой сообщества"}, "JoinUsLive": "Присоединяйтесь к нам в прямом эфире", "howToUseBiela": {"subtitle": "Как использовать Biela", "title": "Объяснено за минуты", "description": "Краткие объяснения, как работает каждая функция"}, "slides": {"newHistoryFeature": "Откройте для себя новую функцию ИСТОРИИ от Biela!", "newHistoryFeatureDescription": "Вкладка ИСТОРИЯ позволяет легко возвращаться и восстанавливать предыдущие версии вашего проекта, обеспечивая полный контроль и непрерывность в процессе Vibe Coding!", "fixPreview": "Как исправить предварительный просмотр в Biela!", "fixPreviewDescription": "Посмотрите полное руководство и убедитесь, что всё отображается именно так, как вы задумали.", "signUp": "Как зарегистрироваться в Biela!", "signUpDescription": "Впервые на biela.dev? Посмотрите это краткое руководство, чтобы узнать, как зарегистрироваться и подтвердить свою учётную запись!", "buildWebsite": "Как создать веб-сайт с помощью Biela!", "buildWebsiteDescription": "Узнайте, как создать и запустить сайт с нуля с помощью biela.dev — не написав ни одной строки кода.", "downloadAndImport": "Как загрузить и импортировать проекты и чаты в Biela!", "downloadAndImportDescription": "В этом пошаговом руководстве вы узнаете, как скачать завершённые проекты и импортировать сохранённые чаты в рабочее пространство — чтобы продолжить работу с того места, где остановились.", "shareProject": "Как поделиться своим проектом Vibe Code в BIELA!", "shareProjectDescription": "Готовы поделиться своим проектом как профессионал? Посмотрите полное руководство и начните прямо сейчас.", "launchProject": "Как запустить свой проект на собственном домене!", "launchProjectDescription": "В этом видео мы расскажем простые шаги, чтобы ваш сайт стал доступным онлайн — от настройки до финальной публикации.", "deployProject": "Как развернуть проект и выйти в онлайн!", "deployProjectDescription": "Это краткое руководство проведёт вас по всем этапам развёртывания сайта, приложения или дашборда — быстро и без проблем."}, "maintenance": {"title": "Мы работаем над решением проблемы", "description": "Biela.dev находится в режиме обслуживания. Мы работаем над решением проблемы", "text": "Спасибо за ваше терпение."}, "planCard": {"descriptionBeginner": "Идеально для новичков, создающих свои первые проекты и изучающих веб-разработку", "descriptionCreator": "Идеально для создателей, которым нужна большая мощность и гибкость для продвинутых проектов", "descriptionProfessional": "Полное решение для профессиональных разработчиков и команд, создающих приложения", "perMillionTokens": "$ {{price}} / за миллион токенов", "dedicatedCto": "Выделенный CTO – 24 часа", "tokens": "Токены", "perMonth": "в месяц", "perYear": "в год", "freeTokens": "{{ tokens }} Бесплатных токенов", "monthly": "Ежемесячно", "yearly": "Ежегодно", "save": "СЭКОНОМЬТЕ 10%"}, "faqLabel": "Часто задаваемые вопросы", "faqHeading": "Смотрите наши самые популярные вопросы", "faqSubheading": "Начните. Поймите. Исследуйте", "whatAreTokensQuestion": "Что такое токены?", "whatAreTokensAnswer1": "Токены — это способ, с помощью которого ИИ обрабатывает ваш проект. На Biela.dev токены используются при отправке запроса ИИ. При этом в сообщение включаются файлы проекта, чтобы ИИ мог понять ваш код. Чем больше проект и длиннее ответ ИИ, тем больше токенов требуется на сообщение.", "whatAreTokensAnswer2": "Также небольшое количество токенов списывается с вашего аккаунта за каждую минуту использования веб-контейнера, чтобы покрыть расходы на среду разработки.", "freePlanTokensQuestion": "Сколько токенов я получаю на бесплатном плане?", "freePlanTokensAnswer": "Каждому пользователю на бесплатном плане предоставляется 200 000 токенов в день, обновляемых ежедневно.", "outOfTokensQuestion": "Что произойдёт, если у меня закончатся токены?", "outOfTokensAnswer": "Не переживайте! Вы всегда можете перейти в Меню → Оплата и мгновенно приобрести дополнительные токены при необходимости.", "changePlanQuestion": "Могу ли я изменить план позже?", "changePlanAnswer": "Да — в любое время. Просто зайдите в Меню → Оплата, чтобы обновить, понизить или сменить тариф в соответствии с вашими нуждами.", "rolloverQuestion": "Переносятся ли неиспользованные токены?", "rolloverAnswer": "В настоящее время токены обнуляются по истечении срока действия и не переносятся — так что используйте их каждый день!", "exploreHowToUseBiela": "Изучите, как использовать Biela", "new": "Новое", "joinVibeCoding": "Присоединяйтесь к хакатону Vibe Coding", "whatDoYouWantToBuild": "ЧТО ВЫ ХОТИТЕ СОЗДАТЬ?", "imaginePromptCreate": "Вообразите. Направьте. Создайте.", "levelOfAmbition": "Каков ваш уровень амбиций?", "startGrowScale": "Начать. Расти. Масштабировать.", "calloutBar": {"start": "Все планы начинаются бесплатно с", "tokens": "200 000 токенов в день", "end": ". Без регистрации. Просто начните создавать."}, "atPriceOf": "по цене", "startFreeNoCreditCard": "Начните бесплатно – без кредитной карты", "SignIn": "Войти", "Register": "Зарегистрироваться", "Affiliates": "Партнеры", "UsernameRequired": "Username is required", "PasswordRequired": "Password is required", "MissingUsernamePasswordOrTurnstile": "Missing username, password or Turnstile token", "LoginSuccess": "Login successful!", "LoginFailed": "<PERSON><PERSON> failed", "EmailRequired": "Email is required", "EmailInvalid": "Please enter a valid email address", "CaptchaRequired": "Please complete the CAPTCHA", "ResetLinkSent": "A reset link has been sent to your email.", "ResetFailed": "Не удалось отправить ссылку для сброса", "BackToLogin": "Назад ко входу", "EmailPlaceholder": "Ваш email", "SendResetLink": "Отправить ссылку для сброса", "loading": "Загрузка", "checkingYourAuthenticity": "Проверяем вашу подлинность", "ToUseBielaDev": "Чтобы использовать Biela.dev, войдите в существующий аккаунт или создайте новый, используя один из вариантов ниже", "Sign in with Google": "Войти через Google", "Sign in with GitHub": "Войти через GitHub", "Sign in with Email and Password": "Войти с помощью Email и пароля", "DontHaveAnAccount": "Нет аккаунта?", "SignUp": "Зарегистрироваться", "ByUsingBielaDev": "Используя Biela.dev, вы соглашаетесь на сбор данных об использовании.", "EmailOrUsernamePlaceholder": "Email/Имя пользователя", "passwordPlaceholder": "Пароль", "login.loading": "Загрузка", "LoginInToProfile": "Войти в профиль", "login.back": "Назад", "forgotPassword": "Забыли пароль?", "PasswordsMismatch": "Пароли не совпадают.", "PhoneRequired": "Номер телефона обязателен", "ConfirmPasswordRequired": "Пожалуйста, подтвердите пароль", "AcceptTermsRequired": "Вы должны принять Условия использования и Политику конфиденциальности", "RegistrationFailed": "Регистрация не удалась", "EmailConfirmationSent": "Письмо с подтверждением отправлено. Пожалуйста, подтвердите ваш email и войдите.", "RegistrationServerError": "Регистрация не удалась (сервер вернул false).", "SomethingWentWrong": "Что-то пошло не так", "CheckEmailConfirmRegistration": "Проверьте вашу почту для подтверждения регистрации", "EmailConfirmationSentText": "Мы отправили вам письмо со ссылкой для подтверждения.", "LoginToProfile": "Войти в профиль", "username": "Имя пользователя", "email": "Email", "PasswordPlaceholder": "Пароль", "ConfirmPasswordPlaceholder": "Подтвердите пароль", "AcceptTermsPrefix": "Я соглашаюсь с", "TermsOfService": "Условиями использования", "AndSeparator": "и", "PrivacyPolicy": "Политикой конфиденциальности", "CreateAccount": "Создать аккаунт", "Back": "Назад", "SignInWithGoogle": "Войти через Google", "SignInWithGitHub": "Войти через GitHub", "SignInWithEmailAndPassword": "Войти с помощью Email и пароля", "translation": {"AIModel": "Модель ИИ", "UpgradePlan": "премиум-план", "PremiumBadge": "ПРЕМИУМ", "Active": "Активна", "projectInfo.features": "Функции", "Stats": "Статистика", "Performance": "Производительность", "Cost": "Стоимость", "UpgradeTooltip": "Откройте доступ, обновившись до ", "UpgradeTooltipSuffix": "пакета", "chat": "Чат", "code": "<PERSON>од"}, "extendedThinkingTooltip": "Позвольте ИИ подумать глубже перед ответом", "extendedThinkingTooltipDisabled": "Отключите углублённое мышление, чтобы сэкономить ресурсы", "AIModel": "Модель ИИ", "UpgradePlan": "премиум-план", "PremiumBadge": "ПРЕМИУМ", "Active": "Активна", "projectInfo.features": "Функции", "Stats": "Статистика", "Performance": "Производительность", "Cost": "Стоимость", "UpgradeTooltip": "Откройте доступ, обновившись до ", "UpgradeTooltipSuffix": "пакета", "HowBielaWorks": "Как работает BIELA", "WatchPromptLaunch": "Смотри. Пиши. Запускай.", "SearchVideos": "Поиск в библиотеке...", "NewReleased": "НОВЫЙ ВЫПУСК", "Playing": "Воспроизведение", "NoVideosFound": "Видео не найдено", "TryAdjustingYourSearchTerms": "Попробуйте изменить поисковые запросы", "feedback": {"title": {"issue": "Сообщить о проблеме", "feature": "Запросить функцию"}, "description": {"issue": "Обнаружили баг или столкнулись с проблемой? Сообщите нам, и мы постараемся исправить это как можно скорее.", "feature": "Есть идея для новой функции? Мы будем рады услышать ваши предложения по улучшению платформы."}, "success": {"title": "Спасибо!", "issue": "Ваш отчет об ошибке успешно отправлен.", "feature": "Ваш запрос на функцию успешно отправлен."}, "form": {"fullName": {"label": "Полное имя", "placeholder": "Введите ваше полное имя"}, "email": {"label": "Адрес электронной почты", "placeholder": "Введите ваш адрес электронной почты"}, "suggestion": {"labelIssue": "Описание проблемы", "labelFeature": "Предложение функции", "placeholderIssue": "Опишите проблему, с которой вы столкнулись...", "placeholderFeature": "Опишите функцию, которую хотели бы видеть..."}, "screenshots": {"label": "Скриншоты (необязательно)", "note": "Добавьте скриншоты, чтобы мы могли лучше понять проблему", "drop": "Нажмите, чтобы загрузить, или перетащите файлы сюда", "hint": "PNG, JPG, JPEG, до 10MB каждый", "count": "Выбрано изображений: {{count}}"}}, "buttons": {"cancel": "Отмена", "submitting": "Отправка...", "submitIssue": "Отправить проблему", "submitFeature": "Отправить запрос"}, "errors": {"fullNameRequired": "Полное имя обязательно", "fullNameNoNumbers": "Полное имя не может содержать цифры", "emailRequired": "Адрес электронной почты обязателен", "emailInvalid": "Пожалуйста, введите действительный адрес электронной почты", "suggestionRequired": "Пожалуйста, опишите вашу проблему или предложение"}}, "changelog": {"title": "История изменений", "description": "Всегда. Эволюционируем. Вперёд.", "TotalReleases": "Всего релизов", "ActiveUsers": "Активные пользователи", "FeaturesAdded": "Добавлено функций", "BugsFixed": "Исправлено ошибок", "shortCallText": "Есть предложения или нашли ошибку? Нам важно ваше мнение!", "reportIssue": "Сообщить о проблеме", "requestFeature": "Запросить функцию", "totalReleases": "Всего релизов", "activeUsers": "Активных пользователей", "featuresAdded": "Добавлено функций", "bugsFixed": "Исправлено ошибок", "types": {"feature": "Функция", "improvement": "Улучшение", "bugfix": "Исправление ошибки", "ui/ux": "UI/UX", "announcement": "Объявление", "general": "Общее"}, "versions": {"v3.2.3": {"date": "26 июня 2025", "changes": {"0": "Исправлены некоторые проблемы UI/UX в панели пользователя.", "1": "Улучшена стабильность WebContainer.", "2": "Улучшен режим AI Diff для обработки маленьких файлов по-другому, чем больших файлов."}}, "v3.2.2": {"date": "24 июня 2025", "changes": {"0": "Реорганизован и переработан фронтофис для улучшения пользовательского опыта.", "1": "Реализована эта потрясающая страница журнала изменений.", "2": "Улучшен алгоритм переподключения WebContainer.", "3": "Добавлена поддержка загрузки проектов через ZIP-файлы, в дополнение к загрузке папок.", "4": "Улучшена производительность всех API-вызовов за счёт лучшей индексации."}}, "v3.2.1": {"date": "24 июня 2025", "changes": {"0": "IDE: Разд<PERSON><PERSON> \"Папки\" панели пользователя был переработан для улучшенного опыта.", "1": "Content Studio: Теперь вы можете копировать изображения в Content Studio."}}, "v3.2.0": {"date": "17 июня 2025", "changes": {"0": "Режим AI Diff (Экспериментальный): ИИ теперь записывает только различия между оригинальным файлом и обновлённой версией, вместо полной регенерации файла. Это значительно улучшает производительность и оптимизирует использование токенов. Вы можете включить или отключить эту экспериментальную функцию в области Настройки → Центр управления вашего проекта."}}, "v3.1.6": {"date": "12 июня 2025", "changes": {"0": "IDE: Добавлено поле кода купона для подписок и дополнительных токенов."}}, "v3.1.5": {"date": "11 июня 2025", "changes": {"0": "IDE: Применены различные исправления к потоку \"Поделиться проектом\"."}}, "v3.1.4": {"date": "10 июня 2025", "changes": {"0": "IDE: Подписки на пакеты были перепроверены и исправлены, устранены проблемы с включением криптовалют и реализацией Stripe."}}, "v3.1.3": {"date": "9 июня 2025", "changes": {"0": "ИИ: Реализована и протестирована прямая интеграция API для Gemini для достижения лучшей задержки."}}, "v3.1.2": {"date": "6 июня 2025", "changes": {"0": "Партнёры: Реализованы задачи выплат партнёрам с согласованностью дизайна с информационным модалом новой панели (история выплат).", "1": "Content Studio: Добавлена новая опция для пользователей, чтобы решать, отправлять ли изображения в ИИ вместе со ссылками. По умолчанию это установлено как \"нет\"."}}, "v3.1.1": {"date": "5 июня 2025", "changes": {"0": "Content Studio: Внесены обновления для организации Content Studio по проектам."}}, "v3.1.0": {"date": "3 июня 2025", "changes": {"0": "🚀 Крупное обновление BIELA.dev только что вышло! 🚀", "1": "ИИ теперь пишет код в 5 раз быстрее, чем сегодня утром. Да, вы правильно прочитали. Мы форсировали производительность BIELA, чтобы вы могли переходить от идеи к работающему коду мгновенно.", "2": "Терминал: Теперь ещё лучше: Основываясь на утренней переработке, мы добавили дополнительную полировку UI/UX для более плавного потока кодирования.", "3": "Улучшения подключения Supabase: Более чистый, интуитивный интерфейс делает настройку и интеграцию лёгкой.", "4": "Обновление обработки ошибок (Автообработка ошибок): Автоматическая отчётность об ошибках была отозвана. Мы готовим новую настройку, чтобы вы могли выбирать между ручной и ИИ-ассистированной обработкой.", "5": "Новый центр управления (в настройках проекта) и интерфейс модульного тестирования: Первая функция: переключение терминала модульного тестирования. Много других элементов управления на подходе!", "6": "Улучшенная логика загрузки: Загрузки теперь ждут, пока ваш код полностью отправлен, обеспечивая полные и точные экспорты каждый раз."}}, "v3.0.0": {"date": "3 июня 2025", "changes": {"0": "🚀 Обновление BIELA.dev - Только что получило мощный буст! 🚀", "1": "Обновлённый UI терминала: Более элегантный, чистый дизайн, который делает работу в терминале удовольствием.", "2": "NPM-действия одним кликом: За<PERSON>ускайте npm install, npm run build, или npm run dev мгновенно с новыми кнопками действий - не нужно печатать! (функция интегрирована в терминал)", "3": "Более умная обработка ошибок и цветовая классификация ошибок: Ошибки предпросмотра теперь автоматически отправляются в ИИ, чтобы он мог помочь вам отладить быстрее. (вы испытаете плавное исправление багов от BIELA при разработке ваших проектов)", "4": "Улучшенная навигация по файлам: Клик по имени файла в чате теперь открывает его прямо в WebContainer. Просто нажмите и редактируйте!"}}, "v2.1.1": {"date": "30 мая 2025", "changes": {"0": "IDE: Доба<PERSON><PERSON>ена кнопка сохранения для WebContainer, появляющаяся при ручных обновлениях, запускающая коммит и пуш в GitHub.", "1": "Панель: Исправлена ошибка, возникающая при переименовании импортированных проектов с панели пользователя.", "2": "WebContainer: Реализовано срочное исправление утечки памяти."}}, "v2.1.0": {"date": "28 мая 2025", "changes": {"0": "🚀 Обновление BIELA.dev — 7 новых улучшений только что пришло! 🚀", "1": "IDE: Мы добавили возможность связать ваш собственный домен с вашим проектом biela прямо из интерфейса.", "2": "Предпросмотры развёрнутых проектов: Теперь вы можете предварительно просматривать ваши развёрнутые проекты одним кликом по иконке глаза - не нужно открывать каждый, чтобы знать, что внутри.", "3": "Исправлено отслеживание затрат в дублированных проектах: Мы исправили проблему, где дублированные проекты наследовали оценки затрат от оригинального проекта. Теперь они начинают с нуля, давая вам точное отслеживание по проекту.", "4": "Живая оценка затрат (за промпт): Оценки затрат теперь обновляются после каждого промпта, а не только раз в день. Это даёт вам обратную связь в реальном времени о том, сколько стоило бы разработать то же самое с агентством разработки и сколько вы экономите с BIELA.", "5": "Вкладка истории и откат исправлены: Теперь вы можете безопасно откатываться в истории проекта снова. После наших недавних крупных обновлений это имело проблемы - теперь всё гладко как масло.", "6": "Улучшения автопрокрутки: Больше никаких замороженных экранов во время кодирования! Мы исправили проблему, где прокрутка отставала во время генерации. Наслаждайтесь плавными потоками.", "7": "Страницы меню теперь открываются в новых вкладках: Если вы кодите в Labs и кликаете на другие элементы меню, они теперь открываются в новой вкладке, чтобы вы никогда не потеряли вашу текущую работу в процессе."}}, "v2.0.4": {"date": "27 мая 2025", "changes": {"0": "Панель: Улучшения скриншотов в панели пользователя включают удаление автопрокрутки, разрешение управляемой пользователем прокрутки и добавление анимации для загрузки скриншотов."}}, "v2.0.3": {"date": "20 мая 2025", "changes": {"0": "Content Studio: Анимация робота в Content Studio теперь отображается в полном размере.", "1": "Партнёры: Исправлены проблемы отображения в панели партнёров при просмотре в Firefox."}}, "v2.0.2": {"date": "19 мая 2025", "changes": {"0": "Content Studio: Максимальное количество файлов, разрешённых одновременно в Content Studio, увеличено до 50 (с 10)."}}, "v2.0.1": {"date": "18 мая 2025", "changes": {"0": "Content Studio: Исправлена проблема, где теги для нескольких файлов не обновлялись, если страница не была обновлена.", "1": "Content Studio: Кнопка редактирования при выборе изображения теперь более заметна.", "2": "Content Studio: При загрузке, местоположение папки теперь автоматически выбирает последнюю созданную или последнюю выбранную папку.", "3": "Content Studio: Выбранная папка теперь сохраняется при перетаскивании изображений.", "4": "Content Studio: Заголовок вкладки теперь использует новый цвет (вместо зелёного).", "5": "Content Studio: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"вставить изображение\" переименована в \"использовать в проекте\".", "6": "Content Studio: Кнопка загрузки файлов теперь имеет зелёный фон.", "7": "Content Studio: Высота строки поиска уменьшена для лучшего выравнивания.", "8": "Content Studio: Строка поиска больше не показывается, если у пользователя нет изображений."}}, "v2.0.0": {"date": "16 мая 2025", "changes": {"0": "🚀 КРУПНЫЕ ОБНОВЛЕНИЯ В BIELA.DEV! 🚀", "1": "Наша собственная технология веб-контейнеров: Мы построили нашу собственную технологию веб-контейнеров, которая полностью независима от любых внешних поставщиков! Это даёт нам беспрецедентную гибкость для разработки новых функций быстрее и лучше, чем когда-либо.", "2": "Множественные варианты LLM для генерации кода: Теперь мы поддерживаем несколько моделей ИИ для генерации кода помимо Anthropic! Теперь вы можете использовать Gemini от Google для ваших проектов кодирования, что приносит некоторые важные преимущества. Gemini предлагает массивное окно контекста в 1,000,000 токенов!", "3": "Content Studio: Ва<PERSON>и фото, ваши проекты: Выражайте себя полностью с нашим новым Content Studio! Теперь вы можете загружать свои собственные фотографии для использования в ваших проектах Vibe Coding.", "4": "Совместное использование проектов: Совместная работа только что стала лучше! Теперь вы можете делиться своими проектами с другими пользователями.", "5": "Подключение к существующим проектам Supabase: Эта мощная новая функция позволяет подключать несколько проектов Biela.dev к одной базе данных Supabase."}}, "v1.0.5": {"date": "15 мая 2025", "changes": {"0": "Платежи: Реа<PERSON><PERSON>зована обработка платежей Stripe.", "1": "Content Studio: Ани<PERSON>а<PERSON>ия (как робот) теперь отображается, когда нет изображения на первой странице."}}, "v1.0.4": {"date": "13 мая 2025", "changes": {"0": "Content Studio: Исправлены проблемы пагинации в Content Studio."}}, "v1.0.3": {"date": "5 мая 2025", "changes": {"0": "Запущен Content Studio - место для хранения и управления всеми вашими медиа.", "1": "Фронтофис: Реализован schema.org для программных приложений на biela_frontoffice.", "2": "Панель команды: Добавлены уведомления в панель команды (например, красная точка, когда новый участник присоединяется или появляется новое сообщение в чате).", "3": "IDE: Исправлен баг, где модал из меню не закрывался должным образом при клике внутри WebContainer в определённых областях при подаче промпта."}}, "v1.0.2": {"date": "2 мая 2025", "changes": {"0": "Партнёры: Реализован редизайн области участников команды.", "1": "Партнёры: Обновлены иконки панели партнёров.", "2": "IDE: Текст кнопки \"Подключить Supabase\" изменён на \"База данных\" с новой иконкой, и теперь показывает \"База данных подключена\" синим цветом при подключении."}}, "v1.0.1": {"date": "1 мая 2025", "changes": {"0": "IDE (Вкладка настроек): Реализована интеграция базы данных в чате настроек.", "1": "Панель: Исправлена пагинация в панели пользователя."}}, "v1.0.0": {"date": "18 апреля 2025", "changes": {"0": "🚀 Добро пожаловать в Biela.dev: Ваш компаньон для кодирования на ИИ, бесплатно для всех! 🚀", "1": "Мы взволнованы объявить первый публичный релиз Biela.dev! Мы верим в то, чтобы сделать передовую веб-разработку с помощью ИИ доступной для всех, поэтому наша платформа полностью бесплатна с первого дня."}}}}}