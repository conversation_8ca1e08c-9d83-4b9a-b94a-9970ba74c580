{"meta": {"home": {"title": "biela.dev | AI 기반 웹 & 앱 빌더 – 프롬프트로 구축하세요", "description": "biela.dev를 통해 당신의 아이디어를 실시간 웹사이트 또는 앱으로 변환하세요. AI 기반 프롬프트를 사용하여 맞춤형 디지털 제품을 손쉽게 만드세요."}, "privacy": {"title": "biela.dev 개인정보 처리방침", "description": "biela.dev가 귀하의 개인 정보를 수집, 사용 및 보호하는 방법을 확인하세요."}, "terms": {"title": "biela.dev 서비스 이용약관", "description": "biela.dev의 AI 기반 개발 플랫폼 사용을 위한 이용약관을 확인하세요."}, "changelog": {"title": "biela.dev 변경 사항 기록 - 항상. 진화. 앞으로.", "description": "biela.dev가 어떻게 각 버전마다 개선되는지 알아보세요."}, "competitionterms": {"title": "Biela 개발 대회 이용 약관", "description": "Biela.dev와 TeachMeCode Institute가 주최하는 공식 한 달간의 챌린지에 참여하세요. 디지털 프로젝트를 선보이고 커뮤니티 참여를 통해 인정을 받으며 상위 순위를 위해 경쟁하세요. 제출 마감일: 2025년 5월 14일."}, "contest": {"title": "biela.dev 개발 대회 2025 – 우승자 발표", "description": "biela.dev 대회의 우승자를 확인하고 최고의 영예를 얻은 놀라운 AI 제작 프로젝트들을 발견하세요"}, "howTo": {"title": "Biela.dev 사용법 – AI 기반 앱을 만드는 완전한 가이드", "description": "Biela.dev를 사용하여 빠르고 코드 없이 웹 앱을 만드는 방법을 알아보세요. 튜토리얼과 실용적인 팁이 포함된 단계별 완벽 가이드입니다.", "keywords": "biela, biela.dev, 사용법, biela 사용법, AI 앱 만들기, 노코드, 빠른 개발, biela 가이드, biela 튜토리얼", "ogTitle": "Biela.dev 사용법 – 단계별 가이드", "ogDescription": "Biela.dev로 AI 앱을 만드는 방법을 알아보세요. 아이디어에서 출시까지, 코딩 없이 가능합니다.", "twitterTitle": "Biela.dev 사용법 – 완벽 가이드", "twitterDescription": "코딩 없이 Biela.dev에서 AI 앱을 만들어보세요. 이 쉬운 단계별 가이드를 따라오세요!"}}, "navbar": {"home": "홈", "demo": "데모", "community": "커뮤니티", "affiliate": "제휴", "partners": "파트너", "contest": "바이브 코딩 해커톤", "subscriptions": "구독", "pricing": "가격"}, "counter": {"days": "일", "hours": "시간", "minutes": "분", "seconds": "초", "centisec": "센티초"}, "hero": {"title": "당신의 아이디어를 몇 분 만에 실시간 웹사이트나 앱으로 전환하세요", "subtitle": "만약 당신이 <1>상상</1>할 수 있다면, <5>코드화</5>할 수 있습니다.", "subtitle2": "무료로 시작하세요. 모든 것을 코딩하세요. 모든 요청마다 당신의 능력을 기회로 바꾸세요.", "timeRunningOut": "시간이 얼마 남지 않았습니다!", "launchDate": "버전 WoodSnake", "experimentalVersion": "베타 출시: 2025년 4월 15일", "earlyAdopter": "정식 출시 전에 얼리어답터 혜택을 확보하려면 지금 가입하세요", "tryFree": "무료 체험", "seeAiAction": "실시간 AI 시연 보기", "tellBiela": "Biela에게 생성 요청하기", "inputPlaceholder": "상상할 수 있다면, BIELA가 코딩할 수 있어요. 오늘은 무엇을 해볼까요?", "chat": "채팅", "code": "코드", "checklist": "체크리스트", "checklistTitle": "체크리스트를 사용하여", "checklistItems": {"trackDevelopment": "개발 작업 추적", "setRequirements": "프로젝트 요구 사항 설정", "createTesting": "테스트 프로토콜 생성"}, "registerNow": "지금 등록", "attachFiles": "프롬프트에 파일 첨부", "voiceInput": "음성 입력 사용", "enhancePrompt": "프롬프트 개선", "cleanupProject": "프로젝트 정리", "suggestions": {"weatherDashboard": "날씨 대시보드 만들기", "ecommercePlatform": "전자상거래 플랫폼 구축", "socialMediaApp": "소셜 미디어 앱 디자인", "portfolioWebsite": "포트폴리오 웹사이트 생성", "taskManagementApp": "작업 관리 앱 만들기", "fitnessTracker": "피트니스 트래커 구축", "recipeSharingPlatform": "레시피 공유 플랫폼 디자인", "travelBookingSite": "여행 예약 사이트 만들기", "learningPlatform": "학습 플랫폼 구축", "musicStreamingApp": "음악 스트리밍 앱 디자인", "realEstateListing": "부동산 매물 만들기", "jobBoard": "구직 게시판 구축"}}, "videoGallery": {"barber": {"title": "이발소 웹사이트", "description": "Biela.dev가 몇 분 만에 완벽한 이발소 웹사이트를 만드는 과정을 확인하세요"}, "tictactoe": {"title": "틱택토 게임", "description": "AI가 '틱택토 게임'보다 더 빠르게 틱택토를 만드는 모습을 보세요"}, "coffeeShop": {"title": "커피숍 웹사이트", "description": "Biela.dev가 커피숍 웹사이트를 만드는 모습을 확인하세요"}, "electronicsStore": {"title": "전자제품 스토어 웹사이트", "description": "Biela.dev가 몇 분 만에 완벽한 온라인 스토어를 만드는 과정을 보세요"}, "seoAgency": {"title": "SEO 에이전시 웹사이트", "description": "AI가 SEO 에이전시 웹사이트를 만드는 모습을 확인하세요"}}, "telegram": {"title": "우리의 텔레그램에 가입하세요", "subtitle": "Biela 커뮤니티와 연결하세요", "description": "즉각적인 지원을 받고, 프로젝트를 공유하며, 전 세계의 AI 애호가들과 연결하세요. <br/><br/>우리의 텔레그램 커뮤니티는 Biela.dev의 최신 소식을 가장 빠르게 접할 수 있는 방법입니다.", "stats": {"members": "멤버", "support": "지원", "countries": "국가", "projects": "프로젝트"}, "joinButton": "우리의 텔레그램 커뮤니티에 가입하기", "communityTitle": "Biela.dev 커뮤니티", "membersCount": "명", "onlineCount": "현재 온라인", "messages": {"1": "Biela.dev를 사용해서 1시간도 안 되어 내 전자상거래 사이트를 런칭했어!", "2": "멋지네요! 제품 카탈로그는 어떻게 관리했나요?", "3": "Biela가 자동으로 처리했어요! 저는 단지 원하는 것을 설명하기만 했습니다.", "4": "지금 포트폴리오 사이트를 만들고 있는데, AI의 제안이 정말 놀라워요!", "5": "새로운 반응형 템플릿을 사용해 본 사람이 있나요? 모바일에서도 정말 멋져 보여요."}}, "joinNetwork": {"title": "우리 네트워크에 가입하세요", "subtitle": "연결하세요. 성장하세요. 수익을 올리세요.", "affiliateProgram": "제휴 프로그램", "registerBring": "등록 후 3명 제휴사 초대", "aiCourse": "AI 엔지니어링 코스", "courseDescription": "TeachMeCode와 함께하는 무료 종합 AI 코스", "digitalBook": "디지털 도서", "bookDescription": "\"The Art of Prompt Engineering\" 독점 디지털 에디션", "exclusiveBenefits": "독점 혜택", "benefitsDescription": "평생 수익 및 특별 멤버 권한", "registerNow": "지금 등록", "teamEarnings": "팀 수익", "buildNetwork": "네트워크 구축", "weeklyMentorship": "주간 멘토링", "teamBonuses": "팀 보너스", "startEarning": "오늘부터 수익 시작", "upToCommission": "최대 커미션"}, "partners": {"title": "업계 리더들이 신뢰하는", "subtitle": "글로벌 혁신가들과 함께 AI 개발의 미래를 만들어 갑니다", "strategicPartnerships": "전략적 파트너십", "joinNetwork": "BIELA와 함께 개발을 혁신하는 파트너 및 기업 네트워크에 참여하세요"}, "demo": {"title": "실시간으로 AI가 앱과 웹사이트를 구축하는 모습을 보세요", "subtitle": "Biela.dev로 기업들이 어떻게 디지털 존재감을 혁신하고 있는지 확인해 보세요", "seeInAction": "Biela.dev의 작동 모습을 확인", "whatKind": "어떤 유형의 웹사이트가 필요하신가요?", "describeWebsite": "웹사이트 아이디어를 설명하세요 (예: '사진작가 포트폴리오')", "generatePreview": "미리보기 생성", "nextDemo": "다음 데모", "startBuilding": "오늘부터 Biela.dev와 함께 구축하고 수익을 올리세요"}, "footer": {"quickLinks": {"title": "빠른 링크", "register": "등록", "bielaInAction": "Biela.dev 작동 보기", "liveOnTelegram": "텔레그램에서 실시간 중", "affiliate": "협업형 제휴 마케팅", "partners": "파트너"}, "legal": {"title": "법적 정보", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. 판권 소유.", "unauthorized": "명시적인 서면 허가 없이 이 서비스의 전체 또는 일부를 무단 사용, 복제 또는 배포하는 것은 엄격히 금지됩니다."}, "reservations": "TeachMeCode Institute는 Biela와 관련된 지적 재산에 대한 모든 법적 권리를 보유합니다."}, "bottomBar": {"left": "2025 Biela.dev. %s 제공 © 모든 권리 보유.", "allRights": "모든 권리 보유.", "right": "개발: %s"}}, "common": {"loading": "로딩 중...", "error": "오류가 발생했습니다", "next": "다음", "previous": "이전", "submit": "제출", "cancel": "취소", "close": "닫기", "loginNow": "지금 로그인", "verifyAccount": "계정 확인"}, "languageSwitcher": {"language": "언어"}, "contest": {"bielaDevelopment": "바이브 코딩 해커톤", "competition": "대회", "firstPlace": "1위", "secondPlace": "2위", "thirdPlace": "3위", "currentLeader": "현재 리더:", "footerText": "Biela.dev의 AI 기반 개발, TeachMeCode® 연구소 지원", "browseHackathonSubmissions": "해커톤 제출물 보기", "winnersAnnouncement": "🎉 콘테스트 우승자 발표! 🎉", "congratulationsMessage": "놀라운 우승자들을 축하합니다! 이 멋진 해커톤에 참여해 주신 모든 분들께 감사드립니다.", "conditionActiveReferrals": "3개의 활성 추천이 있음", "conditionLikedProject": "다른 프로젝트에 좋아요를 눌렀음", "winner": "우승자", "qualificationsMet": "자격:", "noQualifiedParticipant": "자격을 갖춘 참가자 없음", "requirementsNotMet": "요구사항이 충족되지 않음", "noQualificationDescription": "이 순위에 대한 자격 요구사항을 충족한 참가자가 없습니다.", "qualifiedWinners": "🏆 자격을 갖춘 우승자", "qualifiedWinnersMessage": "모든 자격 요구사항을 충족한 참가자들을 축하합니다!", "noTopPrizesQualified": "상위 상금에 자격을 갖춘 참가자 없음", "noTopPrizesMessage": "안타깝게도 처음 세 개의 상금 순위에 대한 자격 요구사항을 충족한 참가자가 없습니다. 자격을 갖춘 모든 참가자는 남은 상금 풀에서 상을 받게 됩니다.", "noTopPrizesMessageShort": "안타깝게도 처음 세 개의 상금 순위에 대한 자격 요구사항을 충족한 참가자가 없습니다."}, "competition": {"header": {"mainTitle": "당신의 프롬프트. 당신의 스타일. 당신의 놀이터.", "timelineTitle": "타임라인", "timelineDesc": "최고의 AI 생성 프로젝트를 만들기 위해 한 달의 시간이 주어집니다.", "eligibilityTitle": "참가 자격", "eligibilityDesc": "프로젝트를 제출하는 활성 Biela.dev 계정", "prizesTitle": "상금", "prizesDesc": "16,000달러의 현금 상금과 수상자에게 무료 접근 권한이 제공됩니다.", "votingTitle": "투표", "votingDesc": "커뮤니티 주도로 진행되며, 인증된 사용자에게만 한 번의 투표가 허용됩니다.", "tagline": "당신의 창의력을 발휘하세요. 전 세계가 투표하게 하세요."}, "details": {"howToEnter": "참가 방법", "enter": {"step1": {"title": "Biela.dev AI를 사용하여 프로젝트를 만드세요", "desc": "Biela의 강력한 AI 도구를 사용하여 혁신적인 프로젝트를 구축하세요."}, "step2": {"title": "사용자 대시보드로 이동하세요", "desc": "프로젝트 옆에 있는 \"해커톤에 게시\"를 클릭하세요", "subdesc": "최대 3개의 다른 프로젝트를 제출할 수 있습니다."}, "step3": {"title": "Biela가 참가 작품을 처리합니다:", "list1": "프로젝트의 스크린샷을 찍으세요", "list2": "참가 자격을 확인하세요", "list3": "요약 및 설명을 생성하세요"}}, "prizeStructure": "상금 구조", "prizes": {"firstPlace": "1위", "firstPlaceValue": "$10,000", "secondPlace": "2위", "secondPlaceValue": "$5,000", "thirdPlace": "3위", "thirdPlaceValue": "$1,000", "fourthToTenth": "4위~10위", "fourthToTenthValue": "30일 무제한 이용"}}, "qualification": {"heading": "참가 자격 요건", "description": "상위 3위 상금 ($10,000, $5,000, $1,000)을 받기 위해서는 다음 기준을 충족해야 합니다:", "criteria1": "최소 3개의 활성 추천이 있어야 합니다", "criteria2": "전시회 벽에서 하나 이상의 프로젝트에 '좋아요'를 눌러야 합니다", "proTipLabel": "전문가 팁:", "proTipDesc": "프로젝트를 빨리 제출할수록 더 많은 노출과 투표를 받을 수 있습니다!"}, "voting": {"heading": "투표 가이드라인", "oneVotePolicyTitle": "한 사람, 한 표 정책", "oneVotePolicyDesc": "자신의 프로젝트에는 투표할 수 없으며, 한 번만 투표가 가능합니다.", "accountVerificationTitle": "계정 인증", "accountVerificationDesc": "인증된 Biela.dev 계정 소유자만 투표할 수 있습니다.", "tieBreakingTitle": "동점자 처리", "tieBreakingDesc": "동점의 경우, 프로젝트 제작자가 획득한 별의 총 수(사용 여부에 관계없이)가 결정적 요소가 됩니다.", "howToVoteTitle": "투표 방법", "howToVoteDesc": "전시 벽을 둘러보고, 게시된 모든 AI 생성 프로젝트를 살펴본 후 마음에 드는 프로젝트에 하트 아이콘을 클릭하여 투표하세요."}}, "contestItems": {"projectShowcase": "프로젝트 쇼케이스", "loading": "로딩 중...", "createBy": "만든 사람: ", "viewProject": "프로젝트 보기", "stars": "별", "overview": "개요", "keyFeatures": "주요 특징", "exploreLiveProject": "라이브 프로젝트 둘러보기", "by": "에 의해", "likeLimitReached": "좋아요 한도에 도달했습니다", "youCanLikeMaximumOneProject": "최대 하나의 프로젝트에 좋아요를 누를 수 있습니다. 현재 좋아요를 취소하고 대신 이 프로젝트에 좋아요를 누르시겠습니까?", "currentlyLikedProject": "현재 좋아요를 누른 프로젝트", "switchMyLike": "좋아요 변경", "mustLoggedIntoLikeProject": "프로젝트에 좋아요를 누르려면 로그인해야 합니다.", "signInToBielaAccountToVote": "투표에 참여하려면 Biela.dev 계정에 로그인하세요.", "yourAccountIsNotVerifiedYet": "계정이 아직 인증되지 않았습니다. 프로젝트에 좋아요를 누르려면 계정을 인증하세요.", "accountVerificationEnsureFairVoting": "계정 인증은 공정한 투표를 보장하고 대회의 무결성을 유지하는 데 도움이 됩니다.", "projectsSubmitted": "제출된 프로젝트:", "unlikeThisProject": "이 프로젝트 좋아요 취소", "likeThisProject": "이 프로젝트 좋아요", "likes": "좋아요", "like": "좋아요", "somethingWentWrong": "문제가 발생했습니다. 나중에 다시 시도해주세요", "voteError": "투표 오류"}, "textsLiveYoutube": {"first": {"title": "초대되었습니다!", "description": "이번 금요일 라이브로 진행합니다 — 이 세션은 <green>목적</green>, <blue>정밀도</blue>, 그리고 <orange>순수한 바이브</orange>로 구축하는 모든 것에 관한 것입니다.", "secondDescription": "어떻게 <green>바이브 코딩을 마스터</green>하고, <blue>새로운 기능을 발견</blue>하며, <orange>실제 프로젝트가</orange> 라이브로 구축되는 모습을 볼 수 있는지 알아보세요.", "specialText": "여기를 클릭하고 YouTube에서 \"알림 받기\"를 눌러보세요 — 놓치지 마세요."}, "second": {"title": "먼저 빌드하세요.", "description": "이번 금요일, 우리는 <green>바이브 코딩</green>을 완전히 새로운 차원으로 끌어올립니다 — <blue>의도를 가지고 생성</blue>하고, <orange>새로운 기능을 잠금 해제</orange>하며, <green>이전보다 더 빠르게</green> 출시하는 방법을 보여드립니다.", "secondDescription": "<green>새로운 빌드</green>, <blue>새로운 아이디어</blue>, 그리고 <orange>새로운 기회</orange>로 성장하세요.", "specialText": "여기를 클릭하여 YouTube 알림을 설정하세요 — 다음 창작 물결에 함께하세요."}, "third": {"title": "자리를 확보하세요.", "description": "다음 <green>Biela.dev 라이브스트림</green>은 이번 금요일에 열립니다 — 당신은 <blue>공식적으로 초대되었습니다</blue>.", "secondDescription": "우리는 <green>바이브 코딩</green>에 깊이 뛰어들고, <blue>강력한 새로운 기능</blue>을 공개하며, <orange>더 스마트하고, 더 빠르고, 더 크게</orange> 구축하도록 도와드립니다.", "specialText": "여기를 클릭하고 YouTube에서 \"알림 받기\"를 탭하세요 — 당신의 다음 큰 아이디어가 여기서 시작될 수 있습니다."}}, "stream": {"title": "<1>처음</1>으로 <3>구축</3>하세요.", "subtitle": "금요일 라이브 스트림에 참여하세요 — 기능, 프로젝트 및 독점 업데이트.", "button": "YouTube 알림을 설정하세요."}, "billings": {"modalTitle": "플랜 선택", "modalSubtitle": "제한 없이 코딩을 계속하려면 세 가지 유연한 플랜 중 하나를 선택하세요.", "toggleMonthly": "월간", "toggleYearly": "연간 (10% 절약)", "mostPopular": "가장 인기 있음", "tokensLowerCase": "토큰", "tokensUpperCase": "토큰", "below": "아래", "unlimited": "무제한", "10Bonus": "10% 보너스", "currentPlan": "현재 플랜", "upgradePlan": "플랜 업그레이드", "purchaseDetails": "구매 내역", "dateLabel": "날짜", "planLabel": "플랜", "amountLabel": "금액", "whatsNext": "다음 단계", "yourTokensAvailable": "토큰이 계정에 추가되었습니다", "purchaseDetailsStored": "구매 내역이 계정에 저장되었습니다", "viewPurchaseHistory": "결제 섹션에서 구매 내역을 확인할 수 있습니다", "thankYou": "감사합니다!", "purchaseSuccessful": "구매가 완료되었습니다", "startCoding": "코딩 시작하기", "month": "월", "year": "년"}, "feature": {"basic_ai": "기본 AI 개발", "community_support": "커뮤니티 지원", "standard_response": "표준 응답 시간", "basic_templates": "기본 템플릿", "advanced_ai": "고급 AI 개발", "priority_support": "우선 지원", "fast_response": "빠른 응답 시간", "premium_templates": "프리미엄 템플릿", "team_collaboration": "팀 협업", "enterprise_ai": "기업용 AI 개발", "support_24_7": "24시간 우선 지원", "instant_response": "즉각 응답 시간", "custom_templates": "맞춤 템플릿", "advanced_team": "고급 팀 기능", "custom_integrations": "맞춤 통합", "big_context_window": "큰 컨텍스트 창 - 중간 크기보다 5배 큼", "code_chat_ai": "코드 및 채팅 AI 접근", "tokens_basic": "약 3개의 프레젠테이션 웹사이트용 토큰", "standard_webcontainer": "전용 표준 웹컨테이너", "medium_context_window": "중간 컨텍스트 창 - 웹사이트, 블로그 또는 기본 브라우저 게임에 적합", "basic_support": "표준 응답 시간의 기본 지원", "supabase_integration": "Supabase 통합", "project_sharing": "프로젝트를 친구와 공유", "project_download": "프로젝트 다운로드", "project_deployment": "배포 기능", "custom_domain": "프로젝트를 사용자 도메인에 연결", "tokens_creator_plus": "약 7개의 프레젠테이션 웹사이트용 토큰", "advanced_support": "더 빠른 응답 시간의 고급 지원", "prioritary_support": "전용 우선 지원", "early_feature_access": "신기능 조기 접근", "human_cto": "비즈니스를 위한 전담 CTO", "community_promotion": "커뮤니티와 함께 비즈니스 홍보"}, "JoinUsLive": "라이브로 참여하세요", "howToUseBiela": {"subtitle": "Biela 사용법", "title": "몇 분 만에 설명", "description": "각 기능이 어떻게 작동하는지 간단한 설명"}, "slides": {"newHistoryFeature": "Biela의 새로운 히스토리 기능을 확인하세요!", "newHistoryFeatureDescription": "히스토리 탭을 통해 이전 프로젝트 버전을 다시 보고 복원할 수 있어 Vibe 코딩 프로세스를 원활하게 유지하며 항상 제어할 수 있습니다!", "fixPreview": "Biela에서 미리보기를 수정하는 방법!", "fixPreviewDescription": "전체 가이드를 시청하고 모든 항목이 구축한 그대로 표시되는지 확인하세요.", "signUp": "Biela에 가입하는 방법!", "signUpDescription": "biela.dev가 처음이신가요? 빠른 가이드를 시청하여 가입하고 계정을 인증하는 방법을 알아보세요!", "buildWebsite": "Biela로 웹사이트를 만드는 방법!", "buildWebsiteDescription": "biela.dev를 사용하여 처음부터 웹사이트를 만들고 런칭하는 방법을 배워보세요 — 코드 한 줄 없이!", "downloadAndImport": "Biela에서 프로젝트 및 채팅 다운로드 및 가져오기 방법!", "downloadAndImportDescription": "단계별 가이드를 통해 완성된 프로젝트를 다운로드하고 저장된 채팅을 워크스페이스로 가져오는 방법을 배워보세요 — 언제든지 이전 상태에서 계속해서 Vibe 코딩을 이어갈 수 있습니다.", "shareProject": "BIELA Vibe 코드 프로젝트를 공유하는 방법!", "shareProjectDescription": "프로처럼 프로젝트를 공유할 준비가 되셨나요? 전체 가이드를 시청하고 시작해보세요.", "launchProject": "자신의 도메인에서 프로젝트를 시작하는 방법!", "launchProjectDescription": "이 영상에서는 사이트를 실시간으로 게시하기 위한 간단한 단계들을 안내합니다 — 설정부터 최종 게시까지, 제작에서 런칭까지 자신 있게 나아갈 수 있도록 도와드립니다.", "deployProject": "프로젝트를 배포하고 실시간으로 전환하는 방법!", "deployProjectDescription": "이 빠른 가이드는 웹사이트, 앱 또는 대시보드를 빠르고 원활하게 배포하는 모든 단계를 안내합니다."}, "maintenance": {"title": "문제를 해결하기 위해 노력하고 있습니다", "description": "Biela.dev는 현재 유지보수 모드에 있습니다. 문제를 해결하기 위해 노력하고 있습니다", "text": "감사합니다."}, "planCard": {"descriptionBeginner": "처음 프로젝트를 만들고 웹 개발을 배우는 초보자에게 적합합니다", "descriptionCreator": "고급 프로젝트를 위해 더 많은 성능과 유연성이 필요한 크리에이터에게 이상적입니다", "descriptionProfessional": "애플리케이션을 구축하는 전문가 개발자와 팀을 위한 완전한 솔루션", "perMillionTokens": "$ {{price}} / 백만 토큰당", "dedicatedCto": "전담 CTO - 24시간", "tokens": "토큰", "perMonth": "월별", "perYear": "연별", "freeTokens": "{{ tokens }} 무료 토큰", "monthly": "월간", "yearly": "연간", "save": "10% 절약"}, "faqLabel": "자주 묻는 질문", "faqHeading": "가장 자주 묻는 질문을 확인해보세요", "faqSubheading": "시작하세요. 이해하세요. 탐색하세요.", "whatAreTokensQuestion": "토큰이란 무엇인가요?", "whatAreTokensAnswer1": "토큰은 AI가 사용자의 프로젝트를 처리하는 방식입니다. Biela.dev에서는 프롬프트를 AI에게 보낼 때 토큰이 사용됩니다. 이 과정에서 프로젝트 파일이 메시지에 포함되어 AI가 코드를 이해할 수 있게 됩니다. 프로젝트가 클수록, AI의 응답이 길수록, 메시지당 더 많은 토큰이 필요합니다.", "whatAreTokensAnswer2": "또한 웹컨테이너 사용 시, 개발 환경 비용을 충당하기 위해 분당 소량의 토큰이 계정에서 차감됩니다.", "freePlanTokensQuestion": "무료 플랜에서는 몇 개의 토큰을 받나요?", "freePlanTokensAnswer": "무료 플랜 사용자는 하루에 200,000개의 토큰을 받을 수 있으며, 매일 초기화됩니다.", "outOfTokensQuestion": "토큰이 다 떨어지면 어떻게 되나요?", "outOfTokensAnswer": "메뉴 → 결제 로 이동하면 언제든지 즉시 추가 토큰을 구매할 수 있으니 걱정하지 마세요!", "changePlanQuestion": "나중에 요금제를 변경할 수 있나요?", "changePlanAnswer": "네 — 언제든지 가능합니다. <1>메뉴 → 결제</1> 에서 플랜을 업그레이드하거나 다운그레이드하거나 변경할 수 있습니다.", "rolloverQuestion": "사용하지 않은 토큰은 이월되나요?", "rolloverAnswer": "현재는 토큰이 만료일에 초기화되며 이월되지 않습니다 — 매일 최대한 활용하세요!", "exploreHowToUseBiela": "Biela 사용법 알아보기", "new": "신규", "joinVibeCoding": "Vibe Coding 해커톤에 참여하세요", "whatDoYouWantToBuild": "무엇을 만들고 싶으신가요?", "imaginePromptCreate": "상상하세요. 프롬프트를 입력하세요. 만들어 보세요.", "levelOfAmbition": "당신의 야망 수준은 어떻게 되나요?", "startGrowScale": "시작. 성장. 확장.", "calloutBar": {"start": "모든 요금제는 무료로 시작됩니다", "tokens": "하루 200,000개의 토큰", "end": ". 가입 절차 없이 바로 시작하세요."}, "atPriceOf": "다음 가격으로", "startFreeNoCreditCard": "무료로 시작 - 신용카드 필요 없음", "SignIn": "로그인", "Register": "회원가입", "Affiliates": "제휴사", "UsernameRequired": "사용자 이름이 필요합니다", "PasswordRequired": "비밀번호가 필요합니다", "MissingUsernamePasswordOrTurnstile": "사용자 이름, 비밀번호 또는 Turnstile 토큰이 누락되었습니다", "LoginSuccess": "로그인 성공!", "LoginFailed": "로그인 실패", "EmailRequired": "이메일이 필요합니다", "EmailInvalid": "유효한 이메일 주소를 입력하세요", "CaptchaRequired": "CAPTCHA를 완료해주세요", "ResetLinkSent": "재설정 링크가 이메일로 전송되었습니다.", "ResetFailed": "재설정 링크 전송 실패", "BackToLogin": "로그인으로 돌아가기", "EmailPlaceholder": "이메일 주소", "SendResetLink": "재설정 링크 보내기", "loading": "로딩 중", "checkingYourAuthenticity": "인증을 확인 중입니다", "ToUseBielaDev": "Biela.dev를 사용하려면 기존 계정에 로그인하거나 아래 옵션 중 하나로 계정을 생성해야 합니다", "Sign in with Google": "Google로 로그인", "Sign in with GitHub": "GitHub로 로그인", "Sign in with Email and Password": "이메일과 비밀번호로 로그인", "DontHaveAnAccount": "계정이 없으신가요?", "SignUp": "회원가입", "ByUsingBielaDev": "Biela.dev를 사용함으로써 데이터 수집에 동의합니다.", "EmailOrUsernamePlaceholder": "이메일/사용자 이름", "passwordPlaceholder": "비밀번호", "login.loading": "로딩 중", "LoginInToProfile": "프로필에 로그인", "login.back": "뒤로", "forgotPassword": "비밀번호를 잊으셨나요?", "PasswordsMismatch": "비밀번호가 일치하지 않습니다.", "PhoneRequired": "전화번호가 필요합니다", "ConfirmPasswordRequired": "비밀번호를 확인해 주세요", "AcceptTermsRequired": "이용 약관 및 개인정보 처리방침에 동의해야 합니다", "RegistrationFailed": "회원가입 실패", "EmailConfirmationSent": "이메일 확인 링크가 전송되었습니다. 이메일을 확인한 후 로그인해주세요.", "RegistrationServerError": "회원가입 실패 (서버 오류).", "SomethingWentWrong": "문제가 발생했습니다", "CheckEmailConfirmRegistration": "회원가입 확인을 위해 이메일을 확인하세요", "EmailConfirmationSentText": "확인 링크가 포함된 이메일을 전송했습니다.", "LoginToProfile": "프로필에 로그인", "username": "사용자 이름", "email": "이메일", "PasswordPlaceholder": "비밀번호", "ConfirmPasswordPlaceholder": "비밀번호 확인", "AcceptTermsPrefix": "다음에 동의합니다:", "TermsOfService": "이용 약관", "AndSeparator": "및", "PrivacyPolicy": "개인정보 처리방침", "CreateAccount": "계정 생성", "Back": "뒤로", "SignInWithGoogle": "Google로 로그인", "SignInWithGitHub": "GitHub로 로그인", "SignInWithEmailAndPassword": "이메일과 비밀번호로 로그인", "translation": {"AIModel": "AI 모델", "UpgradePlan": "프리미엄 요금제", "PremiumBadge": "프리미엄", "Active": "활성화됨", "projectInfo.features": "기능", "Stats": "통계", "Performance": "성능", "Cost": "비용", "UpgradeTooltip": "다음으로 업그레이드하여 잠금 해제: ", "UpgradeTooltipSuffix": "패키지", "chat": "채팅", "code": "코드"}, "extendedThinkingTooltip": "AI가 응답하기 전에 더 깊이 생각할 수 있도록 허용", "extendedThinkingTooltipDisabled": "리소스를 절약하기 위해 확장된 사고 비활성화", "AIModel": "AI 모델", "UpgradePlan": "프리미엄 요금제", "PremiumBadge": "프리미엄", "Active": "활성화됨", "projectInfo.features": "기능", "Stats": "통계", "Performance": "성능", "Cost": "비용", "UpgradeTooltip": "다음으로 업그레이드하여 잠금 해제: ", "UpgradeTooltipSuffix": "패키지", "HowBielaWorks": "BIELA 작동 방식", "WatchPromptLaunch": "보기. 작성. 시작.", "SearchVideos": "라이브러리에서 검색...", "NewReleased": "신규 출시", "Playing": "재생 중", "NoVideosFound": "비디오를 찾을 수 없습니다", "TryAdjustingYourSearchTerms": "검색어를 조정해 보세요", "feedback": {"title": {"issue": "문제 신고", "feature": "기능 요청"}, "description": {"issue": "버그를 발견하셨거나 문제가 발생하셨나요? 알려주시면 최대한 빠르게 해결하겠습니다.", "feature": "새로운 기능에 대한 아이디어가 있으신가요? 플랫폼 개선을 위한 여러분의 제안을 듣고 싶습니다."}, "success": {"title": "감사합니다!", "issue": "문제 신고가 성공적으로 제출되었습니다.", "feature": "기능 요청이 성공적으로 제출되었습니다."}, "form": {"fullName": {"label": "이름", "placeholder": "이름을 입력하세요"}, "email": {"label": "이메일 주소", "placeholder": "이메일 주소를 입력하세요"}, "suggestion": {"labelIssue": "문제 설명", "labelFeature": "기능 제안", "placeholderIssue": "발생한 문제를 설명해주세요...", "placeholderFeature": "원하는 기능을 설명해주세요..."}, "screenshots": {"label": "스크린샷 (선택 사항)", "note": "문제를 더 잘 이해할 수 있도록 스크린샷을 추가해주세요", "drop": "클릭하거나 파일을 끌어다 놓으세요", "hint": "PNG, JPG, JPEG 형식으로 각 10MB까지 지원", "count": "{{count}}개의 이미지 선택됨"}}, "buttons": {"cancel": "취소", "submitting": "제출 중...", "submitIssue": "문제 제출", "submitFeature": "요청 제출"}, "errors": {"fullNameRequired": "전체 이름이 필요합니다", "fullNameNoNumbers": "전체 이름에는 숫자가 포함될 수 없습니다", "emailRequired": "이메일 주소가 필요합니다", "emailInvalid": "유효한 이메일 주소를 입력해주세요", "suggestionRequired": "문제나 제안사항을 설명해주세요"}}, "changelog": {"title": "변경 로그", "description": "항상. 진화합니다. 앞으로.", "TotalReleases": "전체 릴리스 수", "ActiveUsers": "활성 사용자", "FeaturesAdded": "추가된 기능", "BugsFixed": "수정된 버그", "shortCallText": "제안 사항이나 버그를 발견하셨나요? 여러분의 의견을 기다립니다!", "reportIssue": "문제 신고", "requestFeature": "기능 요청", "totalReleases": "총 릴리스", "activeUsers": "활성 사용자", "featuresAdded": "추가된 기능", "bugsFixed": "수정된 버그", "types": {"feature": "기능", "improvement": "개선", "bugfix": "버그 수정", "ui/ux": "UI/UX", "announcement": "공지", "general": "일반"}, "versions": {"v3.2.3": {"date": "2025년 6월 26일", "changes": {"0": "사용자 대시보드의 일부 UI/UX 문제를 수정했습니다.", "1": "WebContainer의 안정성을 개선했습니다.", "2": "AI Diff 모드를 개선하여 작은 파일과 큰 파일을 다르게 처리하도록 했습니다."}}, "v3.2.2": {"date": "2025년 6월 24일", "changes": {"0": "더 나은 사용자 경험을 위해 프론트 오피스를 재구성하고 재설계했습니다.", "1": "이 놀라운 변경 로그 페이지를 구현했습니다.", "2": "WebContainer의 재연결 알고리즘을 개선했습니다.", "3": "폴더 업로드에 추가로 ZIP 파일을 통한 프로젝트 업로드 지원을 추가했습니다.", "4": "더 나은 인덱싱을 활용하여 모든 API 호출의 성능을 개선했습니다."}}, "v3.2.1": {"date": "2025년 6월 24일", "changes": {"0": "IDE: 사용자 대시보드의 \"폴더\" 섹션이 향상된 경험을 위해 재설계되었습니다.", "1": "Content Studio: 이제 Content Studio 내에서 이미지를 복사할 수 있습니다."}}, "v3.2.0": {"date": "2025년 6월 17일", "changes": {"0": "AI Diff 모드 (실험적): AI가 이제 전체 파일을 재생성하는 대신 원본 파일과 업데이트된 버전 간의 차이점만 작성합니다. 이는 성능을 크게 향상시키고 토큰 사용을 최적화합니다. 프로젝트의 설정 → 제어 센터 영역에서 이 실험적 기능을 활성화하거나 비활성화할 수 있습니다."}}, "v3.1.6": {"date": "2025년 6월 12일", "changes": {"0": "IDE: 구독 및 추가 토큰에 대한 쿠폰 코드 필드가 추가되었습니다."}}, "v3.1.5": {"date": "2025년 6월 11일", "changes": {"0": "IDE: \"프로젝트 공유\" 플로우에 다양한 수정 사항이 적용되었습니다."}}, "v3.1.4": {"date": "2025년 6월 10일", "changes": {"0": "IDE: 패키지 구독이 재검토되고 수정되어 암호화 포함 및 Stripe 구현 문제를 해결했습니다."}}, "v3.1.3": {"date": "2025년 6월 9일", "changes": {"0": "AI: 더 나은 지연 시간을 달성하기 위해 Gemini의 직접 API 통합을 구현하고 테스트했습니다."}}, "v3.1.2": {"date": "2025년 6월 6일", "changes": {"0": "제휴: 새 대시보드의 정보 모달과 디자인 일관성을 갖춘 제휴 결제 작업을 구현했습니다 (결제 내역).", "1": "Content Studio: 사용자가 링크와 함께 이미지를 AI에 보낼지 결정할 수 있는 새로운 옵션이 추가되었습니다. 기본적으로 \"아니오\"로 설정되어 있습니다."}}, "v3.1.1": {"date": "2025년 6월 5일", "changes": {"0": "Content Studio: Content Studio를 프로젝트별로 구성하기 위한 업데이트가 이루어졌습니다."}}, "v3.1.0": {"date": "2025년 6월 3일", "changes": {"0": "🚀 주요 BIELA.dev 업데이트가 막 도착했습니다! 🚀", "1": "AI가 이제 오늘 아침보다 5배 빠르게 코드를 작성합니다. 네, 맞게 읽으셨습니다. BIELA의 성능을 과충전하여 아이디어에서 작동하는 코드까지 순식간에 이동할 수 있게 했습니다.", "2": "터미널: 이제 더욱 향상됨: 오늘 아침의 개편을 바탕으로 더 부드러운 코딩 플로우를 위한 추가 UI/UX 다듬기를 추가했습니다.", "3": "Supabase 연결 개선: 더 깔끔하고 직관적인 인터페이스로 설정과 통합이 손쉬워집니다.", "4": "오류 처리 업데이트 (오류 자동 처리): 자동 오류 보고가 철회되었습니다. 수동과 AI 지원 처리 중에서 선택할 수 있는 새로운 설정을 준비하고 있습니다.", "5": "새로운 제어 센터 (프로젝트 설정 내) 및 단위 테스트 인터페이스: 첫 번째 기능: 단위 테스트 터미널 전환. 더 많은 컨트롤이 곧 나옵니다!", "6": "향상된 다운로드 로직: 다운로드는 이제 코드가 완전히 푸시될 때까지 기다려 매번 완전하고 정확한 내보내기를 보장합니다."}}, "v3.0.0": {"date": "2025년 6월 3일", "changes": {"0": "🚀 BIELA.dev 업데이트 - 파워 부스트를 받았습니다! 🚀", "1": "개편된 터미널 UI: 터미널에서의 작업을 즐겁게 만드는 더 세련되고 깔끔한 디자인.", "2": "원클릭 NPM 액션: 새로운 액션 버튼으로 npm install, npm run build, npm run dev를 즉시 실행 - 타이핑할 필요 없음! (기능이 터미널에 통합됨)", "3": "더 스마트한 오류 처리 및 오류 색상 분류: 미리보기 오류가 이제 AI에 자동으로 전송되어 더 빠른 디버깅을 도와줍니다. (프로젝트를 개발하는 동안 BIELA의 부드러운 버그 수정을 경험하게 됩니다)", "4": "향상된 파일 네비게이션: 채팅에서 파일 이름을 클릭하면 이제 WebContainer에서 바로 열립니다. 탭하고 편집하기만 하면 됩니다!"}}, "v2.1.1": {"date": "2025년 5월 30일", "changes": {"0": "IDE: WebContainer용 저장 버튼이 추가되어 수동 업데이트 시 나타나며 GitHub로의 커밋과 푸시를 트리거합니다.", "1": "대시보드: 사용자 대시보드에서 가져온 프로젝트 이름을 바꿀 때 발생하던 오류를 수정했습니다.", "2": "WebContainer: 메모리 누수에 대한 긴급 수정이 구현되었습니다."}}, "v2.1.0": {"date": "2025년 5월 28일", "changes": {"0": "🚀 BIELA.dev 업데이트 — 7가지 새로운 개선 사항이 방금 도착했습니다! 🚀", "1": "IDE: 인터페이스에서 직접 자신의 도메인을 biela 프로젝트에 연결할 수 있는 가능성을 추가했습니다.", "2": "배포된 프로젝트 미리보기: 이제 눈 아이콘을 한 번 클릭으로 배포된 프로젝트를 미리볼 수 있습니다 - 안에 무엇이 있는지 알기 위해 각각을 열 필요가 없습니다.", "3": "복제된 프로젝트의 비용 추적 수정: 복제된 프로젝트가 원본 프로젝트의 비용 추정을 상속받던 문제를 수정했습니다. 이제 0부터 시작하여 프로젝트별로 정확한 추적을 제공합니다.", "4": "실시간 비용 추정 (프롬프트당): 비용 추정이 이제 하루에 한 번이 아니라 각 프롬프트 후에 업데이트됩니다. 이는 개발 에이전시로 같은 것을 개발하는 비용과 BIELA로 얼마나 절약하는지에 대한 실시간 피드백을 제공합니다.", "5": "히스토리 탭 및 롤백 수정: 이제 프로젝트 히스토리를 다시 안전하게 롤백할 수 있습니다. 최근 주요 업데이트 후 이 기능에 문제가 있었지만 이제 매우 부드럽습니다.", "6": "자동 스크롤 개선: 코딩하는 동안 더 이상 화면이 정지되지 않습니다! 생성 중 스크롤이 지연되던 문제를 수정했습니다. 원활한 플로우를 즐기세요.", "7": "메뉴 페이지가 이제 새 탭에서 열림: Labs에서 코딩하고 다른 메뉴 항목을 클릭하면 이제 새 탭에서 열려 현재 진행 중인 작업을 잃지 않습니다."}}, "v2.0.4": {"date": "2025년 5월 27일", "changes": {"0": "대시보드: 사용자 대시보드의 스크린샷 개선에는 자동 스크롤 제거, 사용자 제어 스크롤 허용, 스크린샷 로딩용 애니메이션 추가가 포함됩니다."}}, "v2.0.3": {"date": "2025년 5월 20일", "changes": {"0": "Content Studio: Content Studio의 로봇 애니메이션이 이제 전체 크기로 표시됩니다.", "1": "제휴: Firefox에서 볼 때 제휴 대시보드의 표시 문제를 수정했습니다."}}, "v2.0.2": {"date": "2025년 5월 19일", "changes": {"0": "Content Studio: Content Studio에서 한 번에 허용되는 최대 파일 수가 50개로 증가했습니다 (10개에서)."}}, "v2.0.1": {"date": "2025년 5월 18일", "changes": {"0": "Content Studio: 페이지가 새로고침되지 않으면 여러 파일의 태그가 업데이트되지 않던 문제를 수정했습니다.", "1": "Content Studio: 이미지를 선택할 때 편집 버튼이 더 잘 보이게 되었습니다.", "2": "Content Studio: 업로드 시 폴더 위치가 이제 자동으로 마지막으로 생성되거나 마지막으로 선택된 폴더를 선택합니다.", "3": "Content Studio: 이미지를 드래그 앤 드롭할 때 선택된 폴더가 이제 유지됩니다.", "4": "Content Studio: 탭 제목이 이제 새로운 색상을 사용합니다 (녹색 대신).", "5": "Content Studio: \"이미지 삽입\" 버튼이 \"프로젝트에서 사용\"으로 이름이 변경되었습니다.", "6": "Content Studio: 파일 업로드 버튼이 이제 녹색 배경을 가집니다.", "7": "Content Studio: 더 나은 정렬을 위해 검색 바의 높이가 감소했습니다.", "8": "Content Studio: 사용자에게 이미지가 없으면 검색 바가 더 이상 표시되지 않습니다."}}, "v2.0.0": {"date": "2025년 5월 16일", "changes": {"0": "🚀 BIELA.DEV의 주요 업데이트! 🚀", "1": "우리만의 웹 컨테이너 기술: 모든 외부 공급업체로부터 완전히 독립적인 우리만의 웹 컨테이너 기술을 구축했습니다! 이는 그 어느 때보다 빠르고 나은 새 기능을 개발할 수 있는 전례 없는 유연성을 제공합니다.", "2": "코드 생성을 위한 다중 LLM 옵션: 이제 Anthropic을 넘어 코드 생성을 위한 다중 AI 모델을 지원합니다! 이제 코딩 프로젝트에 Google의 Gemini를 사용할 수 있으며, 이는 몇 가지 주요 장점을 가져다 줍니다. Gemini는 100만 토큰의 거대한 컨텍스트 윈도우를 제공합니다!", "3": "Content Studio: 당신의 사진, 당신의 프로젝트: 새로운 Content Studio로 완전히 자신을 표현하세요! 이제 Vibe Coding 프로젝트에서 사용할 자신만의 사진을 업로드할 수 있습니다.", "4": "프로젝트 공유: 협업이 더욱 좋아졌습니다! 이제 다른 사용자와 프로젝트를 공유할 수 있습니다.", "5": "기존 Supabase 프로젝트에 연결: 이 강력한 새 기능을 통해 여러 Biela.dev 프로젝트를 동일한 Supabase 데이터베이스에 연결할 수 있습니다."}}, "v1.0.5": {"date": "2025년 5월 15일", "changes": {"0": "결제: Stripe 결제 처리가 구현되었습니다.", "1": "Content Studio: 첫 페이지에 이미지가 없을 때 애니메이션 (로봇 같은)이 이제 표시됩니다."}}, "v1.0.4": {"date": "2025년 5월 13일", "changes": {"0": "Content Studio: Content Studio의 페이지네이션 문제를 수정했습니다."}}, "v1.0.3": {"date": "2025년 5월 5일", "changes": {"0": "Content Studio 출시 - 모든 미디어를 저장하고 관리하는 곳.", "1": "프론트 오피스: biela_frontoffice에서 소프트웨어 앱용 schema.org를 구현했습니다.", "2": "팀 대시보드: 팀 대시보드에 알림이 추가되었습니다 (예: 새 멤버가 참여하거나 채팅에 새 메시지가 나타날 때 빨간 점).", "3": "IDE: 프롬프트를 제공하는 동안 WebContainer 내의 특정 영역을 클릭할 때 메뉴의 모달이 제대로 닫히지 않던 버그를 수정했습니다."}}, "v1.0.2": {"date": "2025년 5월 2일", "changes": {"0": "제휴: 팀 멤버 영역의 재설계를 구현했습니다.", "1": "제휴: 제휴 대시보드 아이콘이 업데이트되었습니다.", "2": "IDE: \"Supabase 연결\" 버튼 텍스트가 새 아이콘과 함께 \"데이터베이스\"로 변경되었으며, 연결 시 파란색으로 \"데이터베이스 연결됨\"을 표시합니다."}}, "v1.0.1": {"date": "2025년 5월 1일", "changes": {"0": "IDE (설정 탭): 설정 채팅에서 데이터베이스 통합을 구현했습니다.", "1": "대시보드: 사용자 대시보드의 페이지네이션을 수정했습니다."}}, "v1.0.0": {"date": "2025년 4월 18일", "changes": {"0": "🚀 Biela.dev에 오신 것을 환영합니다: 모든 사람을 위한 무료 AI 기반 코딩 동반자! 🚀", "1": "Biela.dev의 최초 공개 릴리스를 발표하게 되어 기쁩니다! 최첨단 AI 지원 웹 개발을 모든 사람이 접근할 수 있게 만드는 것을 믿기 때문에 우리 플랫폼은 첫날부터 완전히 무료입니다."}}}}}