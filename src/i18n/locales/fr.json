{"meta": {"home": {"title": "biela.dev | Créateur Web & App Propulsé par l'IA – Construisez avec des Prompts", "description": "Transformez vos idées en sites web ou applications avec biela.dev. Utilisez des prompts pilotés par l'IA pour créer des produits numériques personnalisés sans effort"}, "privacy": {"title": "Politique de Confidentialité de biela.dev", "description": "Comprenez comment biela.dev collecte, utilise et protège vos informations personnelles."}, "terms": {"title": "Conditions d'Utilisation de biela.dev", "description": "Consultez les termes et conditions d'utilisation de la plateforme de développement propulsée par l'IA de biela.dev"}, "changelog": {"title": "Journal des modifications de biela.dev - Toujours. En évoluant. En avant.", "description": "Découvrez comment biela.dev évolue avec chaque version"}, "competitionterms": {"title": "Conditions Générales du Concours de Développement Biela", "description": "Participez au concours de développement Biela — un défi officiel d’un mois organisé par Biela.dev et l’Institut TeachMeCode. Présentez vos créations numériques, obtenez de la reconnaissance grâce à l’engagement communautaire et visez les meilleures places. Date limite de soumission : 14 mai 2025."}, "contest": {"title": "Concours de Développement biela.dev 2025 – <PERSON><PERSON><PERSON><PERSON>", "description": "Découvrez les gagnants du concours biela.dev et explorez les incroyables projets construits avec l'IA qui ont remporté les plus grands honneurs"}, "howTo": {"title": "Comment utiliser Biela.dev – Guide complet pour créer des applications alimentées par l'IA", "description": "Découvrez comment utiliser Biela.dev pour créer rapidement des applications web sans coder. Un guide étape par étape complet avec des tutoriels et des conseils pratiques.", "keywords": "biela, biela.dev, comment utiliser biela, créer des applications IA, no code, développement rapide, guide biela, tutoriel biela", "ogTitle": "Comment utiliser Biela.dev – Guide pas à pas", "ogDescription": "Apprenez à créer des applications IA avec Biela.dev. De l'idée au lancement, sans aucune ligne de code.", "twitterTitle": "Comment utiliser Biela.dev – Guide complet", "twitterDescription": "Créez des applications IA sur Biela.dev sans coder. Suivez ce guide étape par étape facile !"}}, "navbar": {"home": "Accueil", "demo": "Démo", "community": "Communauté", "affiliate": "Affiliation", "partners": "Partenaires", "contest": "Hackathon de Codage Vibe", "subscriptions": "Abonnements", "pricing": "<PERSON><PERSON><PERSON>"}, "counter": {"days": "Jours", "hours": "<PERSON><PERSON>", "minutes": "Minutes", "seconds": "Secondes", "centisec": "Centisecondes"}, "hero": {"title": "Transformez votre idée en site web ou application en quelques minutes", "subtitle": "Si tu peux <1>l'imaginer</1>, tu peux <5>le coder</5>.", "subtitle2": "Commencez gratuitement. Codez tout. Transformez vos compétences à chaque demande en une opportunité.", "timeRunningOut": "Le temps presse!", "launchDate": "Version WoodSnake", "experimentalVersion": "Lancement bêta le 15 avril 2025", "earlyAdopter": "Rejoignez-nous maintenant pour bénéficier des avantages réservés aux premiers utilisateurs avant le lancement officiel", "tryFree": "Essayer gratuitement", "seeAiAction": "Voir l'IA en action", "tellBiela": "Demandez à Biela de créer", "inputPlaceholder": "Si vous pouvez l'imaginer, BIELA peut le coder. Que faisons-nous aujourd'hui ?", "chat": "Cha<PERSON>", "code": "Code", "checklist": "Liste de contrôle", "checklistTitle": "Utilisez des listes de contrôle pour", "checklistItems": {"trackDevelopment": "Suivre les tâches de développement", "setRequirements": "Définir les exigences du projet", "createTesting": "Créer des protocoles de test"}, "registerNow": "Ins<PERSON>rivez-vous maintenant", "attachFiles": "Joignez des fichiers à votre demande", "voiceInput": "Utilisez la saisie vocale", "enhancePrompt": "<PERSON><PERSON><PERSON><PERSON> votre demande", "cleanupProject": "Nettoyez le projet", "suggestions": {"weatherDashboard": "<PERSON><PERSON>ez un tableau de bord météo", "ecommercePlatform": "Créez une plateforme de commerce électronique", "socialMediaApp": "Concevez une application de réseau social", "portfolioWebsite": "Générez un site web de portfolio", "taskManagementApp": "Créez une application de gestion des tâches", "fitnessTracker": "<PERSON><PERSON><PERSON> un tracker de fitness", "recipeSharingPlatform": "Concevez une plateforme de partage de recettes", "travelBookingSite": "Créez un site de réservation de voyages", "learningPlatform": "C<PERSON>ez une plateforme d'apprentissage", "musicStreamingApp": "Concevez une application de streaming musical", "realEstateListing": "<PERSON><PERSON>ez une annonce immobilière", "jobBoard": "C<PERSON>ez un tableau d'offres d'emploi"}}, "videoGallery": {"barber": {"title": "Site web de salon de coiffure", "description": "Découvrez comment Biela.dev crée un site web complet de salon de coiffure en quelques minutes"}, "tictactoe": {"title": "<PERSON><PERSON> <PERSON>", "description": "Regardez l'IA créer un jeu de Tic Tac Toe plus rapidement que vous ne pouvez le dire"}, "coffeeShop": {"title": "Site web de café", "description": "Admirez Biela.dev en train de créer un site web de café"}, "electronicsStore": {"title": "Site web de magasin d'électronique", "description": "Voyez comment Biela.dev crée un magasin en ligne complet en quelques minutes"}, "seoAgency": {"title": "Site web d'agence SEO", "description": "Regardez l'IA créer un site web d'agence SEO"}}, "telegram": {"title": "<PERSON><PERSON><PERSON>ez notre Telegram", "subtitle": "Connectez-vous avec la communauté Biela", "description": "Obtenez une assistance instantanée, partagez vos projets et connectez-vous avec des passionnés d'IA du monde entier. <br/><br/> Notre communauté Telegram est le moyen le plus rapide de rester à jour avec Biela.dev.", "stats": {"members": "Me<PERSON><PERSON>", "support": "Support", "countries": "Pays", "projects": "Projets"}, "joinButton": "Rejoindre notre communauté Telegram", "communityTitle": "Communauté Biela.dev", "membersCount": "membres", "onlineCount": "en ligne maintenant", "messages": {"1": "Je viens de lancer mon site e-commerce avec Biela.dev en moins d'une heure !", "2": "Ça a l'air incroyable ! Comment as-tu géré le catalogue de produits ?", "3": "Biela l'a géré automatiquement ! J'ai simplement décrit ce que je voulais.", "4": "Je suis en train de créer un site portfolio en ce moment. Les suggestions IA sont incroyables !", "5": "Quelqu'un a essayé les nouveaux modèles responsives ? Ils sont fantastiques sur mobile."}}, "joinNetwork": {"title": "<PERSON><PERSON><PERSON><PERSON> notre r<PERSON>", "subtitle": "Connectez. Développez. Gagnez.", "affiliateProgram": "Programme d'affiliation", "registerBring": "Inscrivez-vous et amenez 3 affiliés", "aiCourse": "Cours d'ingénierie IA", "courseDescription": "Cours complet gratuit d'IA avec TeachMeCode", "digitalBook": "Livre numérique", "bookDescription": "\"L'art de l'ingénierie de prompts\" édition numérique exclusive", "exclusiveBenefits": "Avantages exclusifs", "benefitsDescription": "Revenus à vie et privilèges spéciaux pour les membres", "registerNow": "S'inscrire maintenant", "teamEarnings": "Gains d'équipe", "buildNetwork": "<PERSON>st<PERSON><PERSON><PERSON> votre r<PERSON>", "weeklyMentorship": "Mentorat hebdomadaire", "teamBonuses": "Bonus d'équipe", "startEarning": "Commencez à gagner aujourd'hui", "upToCommission": "JUSQU'À Commission"}, "partners": {"title": "Approuvé par les leaders de l'industrie", "subtitle": "Collaboration avec des innovateurs mondiaux pour façonner l'avenir du développement propulsé par l'IA", "strategicPartnerships": "Partenariats stratégiques", "joinNetwork": "Rejoignez le réseau en pleine expansion de partenaires et d'entreprises qui transforment le développement avec BIELA."}, "footer": {"quickLinks": {"title": "Liens rapides", "register": "S'inscrire", "bielaInAction": "Voir Biela.dev en action", "liveOnTelegram": "Nous sommes en direct sur Telegram", "affiliate": "Marketing d'affiliation collaboratif", "partners": "Partenaires"}, "legal": {"title": "Mentions légales", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. Tous droits réservés.", "unauthorized": "Toute utilisation, reproduction ou distribution non autorisée de ce service, en tout ou en partie, sans autorisation écrite explicite est strictement interdite."}, "reservations": "TeachMeCode Institute se réserve tous les droits légaux sur la propriété intellectuelle associée à Biela."}, "bottomBar": {"left": "2025 Biela.dev. Propulsé par %s © Tous droits réservés.", "allRights": "Tous droits réservés.", "right": "Développé par %s"}}, "demo": {"title": "Regardez l’IA créer des applications et des sites web en temps réel", "subtitle": "Découvrez comment les entreprises transforment leur présence numérique avec Biela.dev", "seeInAction": "Voir Biela.dev en action", "whatKind": "Quel type de site web avez-vous besoin ?", "describeWebsite": "Décrivez votre idée de site web (ex., 'Un portfolio pour un photographe')", "generatePreview": "Générer un aperçu", "nextDemo": "<PERSON><PERSON><PERSON> suiva<PERSON>", "startBuilding": "Commencez à construire et à gagner avec Biela.dev aujourd'hui"}, "common": {"loading": "Chargement...", "error": "Une erreur s'est produite", "next": "Suivant", "previous": "Précédent", "submit": "So<PERSON><PERSON><PERSON>", "cancel": "Annuler", "close": "<PERSON><PERSON><PERSON>", "loginNow": "Se connecter maintenant", "verifyAccount": "Vérifier le compte"}, "languageSwitcher": {"language": "<PERSON><PERSON>"}, "contest": {"bielaDevelopment": "Hackathon de Codage Vibe", "competition": "COMPÉTITION", "firstPlace": "1ère PLACE", "secondPlace": "2ème PLACE", "thirdPlace": "3ème PLACE", "currentLeader": "Leader actuel :", "footerText": "Développement par IA réalisé par Biela.dev, soutenu par l’Institut TeachMeCode®", "browseHackathonSubmissions": "Parcourir les soumissions du Hackathon", "winnersAnnouncement": "🎉 Gagnants du Concours Annoncés ! 🎉", "congratulationsMessage": "Félicitations à nos incroyables gagnants ! Merci à tous ceux qui ont participé à ce hackathon incroyable.", "conditionActiveReferrals": "A 3 parrainages actifs", "conditionLikedProject": "A aimé un autre projet", "winner": "<PERSON><PERSON><PERSON>", "qualificationsMet": "Qualifications :"}, "competition": {"header": {"mainTitle": "Votre consigne. Votre style. Votre terrain de jeu.", "timelineTitle": "CALENDRIER", "timelineDesc": "Un mois pour créer votre meilleur projet généré par l’IA", "eligibilityTitle": "ADMISSIBILITÉ", "eligibilityDesc": "Les comptes actifs sur Biela.dev qui soumettent un projet", "prizesTitle": "PRIX", "prizesDesc": "16 000 $ en prix en espèces et un accès gratuit pour les gagnants", "votingTitle": "VOTE", "votingDesc": "Piloté par la communauté, un vote par utilisateur vérifié", "tagline": "Laissez parler votre créativité. Que le monde vote."}, "details": {"howToEnter": "Comment participer", "enter": {"step1": {"title": "Créez un projet en utilisant l’IA de Biela.dev", "desc": "Utilisez les puissants outils d’IA de Biela pour construire votre projet innovant"}, "step2": {"title": "Accédez à votre tableau de bord utilisateur", "desc": "Cliquez sur \"Publier pour le Hackathon\" à côté de votre projet", "subdesc": "Vous pouvez soumettre jusqu’à 3 projets différents"}, "step3": {"title": "Biela traitera votre candidature :", "list1": "Prenez une capture d’écran de votre projet", "list2": "Vérifiez que vos consignes répondent aux critères d'admissibilité", "list3": "Générez un résumé et une description"}}, "prizeStructure": "Structure des prix", "prizes": {"firstPlace": "1ère place", "firstPlaceValue": "10 000 $", "secondPlace": "2ème place", "secondPlaceValue": "5 000 $", "thirdPlace": "3ème place", "thirdPlaceValue": "1 000 $", "fourthToTenth": "Du 4ème au 10ème", "fourthToTenthValue": "Accès illimité pendant 30 jours"}}, "qualification": {"heading": "Conditions de participation", "description": "Pour être éligible aux 3 meilleurs prix (10 000 $, 5 000 $ et 1 000 $), vous devez remplir les critères suivants :", "criteria1": "Avoir au moins 3 parrainages actifs", "criteria2": "Aimer au moins un projet du mur d’exposition", "proTipLabel": "Conseil de pro :", "proTipDesc": "Plus tôt vous soumettez votre projet, plus il aura de visibilité et de votes !"}, "voting": {"heading": "<PERSON><PERSON><PERSON>", "oneVotePolicyTitle": "Une seule vote", "oneVotePolicyDesc": "Vous ne pouvez voter qu’une fois et pas pour votre propre projet", "accountVerificationTitle": "Vérification du compte", "accountVerificationDesc": "Seuls les utilisateurs dont les comptes Biela.dev sont vérifiés peuvent voter", "tieBreakingTitle": "Résolution des égalités", "tieBreakingDesc": "En cas d’égalité, le facteur décisif sera le nombre total d’étoiles accumulées par le créateur du projet (utilisées ou non)", "howToVoteTitle": "Comment voter", "howToVoteDesc": "Pa<PERSON><PERSON>ez le mur d’exposition, explorez tous les projets générés par l’IA publiés et cliquez sur l’icône cœur pour voter pour votre projet préféré !"}}, "contestItems": {"projectShowcase": "<PERSON><PERSON><PERSON> de projet", "loading": "Chargement...", "createBy": "C<PERSON><PERSON> par ", "viewProject": "Voir le projet", "stars": "<PERSON><PERSON><PERSON>", "overview": "<PERSON><PERSON><PERSON><PERSON>", "keyFeatures": "Caractéristiques principales", "exploreLiveProject": "Explorer le projet en direct", "by": "par", "likeLimitReached": "<PERSON>ite de <PERSON> atteinte", "youCanLikeMaximumOneProject": "Vous pouvez aimer un seul projet. Souhaitez-vous annuler votre like actuel et aimer celui-ci à la place ?", "currentlyLikedProject": "Projet actuellement aimé", "switchMyLike": "Changer mon like", "mustLoggedIntoLikeProject": "V<PERSON> devez être connecté pour aimer un projet.", "signInToBielaAccountToVote": "Veuillez vous connecter à votre compte Biela.dev pour participer au processus de vote.", "yourAccountIsNotVerifiedYet": "Votre compte n'est pas encore vérifié. Veuillez vérifier votre compte pour pouvoir aimer un projet.", "accountVerificationEnsureFairVoting": "La vérification du compte garantit un vote équitable et aide à maintenir l'intégrité de la compétition.", "projectsSubmitted": "Projets soumis :", "unlikeThisProject": "Ne plus aimer ce projet", "likeThisProject": "Aimer ce projet", "likes": "j'aime", "like": "j'aime", "somethingWentWrong": "Une erreur s'est produite. Veuillez réessayer plus tard", "voteError": "<PERSON><PERSON><PERSON> de vote"}, "textsLiveYoutube": {"first": {"title": "Vous êtes invité !", "description": "Nous serons en direct ce vendredi — cette session porte sur la construction avec <green>objectif</green>, <blue>précision</blue> et <orange>pure vibe</orange>.", "secondDescription": "Apprenez à <green>maî<PERSON><PERSON> le vibe coding</green>, <blue>découvrir de nouvelles fonctionnalités</blue> avant tout le monde, et <orange>voir des projets réels se construire en direct</orange>.", "specialText": "Cliquez ici et appuyez sur « Me notifier » sur YouTube — ne le manquez pas."}, "second": {"title": "Soyez le premier à créer.", "description": "<PERSON> vendredi, nous portons le <green>vibe coding</green> à un tout autre niveau — nous vous montrons comment <blue>créer avec intention</blue>, <orange>débloquer de nouvelles fonctionnalités</orange> et lancer <green>plus vite que jamais</green>.", "secondDescription": "<green>Nouveaux builds</green>, <blue>nouvelles idées</blue> et <orange>nouvelles opportunités</orange> de croissance.", "specialText": "Cliquez ici pour définir votre rappel YouTube — et faites partie de la prochaine vague de créateurs."}, "third": {"title": "Réservez votre place.", "description": "Notre prochain <green>livestream Biela.dev</green> aura lieu ce vendredi — et vous êtes <blue>officiellement invité</blue>.", "secondDescription": "Nous plongeons dans le <green>vibe coding</green>, révélons <blue>puissantes nouvelles fonctionnalités</blue> et vous aidons à construire <orange>plus intelligemment, plus rapidement et à plus grande échelle</orange>.", "specialText": "Cliquez ici et appuyez sur « Me notifier » sur YouTube — votre prochaine grande idée pourrait commencer ici."}}, "stream": {"title": "Soyez le <1>Premier</1> à <3>Construire</3>.", "subtitle": "Re<PERSON><PERSON><PERSON> notre livestream de vendredi — fonctionnalités, projets et mises à jour exclusives.", "button": "Définissez votre rappel YouTube."}, "billings": {"modalTitle": "Choisissez un forfait", "modalSubtitle": "Choisissez parmi nos trois forfaits flexibles pour continuer à coder sans limites.", "toggleMonthly": "<PERSON><PERSON><PERSON>", "toggleYearly": "<PERSON><PERSON> (économisez 10 %)", "mostPopular": "Le plus populaire", "tokensLowerCase": "tokens", "tokensUpperCase": "Tokens", "below": "ci-dessous", "unlimited": "ILLIMITÉ", "10Bonus": "10 % de bonus", "currentPlan": "Forfait actuel", "upgradePlan": "Mettre à niveau le forfait", "purchaseDetails": "<PERSON>é<PERSON> de l'achat", "dateLabel": "Date", "planLabel": "Forfait", "amountLabel": "<PERSON><PERSON>", "whatsNext": "Et ensuite", "yourTokensAvailable": "Vos tokens sont maintenant disponibles sur votre compte", "purchaseDetailsStored": "Les détails de l'achat sont enregistrés dans votre compte", "viewPurchaseHistory": "<PERSON><PERSON> pouvez consulter votre historique d'achats dans la section facturation", "thankYou": "<PERSON><PERSON><PERSON> !", "purchaseSuccessful": "Votre achat a été effectué avec succès", "startCoding": "Commencer à coder", "month": "mois", "year": "ann<PERSON>"}, "feature": {"basic_ai": "Développement IA Basique", "community_support": "Support Communautaire", "standard_response": "<PERSON><PERSON><PERSON> de Réponse Standard", "basic_templates": "<PERSON><PERSON><PERSON><PERSON>", "advanced_ai": "Développement IA Avancé", "priority_support": "Support Prioritaire", "fast_response": "<PERSON><PERSON>lai de Réponse Rapide", "premium_templates": "Modèles Premium", "team_collaboration": "Collaboration d'Équipe", "enterprise_ai": "Développement IA d'Entreprise", "support_24_7": "Support Prioritaire 24/7", "instant_response": "<PERSON><PERSON><PERSON> Réponse Instantané", "custom_templates": "<PERSON><PERSON><PERSON><PERSON>", "advanced_team": "Fonctionnalités Avancées d'Équipe", "custom_integrations": "Intégrations Personnalisées", "big_context_window": "Grande Fenêtre de Contexte - 5x Plus Grande que la Moyenne", "code_chat_ai": "Accès à l'IA de Code & Chat", "tokens_basic": "Tokens suffisants pour env. 3 sites de présentation", "standard_webcontainer": "Webcontainer Standard Dédié", "medium_context_window": "Fenêtre de Contexte Moyenne - Adaptée pour un site, un blog ou un jeu navigateur basique", "basic_support": "Support Basique avec Délai de Réponse Standard", "supabase_integration": "Intégration Supabase", "project_sharing": "Partager des Projets avec Vos Amis", "project_download": "Télécharger Vos Projets", "project_deployment": "Fonctionnalité de Déploiement", "custom_domain": "Connecter les Projets à Votre Propre Domaine", "tokens_creator_plus": "Tokens suffisants pour env. 7 sites de présentation", "advanced_support": "Support Avancé avec Délai de Réponse Accéléré", "prioritary_support": "Support Prioritaire Dédié", "early_feature_access": "Accès Anticipé aux Nouvelles Fonctionnalités", "human_cto": "CTO Humain Dédié pour Votre Entreprise", "community_promotion": "Promouvoir Votre Entreprise avec Notre Communauté"}, "JoinUsLive": "Rejoignez-nous en direct", "howToUseBiela": {"subtitle": "Comment utiliser Biela", "title": "Expliqué en quelques minutes", "description": "Brèves explications sur le fonctionnement de chaque fonctionnalité"}, "slides": {"newHistoryFeature": "Découvrez la nouvelle fonctionnalité HISTORIQUE de Biela !", "newHistoryFeatureDescription": "L’onglet HISTORIQUE vous permet de garder le contrôle total en revisitant et restaurant n’importe quelle version précédente de votre projet, pour un codage fluide et sans stress.", "fixPreview": "Comment corriger l’aperçu sur Biela !", "fixPreviewDescription": "Regardez le guide complet et assurez-vous que tout s’affiche exactement comme vous l’avez conçu.", "signUp": "Comment s’inscrire sur Biela !", "signUpDescription": "Nouveau sur biela.dev ? Regardez ce guide rapide pour apprendre à vous inscrire et vérifier votre compte.", "buildWebsite": "Comment créer un site web avec Biela !", "buildWebsiteDescription": "Apprenez à créer et lancer un site web à partir de zéro avec biela.dev — sans écrire une seule ligne de code.", "downloadAndImport": "Comment télécharger et importer des projets et discussions sur Biela !", "downloadAndImportDescription": "Dans ce tutoriel pas à pas, vous apprendrez à télécharger vos projets terminés et à importer des discussions enregistrées dans votre espace de travail — pour reprendre là où vous vous êtes arrêté(e) et continuer à coder avec style à tout moment.", "shareProject": "Comment partager votre projet Vibe Code BIELA !", "shareProjectDescription": "Pr<PERSON>t(e) à partager votre projet comme un pro ? Regardez le guide complet et lancez-vous.", "launchProject": "Comment lancer votre projet sur votre propre domaine !", "launchProjectDescription": "Dans cette vidéo, nous parcourons les étapes simples pour mettre votre site en ligne — de la configuration à la publication finale, tout ce dont vous avez besoin pour passer de la création au lancement en toute confiance.", "deployProject": "Comment déployer votre projet et le mettre en ligne !", "deployProjectDescription": "Ce guide rapide vous accompagne à chaque étape pour déployer votre site, application ou tableau de bord — rapidement et sans accroc."}, "maintenance": {"title": "Nous travaillons sur la résolution du problème", "description": "Biela.dev est en mode maintenance. Nous travaillons pour résoudre le problème", "text": "Merci pour votre patience."}, "planCard": {"descriptionBeginner": "Parfait pour les débutants qui créent leurs premiers projets et apprennent le développement web", "descriptionCreator": "Idéal pour les créateurs qui ont besoin de plus de puissance et de flexibilité pour des projets avancés", "descriptionProfessional": "Solution complète pour les développeurs professionnels et les équipes qui créent des applications", "perMillionTokens": "$ {{price}} / par million de tokens", "dedicatedCto": "CTO dédié - 24 heures", "tokens": "Tokens", "perMonth": "par mois", "perYear": "par an", "freeTokens": "{{ tokens }} Tokens gratuits", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>", "save": "ÉCONOMISEZ 10%"}, "faqLabel": "Questions Fréquemment Po<PERSON>ées", "faqHeading": "Découvrez nos questions les plus posées", "faqSubheading": "Commencez. Comprenez. Explorez", "whatAreTokensQuestion": "Que sont les tokens ?", "whatAreTokensAnswer1": "Les tokens représentent la manière dont l'IA traite votre projet. Sur Biela.dev, les tokens sont utilisés lorsque vous envoyez une requête à l'IA. Vos fichiers de projet sont inclus dans le message afin que l'IA puisse comprendre votre code. Plus votre projet est volumineux et plus la réponse de l'IA est longue, plus vous aurez besoin de tokens par message.", "whatAreTokensAnswer2": "De plus, un petit nombre de tokens est déduit de votre compte pour chaque minute d'utilisation d'un conteneur web, afin de couvrir les coûts de l’environnement de développement.", "freePlanTokensQuestion": "Combien de tokens ai-je avec le plan gratuit ?", "freePlanTokensAnswer": "Chaque utilisateur du plan gratuit reçoit 200 000 tokens par jour, renouvelés quotidiennement.", "outOfTokensQuestion": "Que se passe-t-il si je n'ai plus de tokens ?", "outOfTokensAnswer": "Pas de souci ! Vous pouvez toujours aller dans Menu → Facturation pour acheter instantanément plus de tokens si nécessaire.", "changePlanQuestion": "Pui<PERSON>-je changer de plan plus tard ?", "changePlanAnswer": "<PERSON><PERSON> — à tout moment. Il suffit d’aller dans Menu → Facturation pour changer, mettre à niveau ou rétrograder selon vos besoins.", "rolloverQuestion": "Les tokens non utilisés sont-ils reportés ?", "rolloverAnswer": "Actuellement, les tokens expirent à leur date limite et ne sont pas reportés — alors profitez-en pleinement chaque jour !", "exploreHowToUseBiela": "Découvrez comment utiliser Biela", "new": "Nouveau", "joinVibeCoding": "Participez au Hackathon de Vibe Coding", "whatDoYouWantToBuild": "QUE VOULEZ-VOUS CONSTRUIRE ?", "imaginePromptCreate": "Imaginez. Demandez. Créez.", "levelOfAmbition": "Quel est votre niveau d'ambition ?", "startGrowScale": "Commencer. Développer. Passer à l'échelle.", "calloutBar": {"start": "Tous les forfaits commencent gratuitement avec", "tokens": "200 000 jetons par jour", "end": ". Aucune friction à l'inscription. Commencez à créer."}, "atPriceOf": "au prix de", "startFreeNoCreditCard": "Commencez gratuitement - Pas de carte bancaire requise", "SignIn": "Se connecter", "Register": "S'inscrire", "Affiliates": "Affiliés", "UsernameRequired": "Nom d'utilisateur requis", "PasswordRequired": "Mot de passe requis", "MissingUsernamePasswordOrTurnstile": "Nom d'utilisateur, mot de passe ou jeton <PERSON><PERSON><PERSON> manquant", "LoginSuccess": "Connexion réussie !", "LoginFailed": "Échec de la connexion", "EmailRequired": "Adresse e-mail requise", "EmailInvalid": "Veuillez entrer une adresse e-mail valide", "CaptchaRequired": "Veuillez compléter le CAPTCHA", "ResetLinkSent": "Un lien de réinitialisation a été envoyé à votre e-mail.", "ResetFailed": "Échec de l'envoi du lien de réinitialisation", "BackToLogin": "Retour à la connexion", "EmailPlaceholder": "Votre e-mail", "SendResetLink": "Envoyer le lien de réinitialisation", "loading": "Chargement", "checkingYourAuthenticity": "Vérification de votre authenticité", "ToUseBielaDev": "Pour utiliser Biela.dev, vous devez vous connecter à un compte existant ou en créer un avec l'une des options ci-dessous", "Sign in with Google": "Se connecter avec Google", "Sign in with GitHub": "Se connecter avec GitHub", "Sign in with Email and Password": "Se connecter avec un e-mail et un mot de passe", "DontHaveAnAccount": "Vous n'avez pas de compte ?", "SignUp": "S'inscrire", "ByUsingBielaDev": "En utilisant Biela.dev, vous consentez à la collecte de données d'utilisation.", "EmailOrUsernamePlaceholder": "E-mail/Nom d'utilisateur", "passwordPlaceholder": "Mot de passe", "login.loading": "Chargement", "LoginInToProfile": "Connectez-vous à votre profil", "login.back": "Retour", "forgotPassword": "Mot de passe oublié ?", "PasswordsMismatch": "Les mots de passe ne correspondent pas.", "PhoneRequired": "Numéro de téléphone requis", "ConfirmPasswordRequired": "Veuillez confirmer votre mot de passe", "AcceptTermsRequired": "<PERSON><PERSON> <PERSON> accepter les Conditions d'utilisation et la Politique de confidentialité", "RegistrationFailed": "Échec de l'inscription", "EmailConfirmationSent": "Un e-mail de confirmation a é<PERSON> envoyé. Veuillez confirmer votre e-mail, puis vous connecter.", "RegistrationServerError": "Échec de l'inscription (le serveur a renvoyé false).", "SomethingWentWrong": "Une erreur s'est produite", "CheckEmailConfirmRegistration": "Vérifiez votre e-mail pour confirmer votre inscription", "EmailConfirmationSentText": "Nous vous avons envoyé un e-mail avec un lien de confirmation.", "LoginToProfile": "Connexion au profil", "username": "Nom d'utilisateur", "email": "E-mail", "PasswordPlaceholder": "Mot de passe", "ConfirmPasswordPlaceholder": "Confirmer le mot de passe", "AcceptTermsPrefix": "J'accepte les", "TermsOfService": "Conditions d'utilisation", "AndSeparator": "et", "PrivacyPolicy": "Politique de confidentialité", "CreateAccount": "<PERSON><PERSON><PERSON> un compte", "Back": "Retour", "SignInWithGoogle": "Se connecter avec Google", "SignInWithGitHub": "Se connecter avec GitHub", "SignInWithEmailAndPassword": "Se connecter avec un e-mail et un mot de passe", "translation": {"AIModel": "Modèle IA", "UpgradePlan": "un plan premium", "PremiumBadge": "PREMIUM", "Active": "Actif", "projectInfo.features": "Fonctionnalités", "Stats": "Statistiques", "Performance": "Performance", "Cost": "Coût", "UpgradeTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> en passant à ", "UpgradeTooltipSuffix": "forfait", "chat": "Cha<PERSON>", "code": "Code"}, "extendedThinkingTooltip": "Activez la réflexion approfondie pour des réponses plus pertinentes de l'IA", "extendedThinkingTooltipDisabled": "Désactivez la réflexion approfondie pour économiser des ressources", "AIModel": "Modèle IA", "UpgradePlan": "un plan premium", "PremiumBadge": "PREMIUM", "Active": "Actif", "projectInfo.features": "Fonctionnalités", "Stats": "Statistiques", "Performance": "Performance", "Cost": "Coût", "UpgradeTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> en passant à ", "UpgradeTooltipSuffix": "forfait", "HowBielaWorks": "Comment fonctionne BIELA", "WatchPromptLaunch": "Regarde. Écris. Lance.", "SearchVideos": "Rechercher dans la bibliothèque...", "NewReleased": "NOUVELLE SORTIE", "Playing": "Lecture en cours", "NoVideosFound": "<PERSON><PERSON>ne vidéo trouvée", "TryAdjustingYourSearchTerms": "Essayez de modifier vos termes de recherche", "feedback": {"title": {"issue": "Signaler un problème", "feature": "Demander une fonctionnalité"}, "description": {"issue": "Vous avez trouvé un bug ou vous rencontrez un problème ? Faites-le nous savoir et nous le corrigerons dès que possible.", "feature": "Vous avez une idée pour une nouvelle fonctionnalité ? Nous aimerions entendre vos suggestions pour améliorer notre plateforme."}, "success": {"title": "<PERSON><PERSON><PERSON> !", "issue": "Votre signalement a été envoyé avec succès.", "feature": "Votre demande de fonctionnalité a été envoyée avec succès."}, "form": {"fullName": {"label": "Nom complet", "placeholder": "Entrez votre nom complet"}, "email": {"label": "Adresse e-mail", "placeholder": "Entrez votre adresse e-mail"}, "suggestion": {"labelIssue": "Description du problème", "labelFeature": "Suggestion de fonctionnalité", "placeholderIssue": "D<PERSON><PERSON><PERSON>z le problème que vous rencontrez...", "placeholderFeature": "Décrivez la fonctionnalité que vous souhaitez voir..."}, "screenshots": {"label": "Captures d'écran (optionnel)", "note": "A<PERSON><PERSON>z des captures d'écran pour nous aider à mieux comprendre le problème", "drop": "Cliquez pour télécharger ou glissez-déposez ici", "hint": "P<PERSON>, JPG, JPEG jusqu'à 10 Mo chacun", "count": "{{count}} image{{count > 1 ? 's' : ''}} sélectionnée(s)"}}, "buttons": {"cancel": "Annuler", "submitting": "Envoi en cours...", "submitIssue": "Envoyer le problème", "submitFeature": "Envoyer la demande"}, "errors": {"fullNameRequired": "Le nom complet est requis", "fullNameNoNumbers": "Le nom complet ne peut pas contenir de chiffres", "emailRequired": "L'adresse e-mail est requise", "emailInvalid": "Veuillez entrer une adresse e-mail valide", "suggestionRequired": "Veuillez décrire votre problème ou suggestion"}}, "changelog": {"title": "Journal des modifications", "description": "Toujours. En évolution. En avant.", "TotalReleases": "Nombre total de versions", "ActiveUsers": "Utilisateurs actifs", "FeaturesAdded": "Fonctionnalités ajoutées", "BugsFixed": "<PERSON> corrigés", "shortCallText": "Vous avez des suggestions ou avez trouvé un bug ? Nous serions ravis de vous entendre !", "reportIssue": "Signaler un problème", "requestFeature": "Demander une fonctionnalité", "totalReleases": "Total des Versions", "activeUsers": "Utilisateurs Actifs", "featuresAdded": "Fonctionnalités Ajoutées", "bugsFixed": "<PERSON>", "types": {"feature": "Fonctionnalité", "improvement": "Amélioration", "bugfix": "Correction de Bug", "ui/ux": "UI/UX", "announcement": "<PERSON><PERSON><PERSON>", "general": "Général"}, "versions": {"v3.2.3": {"date": "26 juin 2025", "changes": {"0": "Correction de quelques problèmes UI/UX sur le tableau de bord utilisateur.", "1": "Amélioration de la stabilité du WebContainer.", "2": "Amélioration du Mode AI Diff pour traiter les petits fichiers différemment des gros fichiers."}}, "v3.2.2": {"date": "24 juin 2025", "changes": {"0": "Réorganisation et refonte du frontoffice pour une meilleure expérience utilisateur.", "1": "Implémentation de cette formidable page de journal des modifications.", "2": "Amélioration de l'algorithme de reconnexion du WebContainer.", "3": "Ajout du support pour télécharger des projets via des fichiers ZIP, en plus des téléchargements de dossiers.", "4": "Amélioration des performances de tous les appels API en tirant parti d'une meilleure indexation."}}, "v3.2.1": {"date": "24 juin 2025", "changes": {"0": "IDE : La section \"Dossiers\" du tableau de bord utilisateur a été repensée pour une expérience améliorée.", "1": "Content Studio : <PERSON><PERSON> pouvez maintenant copier des images dans le Content Studio."}}, "v3.2.0": {"date": "17 juin 2025", "changes": {"0": "Mode AI Diff (Expérimental) : L'IA n'écrit maintenant que les différences entre le fichier original et la version mise à jour, plutôt que de régénérer tout le fichier. Cela améliore considérablement les performances et optimise l'utilisation des tokens. Vous pouvez activer ou désactiver cette fonctionnalité expérimentale depuis la zone Paramètres → Centre de Contrôle de votre projet."}}, "v3.1.6": {"date": "12 juin 2025", "changes": {"0": "IDE : Un champ de code de coupon a été ajouté pour les abonnements et tokens supplémentaires."}}, "v3.1.5": {"date": "11 juin 2025", "changes": {"0": "IDE : Diverses corrections ont été appliquées au flux \"Partager un Projet\"."}}, "v3.1.4": {"date": "10 juin 2025", "changes": {"0": "IDE : Les abonnements de packages ont été revérifiés et corrigés, traitant les problèmes d'inclusion crypto et d'implémentation Stripe."}}, "v3.1.3": {"date": "9 juin 2025", "changes": {"0": "IA : Implémentation et test de l'intégration directe d'API pour Gemini afin d'obtenir une meilleure latence."}}, "v3.1.2": {"date": "6 juin 2025", "changes": {"0": "Affilié : Implémentation des Tâches de Paiement d'Affiliés avec cohérence de design avec le modal d'information du nouveau tableau de bord (historique des paiements).", "1": "Content Studio : Une nouvelle option a été ajoutée pour que les utilisateurs décident s'ils envoient des images à l'IA avec des liens. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, cela est réglé sur \"non\"."}}, "v3.1.1": {"date": "5 juin 2025", "changes": {"0": "Content Studio : Des mises à jour ont été effectuées pour organiser le Content Studio par projet."}}, "v3.1.0": {"date": "3 juin 2025", "changes": {"0": "🚀 Une mise à jour majeure de BIELA.dev vient d'arriver ! 🚀", "1": "L'IA écrit maintenant du code 5 fois plus vite que ce matin. <PERSON><PERSON>, vous avez bien lu. Nous avons suralimenté les performances de BIELA pour que vous puissiez passer de l'idée au code fonctionnel en un éclair.", "2": "Terminal : Maintenant encore mieux : En nous appuyant sur la refonte de ce matin, nous avons ajouté un polissage UI/UX supplémentaire pour un flux de codage plus fluide.", "3": "Améliorations de connexion Supabase : Une interface plus propre et intuitive rend la configuration et l'intégration sans effort.", "4": "Mise à jour de la gestion d'erreurs (Gestion automatique des erreurs) : Le signalement automatique d'erreurs a été annulé. Nous préparons un nouveau paramètre pour que vous puissiez choisir entre la gestion manuelle et assistée par IA.", "5": "Nouveau Centre de Contrôle (dans les Paramètres du Projet) et Interface de Test Unitaire : Première fonctionnalité : basculer le Terminal de Test Unitaire. Beaucoup plus de contrôles arrivent !", "6": "Logique de téléchargement améliorée : Les téléchargements attendent maintenant que votre code soit entièrement poussé, assurant des exportations complètes et précises à chaque fois."}}, "v3.0.0": {"date": "3 juin 2025", "changes": {"0": "🚀 Mise à jour BIELA.dev - Vient de recevoir un boost de puissance ! 🚀", "1": "UI Terminal rénovée : Un design plus élégant et propre qui rend le travail dans le terminal un plaisir.", "2": "Actions NPM en un clic : Exécutez npm install, npm run build, ou npm run dev instantanément avec de nouveaux boutons d'action, pas besoin de taper ! (la fonctionnalité est intégrée dans le terminal)", "3": "Gestion d'erreurs plus intelligente et classification des couleurs d'erreur : Les erreurs d'aperçu sont maintenant automatiquement envoyées à l'IA pour qu'elle puisse vous aider à déboguer plus rapidement. (vous ferez l'expérience d'une correction de bugs fluide par BIELA pendant que vous développez vos projets)", "4": "Navigation de fichiers améliorée : Cliquer sur un nom de fichier dans le chat l'ouvre maintenant directement dans le WebContainer. Il suffit de taper et d'éditer !"}}, "v2.1.1": {"date": "30 mai 2025", "changes": {"0": "IDE : Un bouton de sauvegarde a été ajouté pour le WebContainer, apparaissant lorsque des mises à jour manuelles sont effectuées, déclenchant un commit et push vers GitHub.", "1": "Tableau de bord : Correction d'une erreur qui se produisait lors du renommage de projets importés depuis le tableau de bord utilisateur.", "2": "WebContainer : Une correction urgente pour une fuite mémoire a été implémentée."}}, "v2.1.0": {"date": "28 mai 2025", "changes": {"0": "🚀 Mise à jour BIELA.dev — 7 nouvelles améliorations viennent d'arriver ! 🚀", "1": "IDE : Nous avons ajouté la possibilité de lier votre propre domaine à votre projet biela, directement depuis l'interface.", "2": "Aperçus de projets déployés : Vous pouvez maintenant prévisualiser vos projets déployés d'un seul clic sur l'icône œil, pas besoin d'ouvrir chacun pour savoir ce qu'il y a dedans.", "3": "Suivi des coûts corrigé dans les projets dupliqués : Nous avons corrigé un problème où les projets dupliqués héritaient des estimations de coûts du projet original. Maintenant ils commencent à zéro, vous donnant un suivi précis par projet.", "4": "Estimation des coûts en direct (par prompt) : Les estimations de coûts se mettent maintenant à jour après chaque prompt, pas seulement une fois par jour. Ce<PERSON> vous donne un retour en temps réel sur ce qu'il en coûterait de développer la même chose avec une agence de dev, et combien vous économisez avec BIELA.", "5": "Onglet Historique et Retour en arrière corrigés : V<PERSON> pouvez maintenant revenir en arrière en toute sécurité dans l'historique du projet. Après nos récentes mises à jour majeures, cela avait des problèmes, maintenant c'est ultra fluide.", "6": "Améliorations du défilement automatique : Plus d'écrans figés pendant le codage ! Nous avons corrigé le problème où le défilement était en retard pendant la génération. Profitez de flux fluides.", "7": "Les pages de menu s'ouvrent maintenant dans de nouveaux onglets : Si vous codez dans Labs et cliquez sur d'autres éléments de menu, ils s'ouvrent maintenant dans un nouvel onglet pour que vous ne perdiez jamais votre travail en cours."}}, "v2.0.4": {"date": "27 mai 2025", "changes": {"0": "Tableau de bord : Les améliorations de captures d'écran sur le tableau de bord utilisateur incluent la suppression du défilement automatique, permettant un défilement contrôlé par l'utilisateur et ajoutant une animation pour charger les captures d'écran."}}, "v2.0.3": {"date": "20 mai 2025", "changes": {"0": "Content Studio : L'animation du robot dans Content Studio s'affiche maintenant en taille réelle.", "1": "Affilié : Correction des problèmes d'affichage sur le Tableau de bord Affilié lors de la visualisation dans Firefox."}}, "v2.0.2": {"date": "19 mai 2025", "changes": {"0": "Content Studio : Le nombre maximum de fichiers autorisés à la fois dans Content Studio a été augmenté à 50 (contre 10)."}}, "v2.0.1": {"date": "18 mai 2025", "changes": {"0": "Content Studio : Correction d'un problème où les tags pour plusieurs fichiers ne se mettaient pas à jour sauf si la page était rafraîchie.", "1": "Content Studio : Le bouton d'édition lors de la sélection d'une image est maintenant plus visible.", "2": "Content Studio : Lors du téléchargement, l'emplacement du dossier sélectionne maintenant automatiquement le dernier dossier créé ou le dernier sélectionné.", "3": "Content Studio : Le dossier sélectionné est maintenant conservé lors du glisser-déposer d'images.", "4": "Content Studio : Le titre de l'onglet utilise maintenant une nouvelle couleur (au lieu du vert).", "5": "Content Studio : Le bouton \"insérer image\" a été renommé en \"utiliser dans le projet\".", "6": "Content Studio : Le bouton de téléchargement de fichiers a maintenant un arrière-plan vert.", "7": "Content Studio : La hauteur de la barre de recherche a été diminuée pour un meilleur alignement.", "8": "Content Studio : La barre de recherche n'est plus affichée s'il n'y a pas d'images pour l'utilisateur."}}, "v2.0.0": {"date": "16 mai 2025", "changes": {"0": "🚀 MISES À JOUR MAJEURES CHEZ BIELA.DEV ! 🚀", "1": "Notre propre technologie de conteneur web : Nous avons construit notre propre technologie de conteneur web qui est complètement indépendante de tout fournisseur externe ! Cela nous donne une flexibilité sans précédent pour développer de nouvelles fonctionnalités plus rapidement et mieux que jamais.", "2": "Options LLM multiples pour la génération de code : Nous supportons maintenant plusieurs modèles d'IA pour la génération de code au-delà d'Anthropic ! Vous pouvez maintenant utiliser Gemini de Google pour vos projets de codage, ce qui apporte des avantages majeurs. Gemini offre une fenêtre de contexte massive de 1 000 000 de tokens !", "3": "Content Studio : Vos photos, vos projets : Exprimez-vous pleinement avec notre nouveau Content Studio ! Maintenant vous pouvez télécharger vos propres photos à utiliser dans vos projets Vibe Coding.", "4": "Partage de projets : La collaboration vient de s'améliorer ! Maintenant vous pouvez partager vos projets avec d'autres utilisateurs.", "5": "Connexion aux projets Supabase existants : Cette nouvelle fonctionnalité puissante vous permet de connecter plusieurs projets Biela.dev à la même base de données Supabase."}}, "v1.0.5": {"date": "15 mai 2025", "changes": {"0": "Paiements : Le traitement des paiements Stripe a été implémenté.", "1": "Content Studio : Une animation (comme le robot) est maintenant affichée quand il n'y a pas d'image sur la première page."}}, "v1.0.4": {"date": "13 mai 2025", "changes": {"0": "Content Studio : Correction des problèmes de pagination dans Content Studio."}}, "v1.0.3": {"date": "5 mai 2025", "changes": {"0": "Lancement de Content Studio - un endroit pour stocker et gérer tous vos médias.", "1": "Frontoffice : Implémentation de schema.org pour les applications logicielles sur le biela_frontoffice.", "2": "Tableau de bord d'équipe : Des notifications ont été ajoutées au tableau de bord d'équipe (ex. un point rouge quand un nouveau membre rejoint ou qu'un nouveau message apparaît dans le chat).", "3": "IDE : Correction d'un bug où le modal du menu ne se fermait pas correctement en cliquant dans le WebContainer dans certaines zones lors de la saisie d'un prompt."}}, "v1.0.2": {"date": "2 mai 2025", "changes": {"0": "Affilié : Implémentation de la refonte de la zone des membres d'équipe.", "1": "Affilié : Les icônes du Tableau de bord Affilié ont été mises à jour.", "2": "IDE : Le texte du bouton \"Connexion Supabase\" a été changé en \"Base de données\" avec une nouvelle icône, et affiche maintenant \"Base de données connectée\" en bleu quand connecté."}}, "v1.0.1": {"date": "1er mai 2025", "changes": {"0": "IDE (Onglet Paramètres) : Implémentation de l'intégration de base de données sur le chat des paramètres.", "1": "Tableau de bord : Correction de la pagination sur le tableau de bord utilisateur."}}, "v1.0.0": {"date": "18 avril 2025", "changes": {"0": "🚀 Bienvenue sur Biela.dev : Votre compagnon de codage alimenté par l'IA, gratuit pour tous ! 🚀", "1": "Nous sommes ravis d'annoncer la version publique initiale de Biela.dev ! Nous croyons en rendant le développement web assisté par IA de pointe accessible à tous, c'est pourquoi notre plateforme est complètement gratuite dès le premier jour."}}}}}