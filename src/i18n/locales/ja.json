{"meta": {"home": {"title": "biela.dev | AI駆動型ウェブ＆アプリビルダー – プロンプトで構築", "description": "biela.devであなたのアイデアを実際のウェブサイトやアプリに変えましょう。AI駆動のプロンプトを使用して、手間なくカスタムデジタル製品を作成できます"}, "privacy": {"title": "biela.devプライバシーポリシー", "description": "biela.devがどのようにあなたの個人情報を収集、使用、保護しているかを理解しましょう。"}, "terms": {"title": "biela.dev利用規約", "description": "biela.devのAI駆動型開発プラットフォームを使用するための利用規約と条件を確認してください"}, "changelog": {"title": "biela.dev チェンジログ - 常に。進化する。前進。", "description": "biela.devがどのように各リリースで進化しているかを理解しましょう"}, "competitionterms": {"title": "Biela開発コンペティション利用規約", "description": "Biela.devおよびTeachMeCode Instituteが主催する1か月間の公式開発コンペに参加しよう。あなたのデジタル作品を披露し、コミュニティの支持で評価され、上位入賞を目指そう。応募締切は2025年5月14日。"}, "contest": {"title": "biela.dev開発コンペティション2025 – 優勝者発表", "description": "biela.devコンテストの優勝者をご覧ください。最高の栄誉を獲得した素晴らしいAI構築プロジェクトを発見しましょう"}, "howTo": {"title": "Biela.devの使い方 – AI駆動型アプリケーション構築の完全ガイド", "description": "Biela.devを使用して、コーディングなしで迅速にウェブアプリを構築する方法を学びましょう。チュートリアルや実践的なヒントを含む完全なステップバイステップガイドです。", "keywords": "biela, biela.dev, bielaの使い方, AIアプリ作成, ノーコード, 高速開発, bielaガイド, bielaチュートリアル", "ogTitle": "Biela.devの使い方 – ステップバイステップガイド", "ogDescription": "Biela.devでAIアプリを構築する方法を学びましょう。アイデアから公開まで、コーディング不要です。", "twitterTitle": "Biela.devの使い方 – 完全ガイド", "twitterDescription": "Biela.devでノーコードAIアプリを構築。簡単なステップバイステップガイドをチェック！"}}, "navbar": {"home": "ホーム", "demo": "デモ", "community": "コミュニティ", "affiliate": "アフィリエイト", "partners": "パートナー", "contest": "バイブコーディングハッカソン", "subscriptions": "サブスクリプション", "pricing": "料金"}, "counter": {"days": "日", "hours": "時間", "minutes": "分", "seconds": "秒", "centisec": "センチ秒"}, "hero": {"title": "あなたのアイデアを数分でライブウェブサイトやアプリに変えよう", "subtitle": "もしあなたが<1>想像</1>できるなら、<5>コード化</5>もできる。", "subtitle2": "無料で始める。すべてをコーディングする。すべての要求であなたのスキルをチャンスに変える。", "timeRunningOut": "時間が迫っています！", "launchDate": "バージョン WoodSnake", "experimentalVersion": "ベータ版リリース: 2025年4月15日", "earlyAdopter": "正式リリース前に早期採用者特典を確保するため、今すぐ参加しよう", "tryFree": "無料で試す", "seeAiAction": "AIの動作を見る", "tellBiela": "Bielaに作成を依頼する", "inputPlaceholder": "想像できれば、BIELA はそれをコードにできます。今日は何をしましょうか？", "chat": "チャット", "code": "コード", "checklist": "チェックリスト", "checklistTitle": "チェックリストを使って", "checklistItems": {"trackDevelopment": "開発タスクを追跡する", "setRequirements": "プロジェクト要件を設定する", "createTesting": "テストプロトコルを作成する"}, "registerNow": "今すぐ登録", "attachFiles": "プロンプトにファイルを添付する", "voiceInput": "音声入力を使用する", "enhancePrompt": "プロンプトを強化する", "cleanupProject": "プロジェクトを整理する", "suggestions": {"weatherDashboard": "天気ダッシュボードを作成する", "ecommercePlatform": "Eコマースプラットフォームを構築する", "socialMediaApp": "ソーシャルメディアアプリをデザインする", "portfolioWebsite": "ポートフォリオサイトを生成する", "taskManagementApp": "タスク管理アプリを作成する", "fitnessTracker": "フィットネストラッカーを構築する", "recipeSharingPlatform": "レシピ共有プラットフォームをデザインする", "travelBookingSite": "旅行予約サイトを作成する", "learningPlatform": "学習プラットフォームを構築する", "musicStreamingApp": "音楽ストリーミングアプリをデザインする", "realEstateListing": "不動産リストを作成する", "jobBoard": "求人掲示板を構築する"}}, "videoGallery": {"barber": {"title": "理髪店のウェブサイト", "description": "Biela.devが数分で理髪店の完全なウェブサイトを作成する様子を見てみよう"}, "tictactoe": {"title": "三目並べゲーム", "description": "AIが『三目並べゲーム』と言うよりも速く三目並べを作成するのを見てみよう"}, "coffeeShop": {"title": "コーヒーショップのウェブサイト", "description": "Biela.devがコーヒーショップのウェブサイトを作成する様子を見てみよう"}, "electronicsStore": {"title": "電子機器ストアのウェブサイト", "description": "Biela.devが数分で完全なオンラインストアを作成する様子を見てみよう"}, "seoAgency": {"title": "SEOエージェンシーのウェブサイト", "description": "AIがSEOエージェンシーのウェブサイトを作成するのを見てみよう"}}, "telegram": {"title": "私たちのTelegramに参加", "subtitle": "Bielaコミュニティと繋がろう", "description": "即時サポートを受け、あなたのプロジェクトを共有し、世界中のAI愛好家とつながりましょう。<br/><br/>私たちのTelegramコミュニティは、Biela.devの最新情報を手に入れる最速の方法です。", "stats": {"members": "メンバー", "support": "サポート", "countries": "国", "projects": "プロジェクト"}, "joinButton": "Telegramコミュニティに参加する", "communityTitle": "Biela.devコミュニティ", "membersCount": "メンバー", "onlineCount": "現在オンライン", "messages": {"1": "Biela.devを使って1時間以内に私のeコマースサイトを立ち上げたよ！", "2": "すごいね！どうやって商品カタログを管理したの？", "3": "Bielaが自動で処理してくれたんだ！僕はただ要望を伝えただけ。", "4": "今、ポートフォリオサイトを作っているところだよ。AIの提案は本当に素晴らしい！", "5": "新しいレスポンシブテンプレートを試した人はいる？モバイルで見たら素晴らしいよ。"}}, "joinNetwork": {"title": "私たちのネットワークに参加しよう", "subtitle": "つながる。成長する。稼ぐ。", "affiliateProgram": "アフィリエイトプログラム", "registerBring": "登録して3人のアフィリエイトを招待", "aiCourse": "AIエンジニアリングコース", "courseDescription": "TeachMeCodeとの無料で包括的なAIコース", "digitalBook": "デジタルブック", "bookDescription": "\"The Art of Prompt Engineering\" 独占デジタル版", "exclusiveBenefits": "限定特典", "benefitsDescription": "生涯の収益と特別なメンバー特典", "registerNow": "今すぐ登録", "teamEarnings": "チーム収益", "buildNetwork": "ネットワークを構築する", "weeklyMentorship": "毎週のメンタリング", "teamBonuses": "チームボーナス", "startEarning": "今日から稼ぎ始めよう", "upToCommission": "最大でコミッション"}, "partners": {"title": "業界のリーダーに信頼されて", "subtitle": "グローバルなイノベーターと協力して、AI開発の未来を形作る", "strategicPartnerships": "戦略的パートナーシップ", "joinNetwork": "BIELAで開発を変革するパートナーと企業のネットワークに参加しよう"}, "demo": {"title": "AIがリアルタイムでアプリとウェブサイトを構築する様子を見る", "subtitle": "Biela.devで企業がどのようにデジタルプレゼンスを変革しているかを体験しよう", "seeInAction": "Biela.devの動作を見る", "whatKind": "どのタイプのウェブサイトが必要ですか？", "describeWebsite": "ウェブサイトのアイデアを説明してください（例：「フォトグラファーのためのポートフォリオ」）", "generatePreview": "プレビューを生成する", "nextDemo": "次のデモ", "startBuilding": "今日からBiela.devで構築し、稼ごう"}, "footer": {"quickLinks": {"title": "クイックリンク", "register": "登録", "bielaInAction": "Biela.devの動作を見る", "liveOnTelegram": "Telegramでライブ中", "affiliate": "共同アフィリエイトマーケティング", "partners": "パートナー"}, "legal": {"title": "法的情報", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. 全著作権所有.", "unauthorized": "明示的な書面による許可なしに、本サービスの全部または一部の不正使用、複製、配布を固く禁じます。"}, "reservations": "TeachMeCode Instituteは、Bielaに関連する知的財産権に関してすべての法的権利を留保します。"}, "bottomBar": {"left": "2025 Biela.dev. %s提供 © 全著作権所有.", "allRights": "全著作権所有", "right": "開発者：%s"}}, "common": {"loading": "読み込み中...", "error": "エラーが発生しました", "next": "次へ", "previous": "前へ", "submit": "送信", "cancel": "キャンセル", "close": "閉じる", "loginNow": "今すぐログイン", "verifyAccount": "アカウントを確認"}, "languageSwitcher": {"language": "言語"}, "contest": {"bielaDevelopment": "バイブコーディングハッカソン", "competition": "コンテスト", "firstPlace": "第1位", "secondPlace": "第2位", "thirdPlace": "第3位", "currentLeader": "現在のリーダー：", "footerText": "Biela.devによるAI開発、TeachMeCode®研究所の支援", "browseHackathonSubmissions": "ハッカソンの提出物を閲覧する", "winnersAnnouncement": "🎉 コンテスト優勝者発表！ 🎉", "congratulationsMessage": "素晴らしい優勝者の皆様、おめでとうございます！この素晴らしいハッカソンに参加してくださった皆様、ありがとうございました。", "conditionActiveReferrals": "3つのアクティブな紹介がある", "conditionLikedProject": "他のプロジェクトにいいねした", "winner": "優勝者", "qualificationsMet": "資格:", "noQualifiedParticipant": "資格のある参加者なし", "requirementsNotMet": "要件が満たされていません", "noQualificationDescription": "この順位の資格要件を満たした参加者はいませんでした。", "qualifiedWinners": "🏆 資格のある優勝者", "qualifiedWinnersMessage": "すべての資格要件を満たした参加者の皆様、おめでとうございます！", "noTopPrizesQualified": "上位賞に資格のある参加者なし", "noTopPrizesMessage": "残念ながら、最初の3つの賞の順位の資格要件を満たした参加者はいませんでした。資格のあるすべての参加者は、残りの賞金プールから賞を受け取ります。", "noTopPrizesMessageShort": "残念ながら、最初の3つの賞の順位の資格要件を満たした参加者はいませんでした。"}, "competition": {"header": {"mainTitle": "あなたのプロンプト。あなたのスタイル。あなたの遊び場。", "timelineTitle": "スケジュール", "timelineDesc": "AIが生成した最高のプロジェクトを作成するための1ヶ月", "eligibilityTitle": "参加資格", "eligibilityDesc": "Biela.devでプロジェクトを提出するアクティブなアカウント", "prizesTitle": "賞品", "prizesDesc": "16,000ドルの現金賞と受賞者への無料アクセス", "votingTitle": "投票", "votingDesc": "コミュニティ主導で、認証ユーザーにつき1票", "tagline": "あなたの創造性を発信しよう。世界に投票させよう。"}, "details": {"howToEnter": "参加方法", "enter": {"step1": {"title": "Biela.devのAIを使用してプロジェクトを作成する", "desc": "B<PERSON><PERSON>の強力なAIツールを活用して、あなたの革新的なプロジェクトを構築しよう"}, "step2": {"title": "ユーザーダッシュボードにアクセスする", "desc": "プロジェクトの横にある「ハッカソン用に公開」をクリックしてください", "subdesc": "最大で3つの異なるプロジェクトを提出できます"}, "step3": {"title": "Bielaがあなたのエントリーを処理します：", "list1": "プロジェクトのスクリーンショットを撮る", "list2": "エントリーが参加資格を満たしているか確認する", "list3": "概要と説明を生成する"}}, "prizeStructure": "賞品の構成", "prizes": {"firstPlace": "第1位", "firstPlaceValue": "$10,000", "secondPlace": "第2位", "secondPlaceValue": "$5,000", "thirdPlace": "第3位", "thirdPlaceValue": "$1,000", "fourthToTenth": "第4位から第10位", "fourthToTenthValue": "30日間の無制限アクセス"}}, "qualification": {"heading": "資格要件", "description": "トップ3の賞（金額：$10,000、$5,000、$1,000）に応募するには、以下の条件を満たす必要があります：", "criteria1": "少なくとも3件のアクティブな推薦が必要です", "criteria2": "展示壁のプロジェクトに少なくとも1回「いいね」をしてください", "proTipLabel": "プロのヒント：", "proTipDesc": "プロジェクトを早く提出すればするほど、注目と投票が増えます！"}, "voting": {"heading": "投票ガイドライン", "oneVotePolicyTitle": "一人一票のルール", "oneVotePolicyDesc": "自分のプロジェクトには投票できず、各ユーザーは一度しか投票できません", "accountVerificationTitle": "アカウント認証", "accountVerificationDesc": "Biela.devの認証済みアカウントのみが投票可能です", "tieBreakingTitle": "同点時の決定方法", "tieBreakingDesc": "同点の場合は、プロジェクト作成者が獲得した総スター数（使用済み・未使用を問わず）が決定要因となります", "howToVoteTitle": "投票方法", "howToVoteDesc": "展示壁を見て、公開されたすべてのAI生成プロジェクトを確認し、ハートアイコンをクリックしてお気に入りのプロジェクトに投票してください！"}}, "contestItems": {"projectShowcase": "プロジェクト展示", "loading": "読み込み中...", "createBy": "作成者: ", "viewProject": "プロジェクトを見る", "stars": "スター", "overview": "概要", "keyFeatures": "主な特徴", "exploreLiveProject": "ライブプロジェクトを探索する", "by": "による", "likeLimitReached": "いいねの上限に達しました", "youCanLikeMaximumOneProject": "最大で1つのプロジェクトにいいねできます。現在のいいねを解除して、代わりにこのプロジェクトにいいねしますか？", "currentlyLikedProject": "現在いいねしているプロジェクト", "switchMyLike": "いいねを切り替える", "mustLoggedIntoLikeProject": "プロジェクトにいいねするにはログインが必要です。", "signInToBielaAccountToVote": "投票に参加するには、Biela.devアカウントにサインインしてください。", "yourAccountIsNotVerifiedYet": "アカウントがまだ認証されていません。プロジェクトにいいねするには、アカウントを認証してください。", "accountVerificationEnsureFairVoting": "アカウント認証は公正な投票を保証し、競争の公平性を維持するのに役立ちます。", "projectsSubmitted": "提出されたプロジェクト：", "unlikeThisProject": "このプロジェクトのいいねを取り消す", "likeThisProject": "このプロジェクトが好き", "likes": "いいね", "like": "いいね", "somethingWentWrong": "問題が発生しました。後でもう一度お試しください", "voteError": "投票エラー"}, "textsLiveYoutube": {"first": {"title": "ご招待します！", "description": "今週の金曜日にライブ配信します — このセッションでは<green>目的</green>、<blue>精度</blue>、<orange>純粋なバイブ</orange>で構築する方法を扱います。", "secondDescription": "バイブコーディングを<green>マスターする</green>方法、<blue>新機能を発見する</blue>方法、そして<orange>実際のプロジェクトがライブで構築される</orange>のを見る方法を学びましょう。", "specialText": "ここをクリックして YouTube の「通知を受け取る」を押してください — お見逃しなく。"}, "second": {"title": "一番乗りでビルドしよう。", "description": "今週の金曜日、<green>バイブコーディング</green>を全く新しいレベルに引き上げます — <blue>意図を持って作成する</blue>方法、<orange>新機能を解放する</orange>方法、そして<green>これまで以上に速く</green>ローンチする方法をお見せします。", "secondDescription": "<green>新しいビルド</green>、<blue>新しいアイデア</blue>、<orange>新しい機会</orange>。", "specialText": "ここをクリックして YouTube のリマインダーを設定 — 次世代クリエイターの波に乗り遅れないでください。"}, "third": {"title": "席を確保しよう。", "description": "次回の<green>Biela.devライブストリーム</green>は今週の金曜日に行われます — あなたは<blue>正式に招待されています</blue>。", "secondDescription": "私たちは<green>バイブコーディング</green>を深く掘り下げ、<blue>強力な新機能</blue>を公開し、あなたが<orange>よりスマートに、より速く、より大規模に</orange>構築できるよう支援します。", "specialText": "ここをクリックして YouTube の「通知を受け取る」をタップ — あなたの次の大きなアイデアはここから始まるかもしれません。"}}, "stream": {"title": "<1>最初</1>に<3>構築</3>する。", "subtitle": "金曜日のライブ配信に参加しましょう — 機能、プロジェクト、独占アップデート。", "button": "YouTubeリマインダーを設定する。"}, "billings": {"modalTitle": "プランを選択", "modalSubtitle": "制限なくコーディングを続けるために、3つの柔軟なプランから選択してください。", "toggleMonthly": "月額", "toggleYearly": "年額（10％お得）", "mostPopular": "人気 No.1", "tokensLowerCase": "トークン", "tokensUpperCase": "トークン", "below": "以下", "unlimited": "無制限", "10Bonus": "10％ボーナス", "currentPlan": "現在のプラン", "upgradePlan": "プランをアップグレード", "purchaseDetails": "購入詳細", "dateLabel": "日付", "planLabel": "プラン", "amountLabel": "金額", "whatsNext": "次のステップ", "yourTokensAvailable": "トークンがアカウントに追加されました", "purchaseDetailsStored": "購入情報はアカウントに保存されました", "viewPurchaseHistory": "請求セクションで購入履歴を確認できます", "thankYou": "ありがとうございます！", "purchaseSuccessful": "購入が完了しました", "startCoding": "コーディングを始める", "month": "月", "year": "年"}, "feature": {"basic_ai": "基本的なAI開発", "community_support": "コミュニティサポート", "standard_response": "標準応答時間", "basic_templates": "基本テンプレート", "advanced_ai": "高度なAI開発", "priority_support": "優先サポート", "fast_response": "迅速な応答時間", "premium_templates": "プレミアムテンプレート", "team_collaboration": "チームコラボレーション", "enterprise_ai": "エンタープライズAI開発", "support_24_7": "24時間365日優先サポート", "instant_response": "即時応答時間", "custom_templates": "カスタムテンプレート", "advanced_team": "高度なチーム機能", "custom_integrations": "カスタム統合", "big_context_window": "大きなコンテキストウィンドウ - 中程度の5倍のサイズ", "code_chat_ai": "コード＆チャットAIへのアクセス", "tokens_basic": "約3つのプレゼンテーション用ウェブサイト分のトークン", "standard_webcontainer": "専用の標準Webコンテナ", "medium_context_window": "中程度のコンテキストウィンドウ - ウェブサイト、ブログ、または基本的なブラウザゲームに適合", "basic_support": "標準応答時間の基本サポート", "supabase_integration": "Supabase統合", "project_sharing": "プロジェクトを友達と共有", "project_download": "プロジェクトのダウンロード", "project_deployment": "デプロイ機能", "custom_domain": "プロジェクトを独自ドメインに接続", "tokens_creator_plus": "約7つのプレゼンテーション用ウェブサイト分のトークン", "advanced_support": "より速い応答時間の高度なサポート", "prioritary_support": "専用の優先サポート", "early_feature_access": "新機能の早期アクセス", "human_cto": "ビジネス専用の人間CTO", "community_promotion": "コミュニティでビジネスをプロモート"}, "JoinUsLive": "ライブで参加する", "howToUseBiela": {"subtitle": "Bielaの使い方", "title": "数分で解説", "description": "各機能の仕組みを簡単に説明します"}, "slides": {"newHistoryFeature": "Biela の新しい履歴機能を発見！", "newHistoryFeatureDescription": "履歴タブを使えば、プロジェクトの過去のバージョンを簡単に確認・復元でき、Vibe コーディングの流れを常にコントロールできます！", "fixPreview": "Biela のプレビューを修正する方法！", "fixPreviewDescription": "完全なガイドを見て、すべてが意図した通りに表示されることを確認しましょう。", "signUp": "Biela にサインアップする方法！", "signUpDescription": "biela.dev を初めて使う方はこちらのクイックガイドで登録とアカウントの確認方法を学びましょう！", "buildWebsite": "Biela を使ってウェブサイトを作成する方法！", "buildWebsiteDescription": "biela.dev を使ってゼロからウェブサイトを構築・公開する方法を学びましょう — コードを一行も書かずに。", "downloadAndImport": "Biela でプロジェクトやチャットをダウンロード・インポートする方法！", "downloadAndImportDescription": "このステップバイステップのガイドでは、完成したプロジェクトをダウンロードし、保存済みチャットをワークスペースにインポートする方法を学びます。途中からすぐに再開でき、いつでも Vibe コーディングを継続できます。", "shareProject": "BIELA プロジェクトを共有する方法！", "shareProjectDescription": "プロのようにプロジェクトを共有する準備はできましたか？完全なガイドを見て始めましょう。", "launchProject": "独自ドメインでプロジェクトを公開する方法！", "launchProjectDescription": "このビデオでは、サイトを公開するための簡単なステップを解説します。セットアップから公開まで、自信を持ってローンチできるすべての情報が得られます。", "deployProject": "プロジェクトをデプロイして公開する方法！", "deployProjectDescription": "このクイックガイドでは、ウェブサイト、アプリ、ダッシュボードを素早くスムーズにデプロイする方法をすべてのステップで案内します。"}, "maintenance": {"title": "問題を解決するために努力しています", "description": "Biela.devは現在メンテナンスモードです。問題を解決するために努力しています", "text": "ご理解いただきありがとうございます。"}, "planCard": {"descriptionBeginner": "初めてのプロジェクトを構築し、ウェブ開発を学ぶ初心者に最適です", "descriptionCreator": "高度なプロジェクトに必要なパワーと柔軟性を求めるクリエイターに最適です", "descriptionProfessional": "アプリケーションを構築するプロの開発者とチームのための完全なソリューション", "perMillionTokens": "$ {{price}} / 100万トークンあたり", "dedicatedCto": "専任CTO - 24時間対応", "tokens": "トークン", "perMonth": "月ごと", "perYear": "年ごと", "freeTokens": "{{ tokens }} 無料トークン", "monthly": "月額", "yearly": "年額", "save": "10％お得"}, "faqLabel": "よくある質問", "faqHeading": "よく寄せられる質問をご覧ください", "faqSubheading": "始める。理解する。探検する。", "whatAreTokensQuestion": "トークンとは何ですか？", "whatAreTokensAnswer1": "トークンは、AIがあなたのプロジェクトを処理するための単位です。Biela.devでは、プロンプトを送信する際にトークンが使われます。このとき、プロジェクトのファイルがメッセージに含まれ、AIがコードを理解できるようになります。プロジェクトが大きいほど、AIの応答が長いほど、1メッセージあたりに必要なトークン数が増えます。", "whatAreTokensAnswer2": "また、開発環境のコストをカバーするために、Webコンテナの利用ごとに1分あたり少量のトークンがアカウントから差し引かれます。", "freePlanTokensQuestion": "無料プランでは何トークンもらえますか？", "freePlanTokensAnswer": "無料プランのユーザーは毎日 200,000 トークンが付与され、毎日リセットされます。", "outOfTokensQuestion": "トークンがなくなったらどうなりますか？", "outOfTokensAnswer": "ご安心ください！メニュー → 請求 からいつでも追加トークンをすぐに購入できます。", "changePlanQuestion": "後からプランを変更できますか？", "changePlanAnswer": "はい — いつでも可能です。<1>メニュー → 請求</1> にアクセスして、ニーズに応じてプランをアップグレード、ダウングレード、または変更できます。", "rolloverQuestion": "未使用のトークンは繰り越されますか？", "rolloverAnswer": "現在、トークンは有効期限でリセットされ、繰り越しはされません — 毎日しっかり使い切りましょう！", "exploreHowToUseBiela": "B<PERSON>aの使い方を探る", "new": "新着", "joinVibeCoding": "Vibe Coding ハッカソンに参加しよう", "whatDoYouWantToBuild": "何を作りたいですか？", "imaginePromptCreate": "想像する。指示する。作成する。", "levelOfAmbition": "あなたの野心のレベルは？", "startGrowScale": "始める。成長する。拡大する。", "calloutBar": {"start": "すべてのプランは無料で開始できます", "tokens": "1日あたり200,000トークン", "end": "。サインアップ不要。今すぐ構築を始めましょう。"}, "atPriceOf": "価格", "startFreeNoCreditCard": "無料で始める - クレジットカード不要", "SignIn": "サインイン", "Register": "登録", "Affiliates": "アフィリエイト", "UsernameRequired": "ユーザー名は必須です", "PasswordRequired": "パスワードは必須です", "MissingUsernamePasswordOrTurnstile": "ユーザー名、パスワード、またはTurnstileトークンが見つかりません", "LoginSuccess": "ログインに成功しました！", "LoginFailed": "ログインに失敗しました", "EmailRequired": "メールアドレスは必須です", "EmailInvalid": "有効なメールアドレスを入力してください", "CaptchaRequired": "CAPTCHAを完了してください", "ResetLinkSent": "リセットリンクがメールに送信されました。", "ResetFailed": "リセットリンクの送信に失敗しました", "BackToLogin": "ログイン画面に戻る", "EmailPlaceholder": "あなたのメールアドレス", "SendResetLink": "リセットリンクを送信", "loading": "読み込み中", "checkingYourAuthenticity": "認証を確認中", "ToUseBielaDev": "Biela.devを使用するには、既存のアカウントにログインするか、以下のいずれかの方法でアカウントを作成する必要があります", "DontHaveAnAccount": "アカウントをお持ちでないですか？", "SignUp": "サインアップ", "ByUsingBielaDev": "Biela.devを使用することで、利用データの収集に同意したことになります。", "EmailOrUsernamePlaceholder": "メール/ユーザー名", "passwordPlaceholder": "パスワード", "login.loading": "読み込み中", "LoginInToProfile": "プロフィールにログイン", "login.back": "戻る", "forgotPassword": "パスワードをお忘れですか？", "PasswordsMismatch": "パスワードが一致しません。", "PhoneRequired": "電話番号は必須です", "ConfirmPasswordRequired": "パスワードの確認が必要です", "AcceptTermsRequired": "利用規約とプライバシーポリシーに同意する必要があります", "RegistrationFailed": "登録に失敗しました", "EmailConfirmationSent": "確認メールが送信されました。メールを確認してからログインしてください。", "RegistrationServerError": "登録に失敗しました（サーバーがfalseを返しました）。", "SomethingWentWrong": "問題が発生しました", "CheckEmailConfirmRegistration": "登録確認のためメールを確認してください", "EmailConfirmationSentText": "確認リンク付きのメールを送信しました。", "LoginToProfile": "プロフィールにログイン", "username": "ユーザー名", "email": "メール", "PasswordPlaceholder": "パスワード", "ConfirmPasswordPlaceholder": "パスワードを確認", "AcceptTermsPrefix": "私は同意します", "TermsOfService": "利用規約", "AndSeparator": "と", "PrivacyPolicy": "プライバシーポリシー", "CreateAccount": "アカウントを作成", "Back": "戻る", "SignInWithGoogle": "Googleでサインイン", "SignInWithGitHub": "GitHubでサインイン", "SignInWithEmailAndPassword": "メールとパスワードでサインイン", "translation": {"AIModel": "AIモデル", "UpgradePlan": "プレミアムプラン", "PremiumBadge": "プレミアム", "Active": "有効", "projectInfo.features": "機能", "Stats": "統計", "Performance": "パフォーマンス", "Cost": "コスト", "UpgradeTooltip": "アップグレードしてアンロック: ", "UpgradeTooltipSuffix": "パッケージ", "chat": "チャット", "code": "コード"}, "extendedThinkingTooltip": "AIが回答する前により深く考えるようにする", "extendedThinkingTooltipDisabled": "リソースを節約するために深い思考を無効化する", "AIModel": "AIモデル", "UpgradePlan": "プレミアムプラン", "PremiumBadge": "プレミアム", "Active": "有効", "projectInfo.features": "機能", "Stats": "統計", "Performance": "パフォーマンス", "Cost": "コスト", "UpgradeTooltip": "アップグレードしてアンロック: ", "UpgradeTooltipSuffix": "パッケージ", "HowBielaWorks": "BIELAの仕組み", "WatchPromptLaunch": "見る・入力・起動", "SearchVideos": "ライブラリで検索...", "NewReleased": "新着リリース", "Playing": "再生中", "NoVideosFound": "動画が見つかりません", "TryAdjustingYourSearchTerms": "検索語句を調整してみてください", "feedback": {"title": {"issue": "問題を報告する", "feature": "機能をリクエストする"}, "description": {"issue": "バグを見つけた、または問題が発生していますか？お知らせください。できるだけ早く修正いたします。", "feature": "新しい機能のアイデアがありますか？プラットフォームの改善のために、ぜひご提案をお聞かせください。"}, "success": {"title": "ありがとうございます！", "issue": "問題の報告が正常に送信されました。", "feature": "機能のリクエストが正常に送信されました。"}, "form": {"fullName": {"label": "氏名", "placeholder": "氏名を入力してください"}, "email": {"label": "メールアドレス", "placeholder": "メールアドレスを入力してください"}, "suggestion": {"labelIssue": "問題の詳細", "labelFeature": "機能の提案", "placeholderIssue": "発生している問題について説明してください...", "placeholderFeature": "希望する機能について説明してください..."}, "screenshots": {"label": "スクリーンショット（任意）", "note": "問題をより正確に把握するために、スクリーンショットを追加してください", "drop": "クリックしてアップロード、またはドラッグ＆ドロップしてください", "hint": "PNG、JPG、JPEG（各最大10MB）", "count": "{{count}} 件の画像が選択されました"}}, "buttons": {"cancel": "キャンセル", "submitting": "送信中...", "submitIssue": "問題を送信", "submitFeature": "リクエストを送信"}, "errors": {"fullNameRequired": "氏名は必須です", "fullNameNoNumbers": "氏名に数字を含めることはできません", "emailRequired": "メールアドレスは必須です", "emailInvalid": "有効なメールアドレスを入力してください", "suggestionRequired": "問題や提案を説明してください"}}, "changelog": {"title": "変更履歴", "description": "常に進化。前進し続ける。", "TotalReleases": "総リリース数", "ActiveUsers": "アクティブユーザー数", "FeaturesAdded": "追加された機能", "BugsFixed": "修正されたバグ", "shortCallText": "提案がありますか？バグを見つけましたか？ぜひご連絡ください！", "reportIssue": "問題を報告する", "requestFeature": "機能をリクエストする", "totalReleases": "総リリース数", "activeUsers": "アクティブユーザー", "featuresAdded": "追加された機能", "bugsFixed": "修正されたバグ", "types": {"feature": "機能", "improvement": "改善", "bugfix": "バグ修正", "ui/ux": "UI/UX", "announcement": "お知らせ", "general": "一般"}, "versions": {"v3.2.3": {"date": "2025年6月26日", "changes": {"0": "ユーザーダッシュボードでのいくつかのUI/UX問題を修正しました。", "1": "WebContainerの安定性を向上させました。", "2": "AI Diffモードを改善し、小さなファイルと大きなファイルを異なって処理するようにしました。"}}, "v3.2.2": {"date": "2025年6月24日", "changes": {"0": "より良いユーザーエクスペリエンスのためにフロントオフィスを再編成・再設計しました。", "1": "この素晴らしい変更履歴ページを実装しました。", "2": "WebContainerの再接続アルゴリズムを改善しました。", "3": "フォルダーアップロードに加えて、ZIPファイル経由でのプロジェクトアップロードのサポートを追加しました。", "4": "より良いインデックス化を活用してすべてのAPI呼び出しのパフォーマンスを向上させました。"}}, "v3.2.1": {"date": "2025年6月24日", "changes": {"0": "IDE：ユーザーダッシュボードの「フォルダー」セクションが改善されたエクスペリエンスのために再設計されました。", "1": "Content Studio：Content Studio内で画像をコピーできるようになりました。"}}, "v3.2.0": {"date": "2025年6月17日", "changes": {"0": "AI Diffモード（実験的）：AIは今、ファイル全体を再生成するのではなく、元のファイルと更新されたバージョンの差分のみを書き込みます。これによりパフォーマンスが大幅に向上し、トークンの使用が最適化されます。この実験的機能は、プロジェクトの設定→コントロールセンターエリアから有効または無効にできます。"}}, "v3.1.6": {"date": "2025年6月12日", "changes": {"0": "IDE：サブスクリプションと追加トークン用のクーポンコードフィールドが追加されました。"}}, "v3.1.5": {"date": "2025年6月11日", "changes": {"0": "IDE：「プロジェクトを共有」フローに様々な修正を適用しました。"}}, "v3.1.4": {"date": "2025年6月10日", "changes": {"0": "IDE：パッケージサブスクリプションが再チェックされ修正され、暗号通貨の包含とStripe実装の問題に対処しました。"}}, "v3.1.3": {"date": "2025年6月9日", "changes": {"0": "AI：より良いレイテンシを実現するためにGeminiの直接API統合を実装およびテストしました。"}}, "v3.1.2": {"date": "2025年6月6日", "changes": {"0": "アフィリエイト：新しいダッシュボードの情報モーダル（支払い履歴）とデザインの一貫性を持つアフィリエイト支払いタスクを実装しました。", "1": "Content Studio：ユーザーがAIに画像をリンクと一緒に送信するかどうかを決定する新しいオプションが追加されました。デフォルトでは「いいえ」に設定されています。"}}, "v3.1.1": {"date": "2025年6月5日", "changes": {"0": "Content Studio：Content Studioをプロジェクト別に整理するための更新が行われました。"}}, "v3.1.0": {"date": "2025年6月3日", "changes": {"0": "🚀 BIELA.devメジャーアップデートが到着しました！🚀", "1": "AIは今朝の5倍の速さでコードを書くようになりました。はい、読み間違いではありません。BIELAのパフォーマンスを過給して、アイデアから動作するコードまで一瞬で移行できるようにしました。", "2": "ターミナル：さらに良くなりました：今朝の改良に基づいて、よりスムーズなコーディングフローのための追加のUI/UX磨きを加えました。", "3": "Supabase接続の改善：よりクリーンで直感的なインターフェースにより、セットアップと統合が楽になります。", "4": "エラーハンドリングの更新（エラーの自動処理）：自動エラー報告が撤回されました。手動とAI支援の処理から選択できる新しい設定を準備しています。", "5": "新しいコントロールセンター（プロジェクト設定内）＆ユニットテストインターフェース：最初の機能：ユニットテストターミナルの切り替え。さらに多くのコントロールが来ます！", "6": "ダウンロードロジックの改善：ダウンロードはコードが完全にプッシュされるまで待機し、毎回完全で正確なエクスポートを保証します。"}}, "v3.0.0": {"date": "2025年6月3日", "changes": {"0": "🚀 BIELA.devアップデート - パワーブーストを受けました！🚀", "1": "刷新されたターミナルUI：ターミナルでの作業を楽しみにするより洗練されたクリーンなデザイン。", "2": "ワンクリックNPMアクション：新しいアクションボタンでnpm install、npm run build、またはnpm run devを瞬時に実行 - タイピング不要！（この機能はターミナルに統合されています）", "3": "よりスマートなエラーハンドリングとエラーカラー分類：プレビューエラーが自動的にAIに送信され、より速いデバッグをサポートします。（プロジェクトを開発している間、BIELAによるスムーズなバグ修正を体験します）", "4": "改善されたファイルナビゲーション：チャットでファイル名をクリックすると、WebContainerで直接開かれます。タップして編集するだけ！"}}, "v2.1.1": {"date": "2025年5月30日", "changes": {"0": "IDE：WebContainer用の保存ボタンが追加され、手動更新時に表示され、GitHubへのコミットとプッシュをトリガーします。", "1": "ダッシュボード：ユーザーダッシュボードからインポートされたプロジェクトの名前変更時に発生していたエラーを修正しました。", "2": "WebContainer：メモリリークの緊急修正を実装しました。"}}, "v2.1.0": {"date": "2025年5月28日", "changes": {"0": "🚀 BIELA.devアップデート — 7つの新しい改善が到着しました！🚀", "1": "IDE：インターフェースから直接、あなた自身のドメインをbielaプロジェクトにリンクする可能性を追加しました。", "2": "デプロイされたプロジェクトプレビュー：目のアイコンをワンクリックでデプロイされたプロジェクトをプレビューできるようになりました - 中身を知るために各々を開く必要がありません。", "3": "複製されたプロジェクトのコスト追跡修正：複製されたプロジェクトが元のプロジェクトからコスト推定を継承していた問題を修正しました。今はゼロから始まり、プロジェクトごとの正確な追跡を提供します。", "4": "ライブコスト推定（プロンプトごと）：コスト推定が1日1回ではなく、各プロンプト後に更新されるようになりました。これにより、開発代理店で同じものを開発するのにかかるコストと、BIELAでどれだけ節約できるかのリアルタイムフィードバックが得られます。", "5": "履歴タブとロールバック修正：プロジェクト履歴を再び安全にロールバックできるようになりました。最近の大きなアップデート後、これに問題がありましたが、今は非常にスムーズです。", "6": "オートスクロールの改善：コーディング中の画面フリーズがなくなりました！生成中にスクロールが遅れる問題を修正しました。シームレスなフローをお楽しみください。", "7": "メニューページが新しいタブで開く：Labsでコーディングしていて他のメニュー項目をクリックした場合、現在進行中の作業を失わないよう新しいタブで開かれます。"}}, "v2.0.4": {"date": "2025年5月27日", "changes": {"0": "ダッシュボード：ユーザーダッシュボードでのスクリーンショット改善には、自動スクロールの削除、ユーザー制御のスクロール許可、スクリーンショット読み込み用のアニメーション追加が含まれます。"}}, "v2.0.3": {"date": "2025年5月20日", "changes": {"0": "Content Studio：Content Studioのロボットアニメーションがフルサイズで表示されるようになりました。", "1": "アフィリエイト：Firefoxで表示する際のアフィリエイトダッシュボードの表示問題を修正しました。"}}, "v2.0.2": {"date": "2025年5月19日", "changes": {"0": "Content Studio：Content Studioで一度に許可される最大ファイル数が50に増加しました（10から）。"}}, "v2.0.1": {"date": "2025年5月18日", "changes": {"0": "Content Studio：ページが更新されない限り複数ファイルのタグが更新されない問題を修正しました。", "1": "Content Studio：画像選択時の編集ボタンがより見やすくなりました。", "2": "Content Studio：アップロード時に、フォルダーの場所が最後に作成されたまたは最後に選択されたフォルダーを自動選択するようになりました。", "3": "Content Studio：画像のドラッグ&ドロップ時に選択されたフォルダーが保持されるようになりました。", "4": "Content Studio：タブタイトルが新しい色（緑の代わりに）を使用するようになりました。", "5": "Content Studio：「画像を挿入」ボタンが「プロジェクトで使用」に名前変更されました。", "6": "Content Studio：ファイルアップロードボタンが緑の背景を持つようになりました。", "7": "Content Studio：より良い配置のために検索バーの高さが減少しました。", "8": "Content Studio：ユーザーに画像がない場合、検索バーが表示されなくなりました。"}}, "v2.0.0": {"date": "2025年5月16日", "changes": {"0": "🚀 BIELA.DEVでの大きなアップデート！🚀", "1": "私たち独自のWebコンテナテクノロジー：外部プロバイダーから完全に独立した独自のWebコンテナテクノロジーを構築しました！これにより、これまで以上に速く、より良い新機能を開発する前例のない柔軟性が得られます。", "2": "コード生成用の複数のLLMオプション：Anthropic以外のコード生成用複数のAIモデルをサポートするようになりました！GoogleのGeminiをコーディングプロジェクトに使用できるようになり、いくつかの大きな利点があります。Geminiは100万トークンの巨大なコンテキストウィンドウを提供します！", "3": "Content Studio：あなたの写真、あなたのプロジェクト：新しいContent Studioで完全に自分を表現しましょう！Vibe Codingプロジェクトで使用する独自の写真をアップロードできるようになりました。", "4": "プロジェクト共有：コラボレーションがさらに良くなりました！他のユーザーとプロジェクトを共有できるようになりました。", "5": "既存のSupabaseプロジェクトへの接続：この強力な新機能により、複数のBiela.devプロジェクトを同じSupabaseデータベースに接続できます。"}}, "v1.0.5": {"date": "2025年5月15日", "changes": {"0": "支払い：Stripe支払い処理が実装されました。", "1": "Content Studio：最初のページに画像がない場合、アニメーション（ロボットのような）が表示されるようになりました。"}}, "v1.0.4": {"date": "2025年5月13日", "changes": {"0": "Content Studio：Content Studioでのページネーション問題を修正しました。"}}, "v1.0.3": {"date": "2025年5月5日", "changes": {"0": "Content Studioを開始 - すべてのメディアを保存および管理する場所。", "1": "フロントオフィス：biela_frontofficでソフトウェアアプリ用のschema.orgを実装しました。", "2": "チームダッシュボード：チームダッシュボードに通知が追加されました（例：新しいメンバーが参加したり、チャットに新しいメッセージが表示されたときの赤い点）。", "3": "IDE：プロンプト入力時にWebContainer内の特定のエリアをクリックした際にメニューからのモーダルが適切に閉じない不具合を修正しました。"}}, "v1.0.2": {"date": "2025年5月2日", "changes": {"0": "アフィリエイト：チームメンバーエリアの再設計を実装しました。", "1": "アフィリエイト：アフィリエイトダッシュボードアイコンが更新されました。", "2": "IDE：「Supabase接続」ボタンのテキストが新しいアイコンとともに「データベース」に変更され、接続時に青色で「データベース接続済み」と表示されるようになりました。"}}, "v1.0.1": {"date": "2025年5月1日", "changes": {"0": "IDE（設定タブ）：設定チャットでデータベース統合を実装しました。", "1": "ダッシュボード：ユーザーダッシュボードでのページネーションを修正しました。"}}, "v1.0.0": {"date": "2025年4月18日", "changes": {"0": "🚀 Biela.devへようこそ：皆様のAI駆動コーディングコンパニオン、すべての方に無料で！🚀", "1": "Biela.devの初回パブリックリリースを発表できることを嬉しく思います！最先端のAI支援Web開発をすべての人にアクセス可能にすることを信じているため、私たちのプラットフォームは初日から完全に無料です。"}}}}}