{"meta": {"home": {"title": "biela.dev | Constructor Web & App bazat pe AI – Creează cu ajutorul comenzilor", "description": "Transformă-ți ideile în site-uri sau aplicații live cu biela.dev. <PERSON><PERSON><PERSON><PERSON><PERSON> comenzi bazate pe AI pentru a crea produse digitale personalizate cu ușurință."}, "privacy": {"title": "Politica de Confidențialitate biela.dev", "description": "Află cum colectează, utilizează și protejează biela.dev informațiile tale personale."}, "terms": {"title": "Termeni și Condiții biela.dev", "description": "Consultă termenii și condițiile de utilizare ale platformei de dezvoltare bazate pe AI de la biela.dev."}, "changelogSection": {"title": "Jurnalul schimbărilor biela.dev - Întotdeauna. Evoluție. Înainte.", "description": "Află cum biela.dev se îmbunătățește cu fiecare versiune"}, "competitionterms": {"title": "Termeni și Condiții – Concursul de Dezvoltare Biela", "description": "Participă la Concursul de Dezvoltare organizat de Biela.dev și TeachMeCode Institute – o provocare oficială de o lună. Prezintă-ți proiectele digitale, obține recunoaștere prin implicarea în comunitate și concurează pentru premii de top. Data limită de înscriere: 14 mai 2025."}, "contest": {"title": "Concursul de Dezvoltare biela.dev 2025 – Castigatori Anuntati", "description": "Descopera castigatorii concursului biela.dev si exploreaza proiectele incredibile construite cu AI care au castigat cele mai mari onoruri"}, "howTo": {"title": "Cum să folosești Biela.dev – Ghid complet pentru crearea de aplicații cu inteligență artificială", "description": "Descoperă cum să folosești Biela.dev pentru a construi rapid aplicații web, fără să scrii cod. Un ghid complet, pas cu pas, cu tutoriale și sfaturi practice.", "keywords": "biela, biela.dev, cum să folose<PERSON> biela, creează aplicații AI, f<PERSON><PERSON><PERSON> cod, dezvolta<PERSON> rapid<PERSON>, g<PERSON>d biela, tutorial biela", "ogTitle": "Cum să folosești Biela.dev – Ghid pas cu pas", "ogDescription": "Învață să creezi aplicații AI cu Biela.dev. De la idee la lansare, fără să scrii cod.", "twitterTitle": "Cum să folosești Biela.dev – G<PERSON>d complet", "twitterDescription": "Construiește aplicații AI pe Biela.dev fără cod. Urmează acest ghid pas cu pas!"}}, "navbar": {"home": "<PERSON><PERSON><PERSON>", "demo": "Demo", "community": "Comunitate", "affiliate": "<PERSON><PERSON><PERSON><PERSON>", "partners": "Partener<PERSON>", "contest": "Vibe Coding Hackathon", "subscriptions": "Abonamente", "pricing": "<PERSON><PERSON><PERSON>"}, "counter": {"days": "Zile", "hours": "Ore", "minutes": "Minute", "seconds": "Secunde", "centisec": "Centisecunde"}, "hero": {"title": "Transforma-ti ideea intr-un site sau aplicatie live in cateva minute", "subtitle": "Daca iti poti <1>imagina</1>, il poti <5>programa</5>.", "subtitle2": "Pornește gratuit. Codează tot. Transformă-ți abilitățile în fiecare cerere într-o oportunitate.", "timeRunningOut": "Timpul se scurge!", "launchDate": "<PERSON><PERSON><PERSON><PERSON>", "experimentalVersion": "Lansare Beta pe 15 aprilie 2025", "earlyAdopter": "Inscrie-te acum pentru a beneficia de avantajele early adopter inainte de lansarea oficiala", "tryFree": "Incearca G<PERSON>", "seeAiAction": "Vezi IA in actiune", "tellBiela": "Spune Bielei sa creeze", "inputPlaceholder": "Dacă îți poți imagina, BIELA o poate coda. Ce facem astăzi?", "chat": "Cha<PERSON>", "code": "Cod", "checklist": "Lista de verificare", "checklistTitle": "Foloseste liste de verificare pentru ", "checklistItems": {"trackDevelopment": "Urmarirea sarcinile de dezvoltare", "setRequirements": "Stabilirea cerintele proiectului", "createTesting": "Crearea protocoale de testare"}, "registerNow": "Inregistreaza-te acum", "attachFiles": "Ataseaza fisiere la promptul tau", "voiceInput": "Foloseste intrare vocala", "enhancePrompt": "Imbunatateste promptul", "cleanupProject": "Curata proiectul", "suggestions": {"weatherDashboard": "Creeaza un panou meteo", "ecommercePlatform": "Construieste o platforma de e-commerce", "socialMediaApp": "Proiecteaza o aplicatie de social media", "portfolioWebsite": "Genereaza un site de portofoliu", "taskManagementApp": "Creeaza o aplicatie de management al sarcinilor", "fitnessTracker": "Construieste un tracker de fitness", "recipeSharingPlatform": "Proiecteaza o platforma pentru partajarea retetelor", "travelBookingSite": "Creeaza un site de rezervari de calatorii", "learningPlatform": "Construieste o platforma de invatare", "musicStreamingApp": "Proiecteaza o aplicatie de streaming muzical", "realEstateListing": "<PERSON><PERSON>az<PERSON> un anunt imobiliar", "jobBoard": "Construieste un site de joburi"}}, "videoGallery": {"barber": {"title": "Site pentru frizerie", "description": "Vezi cum Biela.dev creeaza un site complet pentru o frizerie in cateva minute"}, "tictactoe": {"title": "Jocul X si 0", "description": "Priveste cum IA creeaza un joc de X si 0 mai repede decat poti spune 'X si 0'"}, "coffeeShop": {"title": "Site pentru cafenea", "description": "Vezi cum Biela.dev creeaza un site pentru o cafenea"}, "electronicsStore": {"title": "Site pentru magazin de electronice", "description": "Vezi cum Biela.dev creeaza un magazin online complet in cateva minute"}, "seoAgency": {"title": "Site pentru agentie SEO", "description": "Priveste cum IA creeaza un site pentru o agentie SEO"}}, "telegram": {"title": "Alatura-te canalului nostru Telegram", "subtitle": "Conecteaza-te cu comunitatea Biela", "description": "Primeste suport instant, impartaseste-ti proiectele si conecteaza-te cu entuziasti de IA din intreaga lume. <br/><br/>Comunitatea noastra de pe Telegram este cea mai rapida modalitate de a ramane la curent cu Biela.dev.", "stats": {"members": "<PERSON><PERSON><PERSON>", "support": "Suport", "countries": "<PERSON><PERSON>", "projects": "Proiecte"}, "joinButton": "Alatura-te comunitatii noastre de pe Telegram", "communityTitle": "Comunitatea Biela.dev", "membersCount": "membri", "onlineCount": "online acum", "messages": {"1": "Tocmai mi-am lansat site-ul de e-commerce cu Biela.dev in mai putin de o ora!", "2": "Arata uimitor! Cum ai gestionat catalogul de produse?", "3": "Biela s-a ocupat de asta automat! Am descris doar ce mi-am dorit.", "4": "Construiesc acum un site de portofoliu. Sugestiile IA sunt incredibile!", "5": "A incercat cineva noile template-uri responsive? Arata fantastic pe mobil."}}, "joinNetwork": {"title": "Alatura-te retelei noastre", "subtitle": "Conectează-te. Crește. Câștigă.", "affiliateProgram": "Program de Afiliere", "registerBring": "Inregistreaza-te si adu 3 afiliati", "aiCourse": "Curs de Inginerie IA", "courseDescription": "Curs cuprinzator de IA gratuit cu TeachMeCode", "digitalBook": "Carte Digitala", "bookDescription": "Editie digitala exclusiva a 'The Art of Prompt Engineering'", "exclusiveBenefits": "Beneficii Exclusive", "benefitsDescription": "Castiguri pe viata si privilegii speciale pentru membri", "registerNow": "Inregistreaza-te acum", "teamEarnings": "<PERSON><PERSON><PERSON><PERSON>", "buildNetwork": "Construieste-ti reteaua", "weeklyMentorship": "Mentorat saptamanal", "teamBonuses": "<PERSON><PERSON>", "startEarning": "Incepe sa castigi astazi", "upToCommission": "PANA LA Comision"}, "partners": {"title": "Incredintat de lideri din industrie", "subtitle": "Colaboram cu inovatori globali pentru a contura viitorul dezvoltarii IA", "strategicPartnerships": "Parteneriate Strategice", "joinNetwork": "Alatura-te retelei tot mai mari de parteneri si companii care transforma dezvoltarea cu BIELA"}, "demo": {"title": "Priveste cum IA construieste aplicatii si site-uri in timp real", "subtitle": "Descopera cum companiile isi transforma prezenta digitala cu Biela.dev", "seeInAction": "Vezi Biela.dev in actiune", "whatKind": "Ce tip de site iti trebuie?", "describeWebsite": "Descrie-ti ideea de site (de exemplu, 'Un portofoliu pentru un fotograf')", "generatePreview": "Genereaza previzualizare", "nextDemo": "De<PERSON> urmator", "startBuilding": "Incepe sa construiesti si sa castigi cu Biela.dev astazi"}, "footer": {"quickLinks": {"title": "<PERSON>uri rapide", "register": "Inregistreaza-te", "bielaInAction": "Vezi Biela.dev in actiune", "liveOnTelegram": "Suntem live pe Telegram", "affiliate": "Marketing afiliat colaborativ", "partners": "Partener<PERSON>"}, "legal": {"title": "Legal", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. Toate drepturile rezervate.", "unauthorized": "<PERSON><PERSON><PERSON><PERSON>, reproducerea sau distribuirea neautorizata a acestui serviciu, in totalitate sau partial, fara permisiune scrisa explicita este strict interzisa."}, "reservations": "TeachMeCode Institute isi rezerva toate drepturile legale asupra proprietatii intelectuale asociate Biela.", "terms": "Termeni de utilizare", "privacy": "Politica de confidențialitate"}, "bottomBar": {"left": "2025 Biela.dev. Alimentat de %s © Toate drepturile rezervate.", "allRights": "Toate drepturile rezervate.", "right": "Dezvoltat de %s"}}, "common": {"loading": "Se incarca...", "error": "A aparut o eroare", "next": "Urmatorul", "previous": "Anteriorul", "submit": "Trimite", "cancel": "Anuleaza", "close": "<PERSON><PERSON><PERSON>", "loginNow": "Conectează-te acum", "verifyAccount": "Verifică contul"}, "languageSwitcher": {"language": "Limba"}, "contest": {"bielaDevelopment": "Vibe Coding Hackathon", "competition": "COMPETITIE", "firstPlace": "LOCUL 1", "secondPlace": "LOCUL 2", "thirdPlace": "LOCUL 3", "currentLeader": "Lider actual:", "footerText": "Dezvoltare asistata de AI de catre Biela.dev, sustinuta de Institutul TeachMeCode®", "browseHackathonSubmissions": "Rasfoiti inscrierile la Hackathon", "winnersAnnouncement": "Castigatorii Concursului Anuntati!", "congratulationsMessage": "Felicitari castigatorilor nostri uimitori! Multumim tuturor celor care au participat la acest hackathon incredibil.", "conditionActiveReferrals": "Are 3 recomandari active", "conditionLikedProject": "A apreciat un alt proiect", "winner": "Castigator", "qualificationsMet": "Calificari:", "noQualifiedParticipant": "Niciun Participant Calificat", "requirementsNotMet": "Cerintele nu sunt indeplinite", "noQualificationDescription": "Niciun participant nu a indeplinit cerintele de calificare pentru aceasta pozitie.", "qualifiedWinners": "🏆 Castigatori Calificati", "qualifiedWinnersMessage": "Felicitari participantilor care au indeplinit toate cerintele de calificare!", "noTopPrizesQualified": "Niciun Participant Calificat pentru Premiile de Top", "noTopPrizesMessage": "<PERSON> p<PERSON>, niciun participant nu a indeplinit cerintele de calificare pentru primele trei pozitii de premii. Toti participantii calificati vor primi premii din fondul de premii ramas.", "noTopPrizesMessageShort": "<PERSON> p<PERSON>, niciun participant nu a indeplinit cerintele de calificare pentru primele trei pozitii de premii."}, "competition": {"header": {"mainTitle": "Propunerea ta. Stilul tau. Terenul tau de joaca.", "timelineTitle": "PROGRAM", "timelineDesc": "O lună pentru a crea cel mai bun proiect generat de AI", "eligibilityTitle": "ELEGIBILITATE", "eligibilityDesc": "Conturi active Biela.dev care trimit un proiect", "prizesTitle": "PREMII", "prizesDesc": "$16.000 în premii în numerar plus acces gratuit pentru c<PERSON>știgători", "votingTitle": "VOTARE", "votingDesc": "Conduse de comunitate, un vot per utilizator verificat", "tagline": "Lasă-ți creativitatea să vorbească. Lasă lumea să <PERSON>ze."}, "details": {"howToEnter": "Cum sa Participi", "enter": {"step1": {"title": "Creează un proiect folosind AI-ul de la Biela.dev", "desc": "Folosește uneltele puternice de AI de la Biela pentru a-ți construi proiectul inovator"}, "step2": {"title": "Accesează panoul tău de utilizator", "desc": "Face<PERSON><PERSON> clic pe \"Publicați pentru Hackathon\" lângă proiectul dvs.", "subdesc": "Poți trimite până la 3 proiecte diferite"}, "step3": {"title": "Biela îți va procesa înscrierea:", "list1": "Fă o captură de ecran a proiectului tău", "list2": "Verifică-ți solicitările pentru eligibilitate", "list3": "Generează un rezumat și o descriere"}}, "prizeStructure": "Structura premiilor", "prizes": {"firstPlace": "Locul I", "firstPlaceValue": "$10,000", "secondPlace": "Locul II", "secondPlaceValue": "$5,000", "thirdPlace": "Locul III", "thirdPlaceValue": "$1,000", "fourthToTenth": "Locul IV–X", "fourthToTenthValue": "30 de zile acces nelimitat"}}, "qualification": {"heading": "Cerinte de calificare", "description": "Pentru a te califica pentru cele 3 premii de top (10.000 $, 5.000 $ și 1.000 $), trebuie să îndeplinești următoarele criterii:", "criteria1": "Să ai cel puțin 3 referințe active", "criteria2": "Să dai like la cel puțin un proiect de pe peretele de expoziție", "proTipLabel": "Sfat profesionist:", "proTipDesc": "Cu cât îți trimiți proiectul mai devreme, cu atât va primi mai multă vizibilitate și mai multe voturi!"}, "voting": {"heading": "Instructiuni de vot", "oneVotePolicyTitle": "Politica de un singur vot", "oneVotePolicyDesc": "Poți vota o singură dată și nu poți vota propriul proiect", "accountVerificationTitle": "Verificarea contului", "accountVerificationDesc": "Doar utilizatorii cu conturi verificate Biela.dev pot vota", "tieBreakingTitle": "Soluționarea egalităților", "tieBreakingDesc": "În caz de egalitate, factorul decisiv va fi numărul total de stele obținute de creatorul proiectului (folosite sau nefolosite)", "howToVoteTitle": "Cum sa <PERSON>zi", "howToVoteDesc": "Parcurge peretele de expoziție, explorează toate proiectele publicate generate de AI și apasă pe iconița inimii pentru a vota proiectul tău preferat!"}}, "contestItems": {"projectShowcase": "Expoziția Proiectelor", "loading": "Se încarcă...", "createBy": "<PERSON><PERSON><PERSON> de ", "viewProject": "Vezi proiectul", "stars": "Stele", "overview": "prezentare generală", "keyFeatures": "Caracteristici cheie", "exploreLiveProject": "Explorează proiectul live", "by": "de", "likeLimitReached": "Limită de aprecieri atinsă", "youCanLikeMaximumOneProject": "Poți aprecia maximum un proiect. Dorești să anulezi aprecierea actuală și să apreciezi acest proiect în schimb?", "currentlyLikedProject": "Proiect apreciat în prezent", "switchMyLike": "Schimbă aprecierea mea", "mustLoggedIntoLikeProject": "Trebuie să fii autentificat pentru a aprecia un proiect.", "signInToBielaAccountToVote": "Te rugăm să te autentifici în contul tău Biela.dev pentru a participa la procesul de votare.", "yourAccountIsNotVerifiedYet": "Contul tău nu a fost încă verificat. Te rugăm să-ți verifici contul pentru a putea aprecia un proiect.", "accountVerificationEnsureFairVoting": "Verificarea contului asigură un vot corect și ajută la menținerea integrității competiției.", "projectsSubmitted": "Proiecte trimise:", "unlikeThisProject": "Nu mai îmi place acest proiect", "likeThisProject": "Îmi place acest proiect", "likes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "like": "apreciere", "somethingWentWrong": "Ceva nu a mers bine. Vă rugăm să încercați din nou mai târziu", "voteError": "<PERSON><PERSON><PERSON> de vot"}, "textsLiveYoutube": {"first": {"title": "Ești invitat!", "description": "Facem live vineri — această sesiune e despre construirea cu <green>scop</green>, <blue>precizie</blue> și <orange>vibe pur</orange>.", "secondDescription": "Află cum să <green>stăpânești vibe coding</green>, <blue>să descoperi funcții noi</blue> înaintea tuturor și <orange>să vezi proiecte reale</orange> construindu-se live.", "specialText": "Dă click aici și apasă \"Notifică-mă\" pe YouTube — nu rata asta."}, "second": {"title": "Fii primul care construiește.", "description": "<PERSON><PERSON> ducem <green>vibe coding</green> la un nivel cu totul nou — îți arătăm cum să <blue>creezi cu intenție</blue>, <orange>deblochezi funcții noi</orange> și s<PERSON> la<PERSON>zi <green>mai rapid ca niciodată</green>.", "secondDescription": "<green>Build-uri noi</green>, <blue>idei noi</blue> și <orange>oportunități noi</orange> de creștere.", "specialText": "Dă click aici pentru a seta memento-ul YouTube — și fii parte din valul următor de creatori."}, "third": {"title": "Rezervă-ți locul.", "description": "Următorul nostru <green>livestream Biela.dev</green> are loc vineri — și ești <blue>oficial invitat</blue>.", "secondDescription": "Ne adâncim în <green>vibe coding</green>, dez<PERSON><PERSON><PERSON><PERSON> <blue>funcții noi puternice</blue> și te ajutăm să construiești <orange>mai inteligent, mai rapid și la scară mai mare</orange>.", "specialText": "Dă click aici și apasă \"Notifică-mă\" pe YouTube — următoarea ta mare idee poate începe aici."}}, "stream": {"title": "<PERSON>i <1><PERSON><PERSON><PERSON></1> care <3><PERSON>st<PERSON><PERSON><PERSON><PERSON></3>.", "subtitle": "Urmărește livestream-ul nostru de vineri — funcționalități, proiecte și actualizări exclusive.", "button": "Setează-ți memento-ul pe YouTube."}, "billings": {"modalTitle": "Alege un Plan", "modalSubtitle": "Alege dintre cele trei planuri flexibile pentru a continua să scrii cod fără limite.", "toggleMonthly": "Lunar", "toggleYearly": "<PERSON><PERSON> (economisește 10%)", "mostPopular": "Cel Mai <PERSON>", "tokensLowerCase": "<PERSON>i", "tokensUpperCase": "Tokeni", "below": "mai jos", "unlimited": "NELIMITAT", "10Bonus": "Bonus 10%", "currentPlan": "<PERSON><PERSON>nt", "upgradePlan": "Upgrade Plan", "purchaseDetails": "Detalii Achiziție", "dateLabel": "Dată", "planLabel": "Plan", "amountLabel": "Sumă", "whatsNext": "Ce Urmează", "yourTokensAvailable": "Tokenii tai sunt acum disponibili în cont", "purchaseDetailsStored": "Detaliile achiziției au fost salvate în contul tău", "viewPurchaseHistory": "Poți vizualiza istoricul achizițiilor în secțiunea de facturare", "thankYou": "Mulțumim!", "purchaseSuccessful": "Achiziția a fost realizată cu succes", "startCoding": "Începe să scrii cod", "month": "lună", "year": "an"}, "feature": {"basic_ai": "Dezvoltare AI de bază", "community_support": "Suport comunitar", "standard_response": "Timp standard de răspuns", "basic_templates": "Șabloane de bază", "advanced_ai": "Dezvoltare AI avansată", "priority_support": "Suport prioritar", "fast_response": "Timp rapid de răspuns", "premium_templates": "Șabloane premium", "team_collaboration": "Colaborare în echipă", "enterprise_ai": "Dezvoltare AI pentru companii", "support_24_7": "Suport prioritar 24/7", "instant_response": "<PERSON><PERSON> ră<PERSON>", "custom_templates": "Șabloane personalizate", "advanced_team": "Funcționalități avansate pentru echipă", "custom_integrations": "Integrări personalizate", "big_context_window": "Fereastră mare de context - de 5 ori mai mare decât cea medie", "code_chat_ai": "Acces la AI pentru cod și chat", "tokens_basic": "Tokeni suficienti pentru aprox. 3 site-uri de prezentare", "standard_webcontainer": "Webcontainer standard dedicat", "medium_context_window": "Fereastră medie de context - potrivită pentru un site, un blog sau un joc browser de bază", "basic_support": "Suport de bază cu timp standard de răspuns", "supabase_integration": "Integrare Supabase", "project_sharing": "Partajează proiectele cu prietenii tăi", "project_download": "Descar<PERSON><PERSON> proiectele tale", "project_deployment": "Funcționalitate de implementare", "custom_domain": "Conectează proiectele la propriul tău domeniu", "tokens_creator_plus": "Tokeni suficienti pentru aprox. 7 site-uri de prezentare", "advanced_support": "Suport avansat cu timp de răspuns mai rapid", "prioritary_support": "Suport prioritar dedicat", "early_feature_access": "Acces timpuriu la funcționalități noi", "human_cto": "CTO uman dedicat a<PERSON><PERSON>i tale", "community_promotion": "Promovează-ți afacerea prin comunitatea noastră"}, "JoinUsLive": "Alătură-te live", "howToUseBiela": {"subtitle": "Cum să folosești Biela", "title": "Explicat în câteva minute", "description": "Explicații scurte despre cum funcționează fiecare caracteristică"}, "slides": {"newHistoryFeature": "Descoperă noua funcționalitate ISTORIC din Biela!", "newHistoryFeatureDescription": "Fila ISTORIC menține procesul tău de Vibe Coding fluid, permițându-ți să revii și să restaurezi orice versiune anterioară a proiectului, astfel încât să rămâi mereu în control!", "fixPreview": "Cum repari previzualizarea în Biela!", "fixPreviewDescription": "Urmărește ghidul complet și asigură-te că totul apare exact așa cum ai construit.", "signUp": "Cum te înregistrezi pe Biela!", "signUpDescription": "Nou pe biela.dev? Urmărește acest ghid rapid pentru a învăța cum să te înregistrezi și să îți verifici contul!", "buildWebsite": "Cum creezi un site web cu Biela!", "buildWebsiteDescription": "Învață cum să creezi și să lansezi un site web de la zero folosind biela.dev — fără să scrii nici măcar o linie de cod.", "downloadAndImport": "Cum descarci și imporți proiecte și conversații în Biela!", "downloadAndImportDescription": "În acest ghid pas cu pas, vei învăța cum să îți descarci proiectele finalizate și cum să imporți conversațiile salvate în spațiul tău de lucru — astfel încât să poți continua exact de unde ai rămas și să menții fluxul de Vibe Coding oricând.", "shareProject": "Cum partajezi proiectul tău BIELA Vibe Code!", "shareProjectDescription": "Ești gata să îți partajezi proiectul ca un profesionist? Urmărește ghidul complet și începe acum.", "launchProject": "Cum îți lansezi proiectul pe propriul domeniu!", "launchProjectDescription": "În acest videoclip, parcurgem pașii simpli pentru a-ți face site-ul live — de la configurare până la publicare finală, ai tot ce îți trebuie pentru a trece cu încredere de la construire la lansare.", "deployProject": "Cum îți publici proiectul și îl faci live!", "deployProjectDescription": "Acest ghid rapid te conduce prin toți pașii pentru a-ți publica rapid și lin site-ul, aplicația sau dashboard-ul."}, "maintenance": {"title": "Lucrăm la rezolvarea problemei", "description": "Biela.dev este în modul de mentenanță. Lucrăm la rezolvarea problemei", "text": "<PERSON>ă mulțumim pentru asteptare."}, "planCard": {"descriptionBeginner": "Perfect pentru începători care își construiesc primele proiecte și învață dezvoltare web", "descriptionCreator": "Ideal pentru creatori care au nevoie de mai multă putere și flexibilitate pentru proiecte avansate", "descriptionProfessional": "Soluție completă pentru dezvoltatori profesioniști și echipe care construiesc aplicații", "perMillionTokens": "$ {{price}} / pe milion de tokeni", "dedicatedCto": "CTO dedicat - 24 de ore", "tokens": "Tokeni", "perMonth": "pe lună", "perYear": "pe an", "freeTokens": "{{ tokens }} To<PERSON><PERSON> gratuiti", "monthly": "Lunar", "yearly": "<PERSON><PERSON>", "save": "ECONOMISEȘTE 10%"}, "faqLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fre<PERSON>", "faqHeading": "Vezi cele mai frecvente întrebari", "faqSubheading": "Începe. Înțelege. Explorează", "whatAreTokensQuestion": "Ce sunt tokenii?", "whatAreTokensAnswer1": "Tokenii sunt modul în care IA procesează proiectul tău. Pe Biela.dev, tokenii sunt folosiți atunci când trimiți un prompt către IA. În acest proces, fișierele proiectului sunt incluse în mesaj pentru ca IA să poată înțelege codul tău. Cu cât proiectul este mai mare și răspunsul mai lung, cu atât mai mulți tokeni sunt necesari per mesaj.", "whatAreTokensAnswer2": "În plus, un număr mic de tokeni este dedus din contul tău pentru fiecare minut în care utilizezi un webcontainer, pentru a acoperi costul mediului de dezvoltare.", "freePlanTokensQuestion": "Cati tokeni primesc în planul gratuit?", "freePlanTokensAnswer": "Fiecare utilizator al planului gratuit primește 200.000 de tokeni pe zi, care se reîmprospătează zilnic.", "outOfTokensQuestion": "Ce se întampla daca raman fara tokeni?", "outOfTokensAnswer": "Nicio grijă! Poți oricând merge la Meniu → Facturare și să cumperi instant mai mulți tokeni dacă ai nevoie.", "changePlanQuestion": "Pot schimba planul mai tarziu?", "changePlanAnswer": "Da — oricând. Accesează Meniu → Facturare pentru a face upgrade, downgrade sau a schimba planul în funcție de nevoile tale.", "rolloverQuestion": "Tokenii nefolositi se reporteaza?", "rolloverAnswer": "<PERSON><PERSON>, tokenii se resetează la data de expirare și nu se reportează — așa că asigură-te că îi folosești zilnic!", "exploreHowToUseBiela": "Explorează cum să folosești Biela", "new": "Nou", "joinVibeCoding": "Participă la Hackathon-ul Vibe Coding", "whatDoYouWantToBuild": "CE VREI SA CONSTRUIESTI?", "imaginePromptCreate": "Imaginează-ți. Propune. Creează.", "levelOfAmbition": "Care este nivelul tau de ambitie?", "startGrowScale": "Începe. Crește. Scalează.", "calloutBar": {"start": "Toate planurile <PERSON><PERSON><PERSON> gratuit cu", "tokens": "200.000 tokeni pe zi", "end": ". Fără fricțiuni la înregistrare. Doar începe s<PERSON> c<PERSON>zi."}, "atPriceOf": "la prețul de", "startFreeNoCreditCard": "<PERSON><PERSON><PERSON> Fără Card de Credit", "SignIn": "Autentificare", "Register": "Înregistrare", "Affiliates": "Afiliere", "UsernameRequired": "Numele de utilizator este obligatoriu", "PasswordRequired": "Parola este obligatorie", "MissingUsernamePasswordOrTurnstile": "Lipsește numele de utilizator, parola sau tokenul Turnstile", "LoginSuccess": "Autentificare reușită!", "LoginFailed": "Autentificare eșuată", "EmailRequired": "Emailul este obligatoriu", "EmailInvalid": "Te rugăm să introduci o adresă de email validă", "CaptchaRequired": "Te rugăm s<PERSON>zi CAPTCHA", "ResetLinkSent": "Un link de resetare a fost trimis către adresa ta de email.", "ResetFailed": "Trimiterea linkului de resetare a eșuat", "BackToLogin": "Înapoi la autentificare", "EmailPlaceholder": "Emailul tău", "SendResetLink": "Trimite linkul de resetare", "loading": "Se încarcă", "checkingYourAuthenticity": "Verificăm autenticitatea ta", "ToUseBielaDev": "Pentru a folosi Biela.dev trebuie să te autentifici într-un cont existent sau să creezi unul folosind una dintre opțiunile de mai jos", "Sign in with Google": "Autentifică-te cu Google", "Sign in with GitHub": "Autentifică-te cu GitHub", "Sign in with Email and Password": "Autentifică-te cu email și parolă", "DontHaveAnAccount": "Nu ai un cont?", "SignUp": "Înregistrează-te", "ByUsingBielaDev": "Prin utilizarea Biela.dev îți dai acordul pentru colectarea datelor de utilizare.", "EmailOrUsernamePlaceholder": "Email/Nume utilizator", "passwordPlaceholder": "Pa<PERSON><PERSON>", "login.loading": "Se încarcă", "LoginInToProfile": "Autentifică-te în profilul tău", "login.back": "Înapoi", "forgotPassword": "Ai uitat parola?", "PasswordsMismatch": "Parolele nu se potrivesc.", "PhoneRequired": "Numărul de telefon este obligatoriu", "ConfirmPasswordRequired": "Te rugăm s<PERSON>i parola", "AcceptTermsRequired": "Trebuie să accepți Termenii și Condițiile și Politica de Confidențialitate", "RegistrationFailed": "Înregistrarea a eșuat", "EmailConfirmationSent": "Confirmarea prin email a fost trimisă. Te rugăm să îți confirmi emailul și apoi să te autentifici.", "RegistrationServerError": "Înregistrarea a eșuat (serverul a returnat false).", "SomethingWentWrong": "Ceva nu a funcționat", "CheckEmailConfirmRegistration": "Verifică-ți emailul pentru a confirma înregistrarea", "EmailConfirmationSentText": "Ți-am trimis un email cu un link de confirmare.", "LoginToProfile": "Autentificare în profil", "username": "Nume utilizator", "email": "Email", "PasswordPlaceholder": "Pa<PERSON><PERSON>", "ConfirmPasswordPlaceholder": "Confirmă parola", "AcceptTermsPrefix": "Sunt de acord cu", "TermsOfService": "Termenii și Condițiile", "AndSeparator": "și", "PrivacyPolicy": "Politica de Confidențialitate", "CreateAccount": "Creează cont", "Back": "Înapoi", "SignInWithGoogle": "Autentifică-te cu Google", "SignInWithGitHub": "Autentifică-te cu GitHub", "SignInWithEmailAndPassword": "Autentifică-te cu email și parolă", "translation": {"AIModel": "Model AI", "UpgradePlan": "un plan premium", "PremiumBadge": "PREMIUM", "Active": "Activ", "projectInfo.features": "Funcționalități", "Stats": "Statistici", "Performance": "Performanță", "Cost": "Cost", "UpgradeTooltip": "Deblochează prin upgrade la ", "UpgradeTooltipSuffix": "pachet", "chat": "Cha<PERSON>", "code": "Cod"}, "extendedThinkingTooltip": "Permite AI-ului să gândească mai profund înainte de a răspunde", "extendedThinkingTooltipDisabled": "Dezactivează gândirea extinsă pentru a economisi resurse", "AIModel": "Model AI", "UpgradePlan": "un plan premium", "PremiumBadge": "PREMIUM", "Active": "Activ", "projectInfo.features": "Funcționalități", "Stats": "Statistici", "Performance": "Performanță", "Cost": "Cost", "UpgradeTooltip": "Deblochează prin upgrade la ", "UpgradeTooltipSuffix": "pachet", "HowBielaWorks": "Cum functioneaza BIELA", "WatchPromptLaunch": "Urmărește. Scrie. Lansează.", "SearchVideos": "Caută în bibliotecă...", "NewReleased": "LANSARE NOUĂ", "Playing": "Redare", "NoVideosFound": "Nu au fost găsite videoclipuri", "TryAdjustingYourSearchTerms": "Încearcă să modifici termenii de căutare", "feedback": {"title": {"issue": "Raportează o problemă", "feature": "Solicită o funcționalitate"}, "description": {"issue": "Ai găsit un bug sau întâmpini o problemă? Spune-ne și o vom rezolva cât mai curând posibil.", "feature": "Ai o idee pentru o nouă funcționalitate? Ne-ar plăcea să aflăm sugestiile tale pentru a îmbunătăți platforma noastră."}, "success": {"title": "Mulțumim!", "issue": "Raportul tău a fost trimis cu succes.", "feature": "Solicitarea ta de funcționalitate a fost trimisă cu succes."}, "form": {"fullName": {"label": "Nume complet", "placeholder": "Introdu numele complet"}, "email": {"label": "Adresă de email", "placeholder": "Introdu adresa de email"}, "suggestion": {"labelIssue": "Descrierea problemei", "labelFeature": "Sugestie de funcționalitate", "placeholderIssue": "Descrie problema pe care o întâmpini...", "placeholderFeature": "Descrie funcționalitatea pe care ai dori să o vezi..."}, "screenshots": {"label": "<PERSON><PERSON> (opțional)", "note": "Adaugă capturi de ecran pentru a ne ajuta să înțelegem mai bine problema", "drop": "Apasă pentru a încărca sau trage fișierele aici", "hint": "PNG, JPG, JPEG de până la 10MB fiecare", "count": "{{count}} imagine{{count > 1 ? 'e' : ''}} selectată(e)"}}, "buttons": {"cancel": "Anulează", "submitting": "Se trimite...", "submitIssue": "Trimite problema", "submitFeature": "Trimite solicitarea"}, "errors": {"fullNameRequired": "Numele complet este obligatoriu", "fullNameNoNumbers": "Numele complet nu poate conține numere", "emailRequired": "Adresa de email este obligatorie", "emailInvalid": "Te rog să introduci o adresă de email validă", "suggestionRequired": "Te rog să descrii problema sau sugestia ta"}}, "changelog": {"title": "Jurnal de modificări", "description": "Întotdeauna. În evoluție. Spre înainte.", "TotalReleases": "Lansări totale", "ActiveUsers": "Utilizatori activi", "FeaturesAdded": "Funcționalități adăugate", "BugsFixed": "Erori corectate", "shortCallText": "Ai sugestii sau ai găsit o problemă? Ne-ar plăcea să aflăm!", "reportIssue": "Raportează o problemă", "requestFeature": "Solicită o funcționalitate", "types": {"feature": "Funcționalitate", "improvement": "Îmbunătățire", "bugfix": "Corecție de erori", "ui/ux": "Interfață/Experiență utilizator", "announcement": "<PERSON><PERSON><PERSON>", "general": "General"}, "versions": {"v3.2.3": {"date": "26 iunie 2025", "changes": {"0": "S-au remediat probleme de UI/UX în tabloul de bord al utilizatorului.", "1": "S-a îmbunătățit stabilitatea WebContainer.", "2": "AI Diff Mode a fost îmbunătățit pentru a gestiona fișiere mici diferit față de fișiere mari."}}, "v3.2.2": {"date": "24 iunie 2025", "changes": {"0": "Interfața frontoffice a fost reorganizată și redesenată pentru o experiență mai bună.", "1": "A fost implementată această pagină de jurnal al modificărilor.", "2": "Algoritmul de reconectare pentru WebContainer a fost îmbunătățit.", "3": "S-a adăugat suport pentru încărcarea proiectelor și în format ZIP, pe lângă încărcarea directoarelor.", "4": "Performanța tuturor apelurilor API a fost îmbunătățită folosind indexări mai eficiente."}}, "v3.2.1": {"date": "24 iunie 2025", "changes": {"0": "IDE: Secț<PERSON><PERSON> \"Foldere\" din tabloul de bord al utilizatorului a fost redesenată.", "1": "Content Studio: Acum poți copia imagini în interiorul studioului."}}, "v3.2.0": {"date": "17 iunie 2025", "changes": {"0": "AI Diff Mode (Experimental): AI-ul scrie acum doar diferențele dintre fișierul original și cel modificat, în loc să regenereze între<PERSON><PERSON> fi<PERSON>. Aceasta îmbunătățește performanța și optimizează utilizarea de tokenuri. Poate fi activat sau dezactivat din Setări → Centrul de Control."}}, "v3.1.6": {"date": "12 iunie 2025", "changes": {"0": "IDE: A fost adăugat un câmp pentru coduri promoționale pentru abonamente și tokenuri suplimentare."}}, "v3.1.5": {"date": "11 iunie 2025", "changes": {"0": "IDE: Au fost aplicate corecturi pentru procesul de „Partajare a unui proiect”."}}, "v3.1.4": {"date": "10 iunie 2025", "changes": {"0": "IDE: Abonamentele pachetelor au fost verificate și corectate, inclusiv integrarea Stripe și criptomonedele."}}, "v3.1.3": {"date": "9 iunie 2025", "changes": {"0": "AI: Integrarea directă a API-ului pentru Gemini a fost implementată pentru o latență mai redusă."}}, "v3.1.2": {"date": "6 iunie 2025", "changes": {"0": "Afiliați: S-au adăugat sarcini de plată pentru afiliați, cu design în ton cu noul mod de informare din dashboard.", "1": "Content Studio: A fost adăugată opțiunea de a trimite imagini către AI împreună cu linkuri (dezactivat implicit)."}}, "v3.1.1": {"date": "5 iunie 2025", "changes": {"0": "Content Studio: Acum este organizat per proiect."}}, "v3.1.0": {"date": "3 iunie 2025", "changes": {"0": "🚀 Actualizare majoră BIELA.dev! 🚀", "1": "AI-ul scrie cod de 5 ori mai repede!", "2": "Terminal: Interfață UI/UX rafinată pentru o experiență de programare mai fluidă.", "3": "Conectarea la Supabase este mai clară și mai intuitivă.", "4": "Raportarea automată a erorilor a fost dezactivată. Urmează o setare nouă pentru a alege între mod manual sau AI.", "5": "Control Center nou în Setări proiect + interfață pentru testare unitară.", "6": "Descărcările se declanșează doar după ce codul este complet salvat — pentru exporturi corecte."}}, "v3.0.0": {"date": "3 iunie 2025", "changes": {"0": "🚀 BIELA.dev devine mai puternic! 🚀", "1": "UI terminal complet modernizat — mai curat și mai plăcut.", "2": "<PERSON><PERSON><PERSON> pentru comenzi npm cu un singur clic: install, build, dev.", "3": "AI detectează și clasifică automat erorile pentru depanare rapidă.", "4": "Navigarea fișierelor: un clic pe numele fișierului îl deschide în WebContainer."}}, "v2.1.1": {"date": "30 mai 2025", "changes": {"0": "IDE: A fost adăugat un buton de salvare pentru WebContainer, care apare atunci când sunt făcute modificări manuale, declanș<PERSON>d commit și push pe GitHub.", "1": "Tablou de bord: A fost corectată o eroare care apărea la redenumirea proiectelor importate.", "2": "WebContainer: A fost aplicat un patch urgent pentru o problemă de scurgere de memorie."}}, "v2.1.0": {"date": "28 mai 2025", "changes": {"0": "🚀 Actualizare BIELA.dev — 7 îmbunătățiri noi! 🚀", "1": "IDE: Acum poți conecta propriul domeniu la proiectul Biela din interfață.", "2": "Previzualiz<PERSON>ri proiecte lansate: Vezi cu un clic ce este înăuntru — fără a mai deschide fiecare proiect.", "3": "Urmărirea costurilor pentru proiectele duplicate a fost corectată — acum pornesc de la zero.", "4": "Estimarea costurilor în timp real după fiecare prompt.", "5": "Istoricul proiectului și funcția de rollback funcționează din nou fără probleme.", "6": "Scroll automat îmbunătățit — fără blocaje în timpul generării.", "7": "Paginile de meniu se deschid acum în tab-uri noi — astfel încât să nu pierzi munca curentă."}}, "v2.0.4": {"date": "27 mai 2025", "changes": {"0": "Tablou de bord: <PERSON><PERSON><PERSON> de ecran nu mai derulează automat. Derularea este controlată de utilizator, iar încărcarea este animată."}}, "v2.0.3": {"date": "20 mai 2025", "changes": {"0": "Content Studio: Animația robotului este acum redată la dimensiune completă.", "1": "Affiliate: <PERSON><PERSON>uri vizuale pentru dashboard-ul afiliaților în Firefox."}}, "v2.0.2": {"date": "19 mai 2025", "changes": {"0": "Content Studio: Numărul maxim de fișiere care pot fi încărcate simultan a crescut de la 10 la 50."}}, "v2.0.1": {"date": "18 mai 2025", "changes": {"0": "Content Studio: Etichetele multiple nu se actualizau fără refresh — acum funcționează corect.", "1": "Butonul de editare al unei imagini selectate este acum mai vizibil.", "2": "La încărcare, folderul selectat este automat ultimul creat sau selectat.", "3": "Folderul selectat se păstrează la drag and drop.", "4": "Titlul tab-ului are acum o culoare nouă.", "5": "Butonul 'insert image' a fost redenumit 'utilizează în proiect'.", "6": "Butonul de încărcare are acum fundal verde.", "7": "Înălțimea barei de căutare a fost redusă pentru aliniere mai bună.", "8": "Bara de căutare nu este afișată dacă nu există imagini pentru utilizator."}}, "v2.0.0": {"date": "16 mai 2025", "changes": {"0": "🚀 ACTUALIZĂRI MAJORE PE BIELA.DEV! 🚀", "1": "Tehnologia noastră proprie WebContainer — nu mai depindem de furnizori externi.", "2": "Mai multe modele AI pentru generarea codului: suport pentru Gemini și altele.", "3": "Content Studio: <PERSON><PERSON><PERSON> poți încărca imaginile proprii pentru utilizare în proiecte.", "4": "Partajarea proiectelor: colaborează cu alți utilizatori.", "5": "Conectare la proiecte existente Supabase: mai multe proiecte Biela pot folosi aceeași bază de date."}}, "v1.0.5": {"date": "15 mai 2025", "changes": {"0": "Plăți: A fost implementat procesatorul Stripe.", "1": "Content Studio: Dacă nu există nicio imagine încărcată, este afișată o animație (robot)."}}, "v1.0.4": {"date": "13 mai 2025", "changes": {"0": "Content Studio: Problemele de paginare au fost rezolvate."}}, "v1.0.3": {"date": "5 mai 2025", "changes": {"0": "Lansarea Content Studio — un loc pentru gestionarea media.", "1": "Frontoffice: S-a adăugat schema.org pentru aplicații software.", "2": "Dashboard-ul echipei: not<PERSON><PERSON><PERSON> (punct ro<PERSON>u) când se alătură un membru nou sau apare un mesaj.", "3": "IDE: A fost remediată o eroare unde ferestrele nu se închideau corect la clic în WebContainer."}}, "v1.0.2": {"date": "2 mai 2025", "changes": {"0": "Affiliate: Redesign complet pentru zona membrilor echipei.", "1": "Dashboard-ul afiliaților are acum pictograme actualizate.", "2": "IDE: Butonul 'Supabase Connect' a fost redenumit 'Bază de date' și afișează 'Bază conectată' în albastru."}}, "v1.0.1": {"date": "1 mai 2025", "changes": {"0": "IDE (tabul Setări): Integrare cu baza de date în chat-ul de setări.", "1": "Dashboard: A fost rezolvată problema de paginare în interfața utilizatorului."}}, "v1.0.0": {"date": "18 aprilie 2025", "changes": {"0": "🚀 Bine ai venit pe Biela.dev: Partenerul tău AI pentru dezvoltare web! 🚀", "1": "Suntem încântați să anunțăm lansarea publică oficială a Biela.dev! Platforma este gratuită pentru toată lumea, încă din prima zi."}}}}}