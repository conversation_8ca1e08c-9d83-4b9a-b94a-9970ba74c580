{"meta": {"home": {"title": "biela.dev | Constructor Web & App bazat pe AI – Creează cu ajutorul comenzilor", "description": "Transformă-ți ideile în site-uri sau aplicații live cu biela.dev. <PERSON><PERSON><PERSON><PERSON><PERSON> comenzi bazate pe AI pentru a crea produse digitale personalizate cu ușurință."}, "privacy": {"title": "Politica de Confidențialitate biela.dev", "description": "Află cum colectează, utilizează și protejează biela.dev informațiile tale personale."}, "terms": {"title": "Termeni și Condiții biela.dev", "description": "Consultă termenii și condițiile de utilizare ale platformei de dezvoltare bazate pe AI de la biela.dev."}, "changelog": {"title": "Jurnalul schimbărilor biela.dev - Întotdeauna. Evoluție. Înainte.", "description": "Află cum biela.dev se îmbunătățește cu fiecare versiune"}, "competitionterms": {"title": "Termeni și Condiții – Concursul de Dezvoltare Biela", "description": "Participă la Concursul de Dezvoltare organizat de Biela.dev și TeachMeCode Institute – o provocare oficială de o lună. Prezintă-ți proiectele digitale, obține recunoaștere prin implicarea în comunitate și concurează pentru premii de top. Data limită de înscriere: 14 mai 2025."}, "contest": {"title": "Concursul de Dezvoltare biela.dev 2025 – Câștigă $10.000", "description": "Participă la concursul biela.dev, prezintă proiecte construite cu AI și câștigă premii în bani."}, "howTo": {"title": "Cum să folosești Biela.dev – Ghid complet pentru crearea de aplicații cu inteligență artificială", "description": "Descoperă cum să folosești Biela.dev pentru a construi rapid aplicații web, fără să scrii cod. Un ghid complet, pas cu pas, cu tutoriale și sfaturi practice.", "keywords": "biela, biela.dev, cum să folose<PERSON> biela, creează aplicații AI, f<PERSON><PERSON><PERSON> cod, dezvolta<PERSON> rapid<PERSON>, g<PERSON>d biela, tutorial biela", "ogTitle": "Cum să folosești Biela.dev – Ghid pas cu pas", "ogDescription": "Învață să creezi aplicații AI cu Biela.dev. De la idee la lansare, fără să scrii cod.", "twitterTitle": "Cum să folosești Biela.dev – G<PERSON>d complet", "twitterDescription": "Construiește aplicații AI pe Biela.dev fără cod. Urmează acest ghid pas cu pas!"}}, "navbar": {"home": "<PERSON><PERSON><PERSON>", "demo": "Demo", "community": "Comunitate", "affiliate": "<PERSON><PERSON><PERSON><PERSON>", "partners": "Partener<PERSON>", "contest": "Vibe Coding Hackathon", "subscriptions": "Abonamente", "pricing": "<PERSON><PERSON><PERSON>"}, "counter": {"days": "Zile", "hours": "Ore", "minutes": "Minute", "seconds": "Secunde", "centisec": "Centisecunde"}, "hero": {"title": "Transforma-ti ideea intr-un site sau aplicatie live in cateva minute", "subtitle": "Daca iti poti <1>imagina</1>, il poti <5>programa</5>.", "subtitle2": "Pornește gratuit. Codează tot. Transformă-ți abilitățile în fiecare cerere într-o oportunitate.", "timeRunningOut": "Timpul se scurge!", "launchDate": "<PERSON><PERSON><PERSON><PERSON>", "experimentalVersion": "Lansare Beta pe 15 aprilie 2025", "earlyAdopter": "Inscrie-te acum pentru a beneficia de avantajele early adopter inainte de lansarea oficiala", "tryFree": "Incearca G<PERSON>", "seeAiAction": "Vezi IA in actiune", "tellBiela": "Spune lui Biela sa creeze", "inputPlaceholder": "Dacă îți poți imagina, BIELA o poate coda. Ce facem astăzi?", "chat": "Cha<PERSON>", "code": "Cod", "checklist": "Lista de verificare", "checklistTitle": "Foloseste liste de verificare pentru a", "checklistItems": {"trackDevelopment": "Urmareste sarcinile de dezvoltare", "setRequirements": "Stabileste cerintele proiectului", "createTesting": "Creeaza protocoale de testare"}, "registerNow": "Inregistreaza-te acum", "attachFiles": "Ataseaza fisiere la promptul tau", "voiceInput": "Foloseste intrare vocala", "enhancePrompt": "Imbunatateste promptul", "cleanupProject": "Curata proiectul", "suggestions": {"weatherDashboard": "Creeaza un panou meteo", "ecommercePlatform": "Construieste o platforma de e-commerce", "socialMediaApp": "Proiecteaza o aplicatie de social media", "portfolioWebsite": "Genereaza un site de portofoliu", "taskManagementApp": "Creeaza o aplicatie de management al sarcinilor", "fitnessTracker": "Construieste un tracker de fitness", "recipeSharingPlatform": "Proiecteaza o platforma pentru partajarea retetelor", "travelBookingSite": "Creeaza un site de rezervari de calatorii", "learningPlatform": "Construieste o platforma de invatare", "musicStreamingApp": "Proiecteaza o aplicatie de streaming muzical", "realEstateListing": "<PERSON><PERSON>az<PERSON> un anunt imobiliar", "jobBoard": "Construieste un site de joburi"}}, "videoGallery": {"barber": {"title": "Site pentru frizerie", "description": "Vezi cum Biela.dev creeaza un site complet pentru frizerie in cateva minute"}, "tictactoe": {"title": "Jocul X si O", "description": "Priveste cum IA creeaza un joc de X si O mai repede decat poti spune '<PERSON><PERSON>'"}, "coffeeShop": {"title": "Site pentru cafenea", "description": "Vezi cum Biela.dev creeaza un site pentru o cafenea"}, "electronicsStore": {"title": "Site pentru magazin de electronice", "description": "Vezi cum Biela.dev creeaza un magazin online complet in cateva minute"}, "seoAgency": {"title": "Site pentru agentie SEO", "description": "Priveste cum IA creeaza un site pentru o agentie SEO"}}, "telegram": {"title": "Alatura-te canalului nostru Telegram", "subtitle": "Conecteaza-te cu comunitatea Biela", "description": "Primeste suport instant, impartaseste-ti proiectele si conecteaza-te cu entuziasti de IA din intreaga lume. <br/><br/>Comunitatea noastra de pe Telegram este cea mai rapida modalitate de a ramane la curent cu Biela.dev.", "stats": {"members": "<PERSON><PERSON><PERSON>", "support": "Suport", "countries": "<PERSON><PERSON>", "projects": "Proiecte"}, "joinButton": "Alatura-te comunitatii noastre de pe Telegram", "communityTitle": "Comunitatea Biela.dev", "membersCount": "membri", "onlineCount": "online acum", "messages": {"1": "Tocmai mi-am lansat site-ul de e-commerce cu Biela.dev in mai putin de o ora!", "2": "Arata uimitor! Cum ai gestionat catalogul de produse?", "3": "Biela s-a ocupat de asta automat! Am descris doar ce mi-am dorit.", "4": "Construiesc acum un site de portofoliu. Sugestiile IA sunt incredibile!", "5": "A incercat cineva noile template-uri responsive? Arata fantastic pe mobil."}}, "joinNetwork": {"title": "Alatura-te retelei noastre", "subtitle": "Conectează-te. Crește. Câștigă.", "affiliateProgram": "Program de Afiliere", "registerBring": "Inregistreaza-te si adu 3 afiliati", "aiCourse": "Curs de Inginerie IA", "courseDescription": "Curs cuprinzator de IA gratuit cu TeachMeCode", "digitalBook": "Carte Digitala", "bookDescription": "Editie digitala exclusiva a 'The Art of Prompt Engineering'", "exclusiveBenefits": "Beneficii Exclusive", "benefitsDescription": "Castiguri pe viata si privilegii speciale pentru membri", "registerNow": "Inregistreaza-te acum", "teamEarnings": "<PERSON><PERSON><PERSON><PERSON>", "buildNetwork": "Construieste-ti reteaua", "weeklyMentorship": "Mentorat saptamanal", "teamBonuses": "<PERSON><PERSON>", "startEarning": "Incepe sa castigi astazi", "upToCommission": "PANA LA Comision"}, "partners": {"title": "Incredintat de lideri din industrie", "subtitle": "Colaboram cu inovatori globali pentru a contura viitorul dezvoltarii IA", "strategicPartnerships": "Parteneriate Strategice", "joinNetwork": "Alatura-te retelei tot mai mari de parteneri si companii care transforma dezvoltarea cu BIELA"}, "demo": {"title": "Priveste cum IA construieste aplicatii si site-uri in timp real", "subtitle": "Descopera cum companiile isi transforma prezenta digitala cu Biela.dev", "seeInAction": "Vezi Biela.dev in actiune", "whatKind": "Ce tip de site iti trebuie?", "describeWebsite": "Descrie-ti ideea de site (de exemplu, 'Un portofoliu pentru un fotograf')", "generatePreview": "Genereaza previzualizare", "nextDemo": "De<PERSON> urmator", "startBuilding": "Incepe sa construiesti si sa castigi cu Biela.dev astazi"}, "footer": {"quickLinks": {"title": "<PERSON>uri rapide", "register": "Inregistreaza-te", "bielaInAction": "Vezi Biela.dev in actiune", "liveOnTelegram": "Suntem live pe Telegram", "affiliate": "Marketing afiliat colaborativ", "partners": "Partener<PERSON>"}, "legal": {"title": "Legal", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. Toate drepturile rezervate.", "unauthorized": "<PERSON><PERSON><PERSON><PERSON>, reproducerea sau distribuirea neautorizata a acestui serviciu, in totalitate sau partial, fara permisiune scrisa explicita este strict interzisa."}, "reservations": "TeachMeCode Institute isi rezerva toate drepturile legale asupra proprietatii intelectuale asociate Biela."}, "bottomBar": {"left": "2025 Biela.dev. Alimentat de %s © Toate drepturile rezervate.", "allRights": "Toate drepturile rezervate.", "right": "Dezvoltat de %s"}}, "common": {"loading": "Se incarca...", "error": "A aparut o eroare", "next": "Urmatorul", "previous": "Anteriorul", "submit": "Trimite", "cancel": "Anuleaza", "close": "<PERSON><PERSON><PERSON>", "loginNow": "Conectează-te acum", "verifyAccount": "Verifică contul"}, "languageSwitcher": {"language": "Limba"}, "contest": {"bielaDevelopment": "Vibe Coding Hackathon", "competition": "COMPETITIE", "firstPlace": "LOCUL 1", "secondPlace": "LOCUL 2", "thirdPlace": "LOCUL 3", "currentLeader": "Lider actual:", "footerText": "Dezvoltare asistată de AI de către Biela.dev, susținută de Institutul TeachMeCode®", "browseHackathonSubmissions": "Răsfoiți înscrierile la Hackathon", "winnersAnnouncement": "🎉 Câștigătorii Concursului Anunțați! 🎉", "congratulationsMessage": "Felicitări câștigătorilor noștri uimitori! Mulțumim tuturor celor care au participat la acest hackathon incredibil.", "conditionActiveReferrals": "Are 3 recomandări active", "conditionLikedProject": "A apreciat un alt proiect", "winner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "qualificationsMet": "Calificări:"}, "competition": {"header": {"mainTitle": "Propunerea ta. Stilul tau. Terenul tau de joaca.", "timelineTitle": "PROGRAM", "timelineDesc": "O lună pentru a crea cel mai bun proiect generat de AI", "eligibilityTitle": "ELEGIBILITATE", "eligibilityDesc": "Conturi active Biela.dev care trimit un proiect", "prizesTitle": "PREMII", "prizesDesc": "16.000 $ în premii în numerar plus acces gratuit pentru c<PERSON>știgători", "votingTitle": "VOTARE", "votingDesc": "Conduse de comunitate, un vot per utilizator verificat", "tagline": "Lasă-ți creativitatea să vorbească. Lasă lumea să <PERSON>ze."}, "details": {"howToEnter": "Cum sa Participi", "enter": {"step1": {"title": "Creează un proiect folosind AI-ul de la Biela.dev", "desc": "Folosește uneltele puternice de AI de la Biela pentru a-ți construi proiectul inovator"}, "step2": {"title": "Accesează panoul tău de utilizator", "desc": "Face<PERSON><PERSON> clic pe \"Publicați pentru Hackathon\" lângă proiectul dvs.", "subdesc": "Poți trimite până la 3 proiecte diferite"}, "step3": {"title": "Biela îți va procesa înscrierea:", "list1": "Fă o captură de ecran a proiectului tău", "list2": "Verifică-ți solicitările pentru eligibilitate", "list3": "Generează un rezumat și o descriere"}}, "prizeStructure": "Structura premiilor", "prizes": {"firstPlace": "Locul I", "firstPlaceValue": "$10,000", "secondPlace": "Locul II", "secondPlaceValue": "$5,000", "thirdPlace": "Locul III", "thirdPlaceValue": "$1,000", "fourthToTenth": "Locul IV–X", "fourthToTenthValue": "30 de zile acces nelimitat"}}, "qualification": {"heading": "Cerinte de calificare", "description": "Pentru a te califica pentru cele 3 premii de top (10.000 $, 5.000 $ și 1.000 $), trebuie să îndeplinești următoarele criterii:", "criteria1": "Să ai cel puțin 3 referințe active", "criteria2": "Să dai like la cel puțin un proiect de pe peretele de expoziție", "proTipLabel": "Sfat profesionist:", "proTipDesc": "Cu cât îți trimiți proiectul mai devreme, cu atât va primi mai multă vizibilitate și mai multe voturi!"}, "voting": {"heading": "Instructiuni de vot", "oneVotePolicyTitle": "Politica de un singur vot", "oneVotePolicyDesc": "Poți vota o singură dată și nu poți vota propriul proiect", "accountVerificationTitle": "Verificarea contului", "accountVerificationDesc": "Doar utilizatorii cu conturi verificate Biela.dev pot vota", "tieBreakingTitle": "Soluționarea egalităților", "tieBreakingDesc": "În caz de egalitate, factorul decisiv va fi numărul total de stele obținute de creatorul proiectului (folosite sau nefolosite)", "howToVoteTitle": "Cum sa <PERSON>zi", "howToVoteDesc": "Parcurge peretele de expoziție, explorează toate proiectele publicate generate de AI și apasă pe iconița inimii pentru a vota proiectul tău preferat!"}}, "contestItems": {"projectShowcase": "Expoziția Proiectelor", "loading": "Se încarcă...", "createBy": "<PERSON><PERSON><PERSON> de ", "viewProject": "Vezi proiectul", "stars": "Stele", "overview": "prezentare generală", "keyFeatures": "Caracteristici cheie", "exploreLiveProject": "Explorează proiectul live", "by": "de", "likeLimitReached": "Limită de aprecieri atinsă", "youCanLikeMaximumOneProject": "Poți aprecia maximum un proiect. Dorești să anulezi aprecierea actuală și să apreciezi acest proiect în schimb?", "currentlyLikedProject": "Proiect apreciat în prezent", "switchMyLike": "Schimbă aprecierea mea", "mustLoggedIntoLikeProject": "Trebuie să fii autentificat pentru a aprecia un proiect.", "signInToBielaAccountToVote": "Te rugăm să te autentifici în contul tău Biela.dev pentru a participa la procesul de votare.", "yourAccountIsNotVerifiedYet": "Contul tău nu a fost încă verificat. Te rugăm să-ți verifici contul pentru a putea aprecia un proiect.", "accountVerificationEnsureFairVoting": "Verificarea contului asigură un vot corect și ajută la menținerea integrității competiției.", "projectsSubmitted": "Proiecte trimise:", "unlikeThisProject": "Nu mai îmi place acest proiect", "likeThisProject": "Îmi place acest proiect", "likes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "like": "apreciere", "somethingWentWrong": "Ceva a mers prost. <PERSON><PERSON> rugăm să încercați din nou mai târziu", "voteError": "<PERSON><PERSON><PERSON> de vot"}, "textsLiveYoutube": {"first": {"title": "Ești invitat!", "description": "Facem live vineri — această sesiune e despre construirea cu <green>scop</green>, <blue>precizie</blue> și <orange>vibe pur</orange>.", "secondDescription": "Află cum să <green>stăpânești vibe coding</green>, <blue>să descoperi funcții noi</blue> înaintea tuturor și <orange>să vezi proiecte reale</orange> construindu-se live.", "specialText": "Dă click aici și apasă \"Notifică-mă\" pe YouTube — nu rata asta."}, "second": {"title": "Fii primul care construiește.", "description": "<PERSON><PERSON> ducem <green>vibe coding</green> la un nivel cu totul nou — îți arătăm cum să <blue>creezi cu intenție</blue>, <orange>deblochezi funcții noi</orange> și s<PERSON> la<PERSON>zi <green>mai rapid ca niciodată</green>.", "secondDescription": "<green>Build-uri noi</green>, <blue>idei noi</blue> și <orange>oportunități noi</orange> de creștere.", "specialText": "Dă click aici pentru a seta memento-ul YouTube — și fii parte din valul următor de creatori."}, "third": {"title": "Rezervă-ți locul.", "description": "Următorul nostru <green>livestream Biela.dev</green> are loc vineri — și ești <blue>oficial invitat</blue>.", "secondDescription": "Ne adâncim în <green>vibe coding</green>, dez<PERSON><PERSON><PERSON><PERSON> <blue>funcții noi puternice</blue> și te ajutăm să construiești <orange>mai inteligent, mai rapid și la scară mai mare</orange>.", "specialText": "Dă click aici și apasă \"Notifică-mă\" pe YouTube — următoarea ta mare idee poate începe aici."}}, "stream": {"title": "<PERSON>i <1><PERSON><PERSON><PERSON></1> care <3><PERSON>st<PERSON><PERSON><PERSON><PERSON></3>.", "subtitle": "Urmărește livestream-ul nostru de vineri — funcționalități, proiecte și actualizări exclusive.", "button": "Setează-ți memento-ul pe YouTube."}, "billings": {"modalTitle": "Alege un Plan", "modalSubtitle": "Alege dintre cele trei planuri flexibile pentru a continua să scrii cod fără limite.", "toggleMonthly": "Lunar", "toggleYearly": "<PERSON><PERSON> (economisește 10%)", "mostPopular": "Cel Mai <PERSON>", "tokensLowerCase": "<PERSON><PERSON>", "tokensUpperCase": "<PERSON><PERSON><PERSON>", "below": "mai jos", "unlimited": "NELIMITAT", "10Bonus": "Bonus 10%", "currentPlan": "<PERSON><PERSON>nt", "upgradePlan": "Upgrade Plan", "purchaseDetails": "Detalii Achiziție", "dateLabel": "Dată", "planLabel": "Plan", "amountLabel": "Sumă", "whatsNext": "Ce Urmează", "yourTokensAvailable": "Tokenurile tale sunt acum disponibile în cont", "purchaseDetailsStored": "Detaliile achiziției au fost salvate în contul tău", "viewPurchaseHistory": "Poți vizualiza istoricul achizițiilor în secțiunea de facturare", "thankYou": "Mulțumim!", "purchaseSuccessful": "Achiziția a fost realizată cu succes", "startCoding": "Începe să scrii cod", "month": "lună", "year": "an"}, "feature": {"basic_ai": "Dezvoltare AI de bază", "community_support": "Suport comunitar", "standard_response": "Timp standard de răspuns", "basic_templates": "Șabloane de bază", "advanced_ai": "Dezvoltare AI avansată", "priority_support": "Suport prioritar", "fast_response": "Timp rapid de răspuns", "premium_templates": "Șabloane premium", "team_collaboration": "Colaborare în echipă", "enterprise_ai": "Dezvoltare AI pentru companii", "support_24_7": "Suport prioritar 24/7", "instant_response": "<PERSON><PERSON> ră<PERSON>", "custom_templates": "Șabloane personalizate", "advanced_team": "Funcționalități avansate pentru echipă", "custom_integrations": "Integrări personalizate", "big_context_window": "Fereastră mare de context - de 5 ori mai mare decât cea medie", "code_chat_ai": "Acces la AI pentru cod și chat", "tokens_basic": "Tokenuri suficiente pentru aprox. 3 site-uri de prezentare", "standard_webcontainer": "Webcontainer standard dedicat", "medium_context_window": "Fereastră medie de context - potrivită pentru un site, un blog sau un joc browser de bază", "basic_support": "Suport de bază cu timp standard de răspuns", "supabase_integration": "Integrare Supabase", "project_sharing": "Partajează proiectele cu prietenii tăi", "project_download": "Descar<PERSON><PERSON> proiectele tale", "project_deployment": "Funcționalitate de implementare", "custom_domain": "Conectează proiectele la propriul tău domeniu", "tokens_creator_plus": "Tokenuri suficiente pentru aprox. 7 site-uri de prezentare", "advanced_support": "Suport avansat cu timp de răspuns mai rapid", "prioritary_support": "Suport prioritar dedicat", "early_feature_access": "Acces timpuriu la funcționalități noi", "human_cto": "CTO uman dedicat a<PERSON><PERSON>i tale", "community_promotion": "Promovează-ți afacerea prin comunitatea noastră"}, "JoinUsLive": "Alătură-te live", "howToUseBiela": {"subtitle": "Cum să folosești Biela", "title": "Explicat în câteva minute", "description": "Explicații scurte despre cum funcționează fiecare caracteristică"}, "slides": {"newHistoryFeature": "Descoperă noua funcționalitate ISTORIC din Biela!", "newHistoryFeatureDescription": "Fila ISTORIC menține procesul tău de Vibe Coding fluid, permițându-ți să revii și să restaurezi orice versiune anterioară a proiectului, astfel încât să rămâi mereu în control!", "fixPreview": "Cum repari previzualizarea în Biela!", "fixPreviewDescription": "Urmărește ghidul complet și asigură-te că totul apare exact așa cum ai construit.", "signUp": "Cum te înregistrezi pe Biela!", "signUpDescription": "Nou pe biela.dev? Urmărește acest ghid rapid pentru a învăța cum să te înregistrezi și să îți verifici contul!", "buildWebsite": "Cum creezi un site web cu Biela!", "buildWebsiteDescription": "Învață cum să creezi și să lansezi un site web de la zero folosind biela.dev — fără să scrii nici măcar o linie de cod.", "downloadAndImport": "Cum descarci și imporți proiecte și conversații în Biela!", "downloadAndImportDescription": "În acest ghid pas cu pas, vei învăța cum să îți descarci proiectele finalizate și cum să imporți conversațiile salvate în spațiul tău de lucru — astfel încât să poți continua exact de unde ai rămas și să menții fluxul de Vibe Coding oricând.", "shareProject": "Cum partajezi proiectul tău BIELA Vibe Code!", "shareProjectDescription": "Ești gata să îți partajezi proiectul ca un profesionist? Urmărește ghidul complet și începe acum.", "launchProject": "Cum îți lansezi proiectul pe propriul domeniu!", "launchProjectDescription": "În acest videoclip, parcurgem pașii simpli pentru a-ți face site-ul live — de la configurare până la publicare finală, ai tot ce îți trebuie pentru a trece cu încredere de la construire la lansare.", "deployProject": "Cum îți publici proiectul și îl faci live!", "deployProjectDescription": "Acest ghid rapid te conduce prin toți pașii pentru a-ți publica rapid și lin site-ul, aplicația sau dashboard-ul."}, "maintenance": {"title": "Lucrăm la rezolvarea problemei", "description": "Biela.dev este în modul de mentenanță. Lucrăm la rezolvarea problemei", "text": "<PERSON>ă mulțumim pentru asteptare."}, "planCard": {"descriptionBeginner": "Perfect pentru începători care își construiesc primele proiecte și învață dezvoltare web", "descriptionCreator": "Ideal pentru creatori care au nevoie de mai multă putere și flexibilitate pentru proiecte avansate", "descriptionProfessional": "Soluție completă pentru dezvoltatori profesioniști și echipe care construiesc aplicații", "perMillionTokens": "$ {{price}} / pe milion de tokenuri", "dedicatedCto": "CTO dedicat - 24 de ore", "tokens": "<PERSON><PERSON><PERSON>", "perMonth": "pe lună", "perYear": "pe an", "freeTokens": "{{ tokens }} <PERSON><PERSON><PERSON> gratuite", "monthly": "Lunar", "yearly": "<PERSON><PERSON>", "save": "ECONOMISEȘTE 10%"}, "faqLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fre<PERSON>", "faqHeading": "Vezi cele mai frecvente întrebari", "faqSubheading": "Începe. Înțelege. Explorează", "whatAreTokensQuestion": "Ce sunt tokenii?", "whatAreTokensAnswer1": "Tokenii sunt modul în care IA procesează proiectul tău. Pe Biela.dev, tokenii sunt folosiți atunci când trimiți un prompt către IA. În acest proces, fișierele proiectului sunt incluse în mesaj pentru ca IA să poată înțelege codul tău. Cu cât proiectul este mai mare și răspunsul mai lung, cu atât mai mulți tokeni sunt necesari per mesaj.", "whatAreTokensAnswer2": "În plus, un număr mic de tokeni este dedus din contul tău pentru fiecare minut în care utilizezi un webcontainer, pentru a acoperi costul mediului de dezvoltare.", "freePlanTokensQuestion": "Cati tokeni primesc în planul gratuit?", "freePlanTokensAnswer": "Fiecare utilizator al planului gratuit primește 200.000 de tokeni pe zi, care se reîmprospătează zilnic.", "outOfTokensQuestion": "Ce se întampla daca raman fara tokeni?", "outOfTokensAnswer": "Nicio grijă! Poți oricând merge la Meniu → Facturare și să cumperi instant mai mulți tokeni dacă ai nevoie.", "changePlanQuestion": "Pot schimba planul mai tarziu?", "changePlanAnswer": "Da — oricând. Accesează Meniu → Facturare pentru a face upgrade, downgrade sau a schimba planul în funcție de nevoile tale.", "rolloverQuestion": "Tokenii nefolositi se reporteaza?", "rolloverAnswer": "<PERSON><PERSON>, tokenii se resetează la data de expirare și nu se reportează — așa că asigură-te că îi folosești zilnic!", "exploreHowToUseBiela": "Explorează cum să folosești Biela", "new": "Nou", "joinVibeCoding": "Participă la Hackathon-ul Vibe Coding", "whatDoYouWantToBuild": "CE VREI Sa CONSTRUIESTI?", "imaginePromptCreate": "Imaginează-ți. Propune. Creează.", "levelOfAmbition": "Care este nivelul tau de ambitie?", "startGrowScale": "Începe. Crește. Scalează.", "calloutBar": {"start": "Toate planurile <PERSON><PERSON><PERSON> gratuit cu", "tokens": "200.000 tokeni pe zi", "end": ". Fără fricțiuni la înregistrare. Doar începe s<PERSON> c<PERSON>zi."}, "atPriceOf": "la prețul de", "startFreeNoCreditCard": "<PERSON><PERSON><PERSON> Fără Card de Credit", "SignIn": "Autentificare", "Register": "Înregistrare", "Affiliates": "Afiliere", "UsernameRequired": "Numele de utilizator este obligatoriu", "PasswordRequired": "Parola este obligatorie", "MissingUsernamePasswordOrTurnstile": "Lipsește numele de utilizator, parola sau tokenul Turnstile", "LoginSuccess": "Autentificare reușită!", "LoginFailed": "Autentificare eșuată", "EmailRequired": "Emailul este obligatoriu", "EmailInvalid": "Te rugăm să introduci o adresă de email validă", "CaptchaRequired": "Te rugăm s<PERSON>zi CAPTCHA", "ResetLinkSent": "Un link de resetare a fost trimis către adresa ta de email.", "ResetFailed": "Trimiterea linkului de resetare a eșuat", "BackToLogin": "Înapoi la autentificare", "EmailPlaceholder": "Emailul tău", "SendResetLink": "Trimite linkul de resetare", "loading": "Se încarcă", "checkingYourAuthenticity": "Verificăm autenticitatea ta", "ToUseBielaDev": "Pentru a folosi Biela.dev trebuie să te autentifici într-un cont existent sau să creezi unul folosind una dintre opțiunile de mai jos", "Sign in with Google": "Autentifică-te cu Google", "Sign in with GitHub": "Autentifică-te cu GitHub", "Sign in with Email and Password": "Autentifică-te cu email și parolă", "DontHaveAnAccount": "Nu ai un cont?", "SignUp": "Înregistrează-te", "ByUsingBielaDev": "Prin utilizarea Biela.dev îți dai acordul pentru colectarea datelor de utilizare.", "EmailOrUsernamePlaceholder": "Email/Nume utilizator", "passwordPlaceholder": "Pa<PERSON><PERSON>", "login.loading": "Se încarcă", "LoginInToProfile": "Autentifică-te în profilul tău", "login.back": "Înapoi", "forgotPassword": "Ai uitat parola?", "PasswordsMismatch": "Parolele nu se potrivesc.", "PhoneRequired": "Numărul de telefon este obligatoriu", "ConfirmPasswordRequired": "Te rugăm s<PERSON>i parola", "AcceptTermsRequired": "Trebuie să accepți Termenii și Condițiile și Politica de Confidențialitate", "RegistrationFailed": "Înregistrarea a eșuat", "EmailConfirmationSent": "Confirmarea prin email a fost trimisă. Te rugăm să îți confirmi emailul și apoi să te autentifici.", "RegistrationServerError": "Înregistrarea a eșuat (serverul a returnat false).", "SomethingWentWrong": "Ceva nu a funcționat", "CheckEmailConfirmRegistration": "Verifică-ți emailul pentru a confirma înregistrarea", "EmailConfirmationSentText": "Ți-am trimis un email cu un link de confirmare.", "LoginToProfile": "Autentificare în profil", "username": "Nume utilizator", "email": "Email", "PasswordPlaceholder": "Pa<PERSON><PERSON>", "ConfirmPasswordPlaceholder": "Confirmă parola", "AcceptTermsPrefix": "Sunt de acord cu", "TermsOfService": "Termenii și Condițiile", "AndSeparator": "și", "PrivacyPolicy": "Politica de Confidențialitate", "CreateAccount": "Creează cont", "Back": "Înapoi", "SignInWithGoogle": "Autentifică-te cu Google", "SignInWithGitHub": "Autentifică-te cu GitHub", "SignInWithEmailAndPassword": "Autentifică-te cu email și parolă", "translation": {"AIModel": "Model AI", "UpgradePlan": "un plan premium", "PremiumBadge": "PREMIUM", "Active": "Activ", "projectInfo.features": "Funcționalități", "Stats": "Statistici", "Performance": "Performanță", "Cost": "Cost", "UpgradeTooltip": "Deblochează prin upgrade la ", "UpgradeTooltipSuffix": "pachet", "chat": "Cha<PERSON>", "code": "Cod"}, "extendedThinkingTooltip": "Permite AI-ului să gândească mai profund înainte de a răspunde", "extendedThinkingTooltipDisabled": "Dezactivează gândirea extinsă pentru a economisi resurse", "AIModel": "Model AI", "UpgradePlan": "un plan premium", "PremiumBadge": "PREMIUM", "Active": "Activ", "projectInfo.features": "Funcționalități", "Stats": "Statistici", "Performance": "Performanță", "Cost": "Cost", "UpgradeTooltip": "Deblochează prin upgrade la ", "UpgradeTooltipSuffix": "pachet", "HowBielaWorks": "Cum functioneaza BIELA", "WatchPromptLaunch": "Urmărește. Scrie. Lansează.", "SearchVideos": "Caută în bibliotecă...", "NewReleased": "LANSARE NOUĂ", "Playing": "Redare", "NoVideosFound": "Nu au fost găsite videoclipuri", "TryAdjustingYourSearchTerms": "Încearcă să modifici termenii de căutare", "feedback": {"title": {"issue": "Raportează o problemă", "feature": "Solicită o funcționalitate"}, "description": {"issue": "Ai găsit un bug sau întâmpini o problemă? Spune-ne și o vom rezolva cât mai curând posibil.", "feature": "Ai o idee pentru o nouă funcționalitate? Ne-ar plăcea să aflăm sugestiile tale pentru a îmbunătăți platforma noastră."}, "success": {"title": "Mulțumim!", "issue": "Raportul tău a fost trimis cu succes.", "feature": "Solicitarea ta de funcționalitate a fost trimisă cu succes."}, "form": {"fullName": {"label": "Nume complet", "placeholder": "Introdu numele complet"}, "email": {"label": "Adresă de email", "placeholder": "Introdu adresa de email"}, "suggestion": {"labelIssue": "Descrierea problemei", "labelFeature": "Sugestie de funcționalitate", "placeholderIssue": "Descrie problema pe care o întâmpini...", "placeholderFeature": "Descrie funcționalitatea pe care ai dori să o vezi..."}, "screenshots": {"label": "<PERSON><PERSON> (opțional)", "note": "Adaugă capturi de ecran pentru a ne ajuta să înțelegem mai bine problema", "drop": "Apasă pentru a încărca sau trage fișierele aici", "hint": "PNG, JPG, JPEG de până la 10MB fiecare", "count": "{{count}} imagine{{count > 1 ? 'e' : ''}} selectată(e)"}}, "buttons": {"cancel": "Anulează", "submitting": "Se trimite...", "submitIssue": "Trimite problema", "submitFeature": "Trimite solicitarea"}, "errors": {"fullNameRequired": "Numele complet este obligatoriu", "fullNameNoNumbers": "Numele complet nu poate conține numere", "emailRequired": "Adresa de email este obligatorie", "emailInvalid": "Te rog să introduci o adresă de email validă", "suggestionRequired": "Te rog să descrii problema sau sugestia ta"}}, "changelog": {"title": "Jurnal de modificari", "TotalReleases": "Lansări totale", "ActiveUsers": "Utilizatori activi", "FeaturesAdded": "Funcționalități adăugate", "BugsFixed": "Erori corectate", "description": "Mereu. Evoluând. Înainte", "totalReleases": "Total Lansări", "activeUsers": "Utilizatori Activi", "featuresAdded": "Funcționalități Adăugate", "bugsFixed": "Bug-uri <PERSON>", "shortCallText": "Aveți sugestii sau ați găsit un bug? Ne-ar plăcea să auzim de la dumneavoastră!", "reportIssue": "Raportează o Problemă", "requestFeature": "Solicitați o Funcționalitate", "types": {"feature": "Funcționalitate", "improvement": "Îmbunătățire", "bugfix": "<PERSON><PERSON><PERSON>", "ui/ux": "UI/UX", "announcement": "<PERSON><PERSON><PERSON>", "general": "General"}, "versions": {"v3.2.3": {"date": "26 iunie 2025", "changes": {"0": "Reparate câteva probleme UI/UX în dashboard-ul utilizatorului.", "1": "Îmbunătățită stabilitatea WebContainer.", "2": "Îmbunătățit modul AI Diff pentru a trata fișierele mici diferit față de fișierele mari."}}, "v3.2.2": {"date": "24 iunie 2025", "changes": {"0": "Reorganizat și reproiectat frontoffice-ul pentru o experiență mai bună a utilizatorului.", "1": "Implementată această pagină incredibilă de jurnal de modificări.", "2": "Îmbunătățit algoritmul de reconectare al WebContainer.", "3": "Adăugat suport pentru încărcarea proiectelor prin fișiere ZIP, pe lângă încărcările de foldere.", "4": "Îmbunătățită performanța tuturor apelurilor API prin utilizarea unei indexări mai bune."}}, "v3.2.1": {"date": "24 iunie 2025", "changes": {"0": "IDE: Secț<PERSON><PERSON> \"Foldere\" din Dashboard-ul Utilizatorului a fost reproiectată pentru o experiență îmbunătățită.", "1": "Content Studio: Acum puteți copia imagini în Content Studio."}}, "v3.2.0": {"date": "17 iunie 2025", "changes": {"0": "Modul <PERSON> (Experimental): AI-ul acum scrie doar diferențele între fișierul original și versiunea actualizată, în loc să regenereze întregul fișier. Aceasta îmbunătățește semnificativ performanța și optimizează utilizarea token-urilor. Puteți activa sau dezactiva această funcționalitate experimentală din zona Setări → Centrul de Control al proiectului dvs."}}, "v3.1.6": {"date": "12 iunie 2025", "changes": {"0": "IDE: A fost adăugat un câmp de cod cupon pentru abonamente și token-uri suplimentare."}}, "v3.1.5": {"date": "11 iunie 2025", "changes": {"0": "IDE: Au fost aplicate diverse corecții la fluxul \"Partajează un Proiect\"."}}, "v3.1.4": {"date": "10 iunie 2025", "changes": {"0": "IDE: Abonamentele de pachete au fost reverificate și corectate, abordând problemele de includere crypto și implementare Stripe."}}, "v3.1.3": {"date": "9 iunie 2025", "changes": {"0": "AI: Implementată și testată integrarea directă API pentru Gemini pentru a obține o latență mai bună."}}, "v3.1.2": {"date": "6 iunie 2025", "changes": {"0": "Afiliat: Implementate Sarcini de Plată Afiliat cu consistența designului cu modalul informativ al noului dashboard (istoricul plăților).", "1": "Content Studio: A fost adăugată o nouă opțiune pentru utilizatori să decidă dacă să trimită imagini către AI împreună cu link-urile. În mod implicit, aceasta este setată pe \"nu\"."}}, "v3.1.1": {"date": "5 iunie 2025", "changes": {"0": "Content Studio: Au fost făcute actualizări pentru a organiza Content Studio pe proiect."}}, "v3.1.0": {"date": "3 iunie 2025", "changes": {"0": "🚀 Actualizarea majoră BIELA.dev tocmai a sosit! 🚀", "1": "AI-ul acum scrie cod de 5 ori mai rapid decât în această dimineață. Da, ați citit corect. Am supraîncărcat performanța BIELA astfel încât să puteți trece de la idee la cod funcțional într-o clipă.", "2": "Terminal: Acum și mai bun: Bazându-ne pe redesignul din această dimineață, am adăugat o finisare UI/UX suplimentară pentru un flux de codare mai fluid.", "3": "Îmbunătățiri conexiune Supabase: O interfață mai curată și mai intuitivă face configurarea și integrarea fără efort.", "4": "Actualizare gestionare erori (Gestionare automată a erorilor): Raportarea automată a erorilor a fost retrasă. Pregătim o nouă setare astfel încât să puteți alege între gestionarea manuală și cea asistată de AI.", "5": "Noul Centru de Control (în Setările Proiectului) și Interfața de Testare Unitară: Prima funcționalitate: comutarea Terminalului de Testare Unitară. Multe alte controale vin!", "6": "Logica de descărcare îmbunătățită: Descărcările acum așteaptă până când codul dvs. este complet împins - asigurând exporturi complete și precise de fiecare dată."}}, "v3.0.0": {"date": "3 iunie 2025", "changes": {"0": "🚀 Actualizarea BIELA.dev - Tocmai a primit o creștere de putere! 🚀", "1": "UI Terminal renovat: Un design mai elegant și mai curat care face munca în terminal o plăcere.", "2": "Acțiuni NPM cu un clic: Rulați npm install, npm run build, sau npm run dev instantaneu cu noi butoane de acțiune - nu este nevoie să tastați! (funcționalitatea este integrată în terminal)", "3": "Gestionarea erorilor mai inteligentă și clasificarea culorilor erorilor: E<PERSON>rile de previzualizare sunt acum trimise automat către AI astfel încât să vă poată ajuta să depanați mai rapid. (veți experimenta repararea fluidă a bug-urilor de către BIELA în timp ce vă dezvoltați proiectele)", "4": "Navigarea fișierelor îmbunătățită: Făcând clic pe un nume de fișier în chat îl deschide acum direct în WebContainer. Doar atingeți și editați!"}}, "v2.1.1": {"date": "30 mai 2025", "changes": {"0": "IDE: A fost adăugat un buton de salvare pentru WebContainer, care apare când sunt făcute actualiz<PERSON>ri manuale, declanșând un commit și push către GitHub.", "1": "Dashboard: Reparată o eroare care apărea la redenumirea proiectelor importate din dashboard-ul utilizatorului.", "2": "WebContainer: A fost implementată o reparare urgentă pentru o scurgere de memorie."}}, "v2.1.0": {"date": "28 mai 2025", "changes": {"0": "🚀 Actualizarea BIELA.dev — 7 îmbunătățiri noi tocmai au sosit! 🚀", "1": "IDE: Am adăugat posibilitatea de a conecta propriul dvs. domeniu la proiectul dvs. biela, direct din interfață.", "2": "Previzualizări proiecte implementate: Acum puteți previzualiza proiectele dvs. implementate cu un singur clic pe pictograma ochiului - nu trebuie să deschideți fiecare pentru a ști ce este înăuntru.", "3": "Urmărirea costurilor reparată în proiectele duplicate: Am corectat o problemă unde proiectele duplicate moșteneau estimările de costuri de la proiectul original. Acum încep de la zero, oferindu-vă urmărire precisă per proiect.", "4": "Estimarea costurilor live (per prompt): Estimările de costuri se actualizează acum după fiecare prompt, nu doar o dată pe zi. Aceasta vă oferă feedback în timp real despre cât ar costa să dezvoltați același lucru cu o agenție de dezvoltare - și cât economisiți cu BIELA.", "5": "Tab-ul istoric și rollback reparat: Acum puteți reveni în siguranță în istoricul proiectului din nou. După actualizările noastre majore recente, aceasta avea probleme - acum este foarte fluid.", "6": "Îmbunătățiri autoscroll: Nu mai există ecrane înghețate în timp ce codați! Am reparat problema unde scroll-ul rămânea în urmă în timpul generării. Bucurați-vă de fluxuri fluide.", "7": "Paginile meniului se deschid acum în tab-uri noi: Dacă codați în Labs și faceți clic pe alte elemente de meniu, acestea se deschid acum într-un tab nou astfel încât să nu vă pierdeți niciodată munca în curs."}}, "v2.0.4": {"date": "27 mai 2025", "changes": {"0": "Dashboard: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> capturilor de ecran pe dashboard-ul utilizatorului includ eliminarea auto-scroll-ului, permiterea scroll-ului controlat de utilizator și adăugarea unei animații pentru încărcarea capturilor de ecran."}}, "v2.0.3": {"date": "20 mai 2025", "changes": {"0": "Content Studio: Animația robotului în Content Studio se afișează acum la dimensiune completă.", "1": "Afiliat: Reparate problemele de afișare în Dashboard-ul Afiliat când este vizualizat în Firefox."}}, "v2.0.2": {"date": "19 mai 2025", "changes": {"0": "Content Studio: <PERSON><PERSON><PERSON><PERSON><PERSON> maxim de fișiere permise deodată în Content Studio a fost crescut la 50 (de la 10)."}}, "v2.0.1": {"date": "18 mai 2025", "changes": {"0": "Content Studio: Reparată o problemă unde tag-urile pentru fișiere multiple nu se actualizau decât dacă pagina era reîmprospătată.", "1": "Content Studio: Butonul de editare la selectarea unei imagini este acum mai vizibil.", "2": "Content Studio: La încărcare, locația folderului selectează acum automat ultimul folder creat sau ultimul selectat.", "3": "Content Studio: Folderul selectat este acum păstrat la tragerea și plasarea imaginilor.", "4": "Content Studio: Tit<PERSON><PERSON> tab-ului folosește acum o nouă culoare (în loc de verde).", "5": "Content Studio: <PERSON><PERSON><PERSON> \"inserează imagine\" a fost redenumit în \"folosește în proiect\".", "6": "Content Studio: Butonul de încărcare fișiere are acum un fundal verde.", "7": "Content Studio: Înălțimea barei de căutare a fost scăzută pentru o aliniere mai bună.", "8": "Content Studio: Bara de căutare nu mai este afișată dacă nu există imagini pentru utilizator."}}, "v2.0.0": {"date": "16 mai 2025", "changes": {"0": "🚀 ACTUALIZĂRI MAJORE LA BIELA.DEV! 🚀", "1": "Propria noastră tehnologie de container web: Am construit propria noastră tehnologie de container web care este complet independentă de orice furnizori externi! Aceasta ne oferă flexibilitate fără precedent pentru a dezvolta funcționalități noi mai rapid și mai bine ca niciodată.", "2": "Opțiuni LLM multiple pentru generarea de cod: Acum suportăm modele AI multiple pentru generarea de cod dincolo de Anthropic! Acum puteți folosi Gemini de la Google pentru proiectele dvs. de codare, care aduce unele avantaje majore. Gemini oferă o fereastră de context masivă de 1.000.000 de token-uri!", "3": "Content Studio: Fotografiile dvs., proiectele dvs.: Exprimați-vă complet cu noul nostru Content Studio! Acum puteți încărca propriile fotografii pentru a le folosi în proiectele dvs. Vibe Coding.", "4": "Partajarea proiectelor: Colaborarea tocmai s-a îmbunătățit! Acum puteți partaja proiectele dvs. cu alți utilizatori.", "5": "Conectarea la proiecte Supabase existente: Această nouă funcționalitate puternică vă permite să conectați proiecte Biela.dev multiple la aceeași bază de date Supabase."}}, "v1.0.5": {"date": "15 mai 2025", "changes": {"0": "Plăți: A fost implementată procesarea plăților Stripe.", "1": "Content Studio: O animație (ca robotul) este acum afișată când nu există imagine pe prima pagină."}}, "v1.0.4": {"date": "13 mai 2025", "changes": {"0": "Content Studio: Reparate problemele de paginare în Content Studio."}}, "v1.0.3": {"date": "5 mai 2025", "changes": {"0": "Lansat Content Studio - un loc pentru stocarea și gestionarea tuturor mediilor dvs.", "1": "Frontoffice: Implementat schema.org pentru aplicații software pe biela_frontoffice.", "2": "Dashboard echipă: Au fost adăugate notificări la dashboard-ul echipei (ex. un punct roșu când un membru nou se alătură sau apare un mesaj nou în chat).", "3": "IDE: Reparat un bug unde modalul din meniu nu se închidea corespunzător când se făcea clic în interiorul WebContainer în anumite zone în timp ce se dădea un prompt."}}, "v1.0.2": {"date": "2 mai 2025", "changes": {"0": "Afiliat: Implementat redesignul zonei de membri ai echipei.", "1": "Afiliat: Pictogramele Dashboard-ului Afiliat au fost actualizate.", "2": "IDE: Textul butonului \"Conectează Supabase\" a fost schimbat în \"Bază de date\" cu o pictogramă nouă, și acum afișează \"Bază de date Conectată\" în albastru când este conectat."}}, "v1.0.1": {"date": "1 mai 2025", "changes": {"0": "IDE (Tab Setări): Implementată integrarea bazei de date în chat-ul setărilor.", "1": "Dashboard: Reparată paginarea în dashboard-ul utilizatorului."}}, "v1.0.0": {"date": "18 aprilie 2025", "changes": {"0": "🚀 Bun venit la Biela.dev: Companionul dvs. de codare alimentat de AI, gratuit pentru toți! 🚀", "1": "Suntem încântați să anunțăm lansarea publică inițială a Biela.dev! Credem în a face dezvoltarea web asistată de AI de ultimă generație accesibilă tuturor, de aceea platforma noastră este complet gratuită din prima zi."}}}}}