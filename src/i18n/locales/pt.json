{"meta": {"home": {"title": "biela.dev | Construtor de Web & Apps com IA – Construa com Prompts", "description": "Transforme suas ideias em sites ou aplicativos funcionais com biela.dev. Use prompts orientados por IA para criar produtos digitais personalizados sem esforço"}, "privacy": {"title": "Política de Privacidade da biela.dev", "description": "Entenda como a biela.dev coleta, utiliza e protege suas informações pessoais."}, "terms": {"title": "Termos de Serviço da biela.dev", "description": "Revise os termos e condições para usar a plataforma de desenvolvimento baseada em IA da biela.dev"}, "changelogSection": {"title": "Registro de Alterações da biela.dev - Sempre. Evoluindo. Para a frente.", "description": "Descubra como a biela.dev evolui com cada versão"}, "competitionterms": {"title": "Termos e Condições do Concurso de Desenvolvimento da Biela", "description": "Participe do Concurso de Desenvolvimento da Biela—um desafio oficial de um mês promovido pela Biela.dev e pelo Instituto TeachMeCode. Mostre suas criações digitais, conquiste reconhecimento com o apoio da comunidade e dispute os melhores lugares. Prazo final para envio: 14 de maio de 2025."}, "contest": {"title": "Competição de Desenvolvimento biela.dev 2025 – Vencedores Anunciados", "description": "Conheça os vencedores da competição biela.dev e descubra os incríveis projetos construídos com IA que conquistaram as maiores honras"}, "howTo": {"title": "Como usar o Biela.dev – Guia completo para criar aplicativos com IA", "description": "Descubra como usar o Biela.dev para criar aplicativos web de forma rápida e sem escrever código. Um guia completo passo a passo com tutoriais e dicas práticas.", "keywords": "biela, biela.dev, como usar biela, criar apps IA, sem código, desenvolvimento rápido, guia biela, tutorial biela", "ogTitle": "Como usar o Biela.dev – Guia Passo a Passo", "ogDescription": "Aprenda a criar apps com IA no Biela.dev. Da ideia ao lançamento, sem precisar programar.", "twitterTitle": "Como usar o Biela.dev – <PERSON><PERSON><PERSON>", "twitterDescription": "Crie apps com IA no Biela.dev sem programar. Siga este guia passo a passo!"}}, "navbar": {"home": "Início", "demo": "Demonstração", "community": "Comunidade", "affiliate": "<PERSON><PERSON><PERSON><PERSON>", "partners": "<PERSON><PERSON><PERSON><PERSON>", "contest": "Hackathon de Codificação Vibe", "subscriptions": "Assinaturas", "pricing": "Preços"}, "counter": {"days": "<PERSON><PERSON>", "hours": "<PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "seconds": "<PERSON><PERSON><PERSON>", "centisec": "Centissegundos"}, "hero": {"title": "Transforme sua ideia em um site ou app ao vivo em minutos", "subtitle": "Se você pode <1>imaginar</1> isso, você pode <5>programá-lo</5>.", "subtitle2": "Comece gratuitamente. Codifique tudo. Transforme suas habilidades a cada solicitação em uma oportunidade.", "timeRunningOut": "O tempo está acabando!", "launchDate": "<PERSON><PERSON><PERSON>", "experimentalVersion": "Lançamento beta em 15 de abril de 2025", "earlyAdopter": "Junte-se agora para garantir benefícios de adotante precoce antes do lançamento oficial", "tryFree": "Experimente Gratuitamente", "seeAiAction": "Veja a IA em ação", "tellBiela": "Peça para a Biela criar", "inputPlaceholder": "Se você pode imaginar, a BIELA pode codificar. O que vamos fazer hoje?", "chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "Codificar", "checklist": "Lista de verificação", "checklistTitle": "Use listas de verificação para", "checklistItems": {"trackDevelopment": "Acompanhar tarefas de desenvolvimento", "setRequirements": "Definir requisitos do projeto", "createTesting": "Criar protocolos de teste"}, "registerNow": "Cadastre-se agora", "attachFiles": "Anexe arquivos ao seu prompt", "voiceInput": "Use entrada de voz", "enhancePrompt": "Aprimore o prompt", "cleanupProject": "Limpar projeto", "suggestions": {"weatherDashboard": "Crie um painel meteorológico", "ecommercePlatform": "Construa uma plataforma de comércio eletrônico", "socialMediaApp": "Projete um aplicativo de rede social", "portfolioWebsite": "Gere um site de portfólio", "taskManagementApp": "Crie um aplicativo de gerenciamento de tarefas", "fitnessTracker": "Construa um monitor de fitness", "recipeSharingPlatform": "Projete uma plataforma para compartilhamento de receitas", "travelBookingSite": "Crie um site de reservas de viagens", "learningPlatform": "Construa uma plataforma de aprendizado", "musicStreamingApp": "Projete um aplicativo de streaming de música", "realEstateListing": "Crie uma listagem imobiliária", "jobBoard": "Crie um quadro de empregos"}}, "videoGallery": {"barber": {"title": "Site de Barbearia", "description": "Veja como a Biela.dev cria um site completo para barbearia em minutos"}, "tictactoe": {"title": "<PERSON><PERSON>", "description": "Assista a IA criar um jogo da velha mais rápido do que você diz 'jogo da velha'"}, "coffeeShop": {"title": "Site de Cafeteria", "description": "Veja a Biela.dev criando uma cafeteria"}, "electronicsStore": {"title": "Site de Loja de Eletrônicos", "description": "Veja como a Biela.dev cria uma loja online completa em minutos"}, "seoAgency": {"title": "Site de Agência de SEO", "description": "Assista a IA criar um site de agência de SEO"}}, "telegram": {"title": "Junte-se ao nosso Telegram", "subtitle": "Conecte-se com a comunidade Biela", "description": "Receba suporte instantâneo, compartilhe seus projetos e conecte-se com entusiastas de IA de todo o mundo. <br/><br/> Nossa comunidade no Telegram é a maneira mais rápida de se manter atualizado com a Biela.dev.", "stats": {"members": "Me<PERSON><PERSON>", "support": "Suporte", "countries": "Países", "projects": "Projetos"}, "joinButton": "Entre para a nossa comunidade no Telegram", "communityTitle": "Comunidade Biela.dev", "membersCount": "membros", "onlineCount": "online agora", "messages": {"1": "Acabei de lançar meu site de e-commerce com a Biela.dev em menos de uma hora!", "2": "Isso parece incrível! Como você lidou com o catálogo de produtos?", "3": "A Biela cuidou disso automaticamente! Eu apenas descrevi o que eu queria.", "4": "Estou construindo um site de portfólio agora mesmo. As sugestões de IA são incríveis!", "5": "Alguém já experimentou os novos templates responsivos? Eles ficam fantásticos em dispositivos móveis."}}, "joinNetwork": {"title": "Junte-se à nossa rede", "subtitle": "Conecte-se. Cresça. Ganhe.", "affiliateProgram": "Programa de Afiliados", "registerBring": "Cadastre-se e traga 3 afiliados", "aiCourse": "Curso de Engenharia de IA", "courseDescription": "Curso abrangente de IA gratuito com TeachMeCode", "digitalBook": "Livro Digital", "bookDescription": "Edição digital exclusiva de \"The Art of Prompt Engineering\"", "exclusiveBenefits": "Benefícios Exclusivos", "benefitsDescription": "Ganhos vitalícios e privilégios especiais para membros", "registerNow": "Cadastre-se Agora", "teamEarnings": "<PERSON><PERSON><PERSON> da Equipe", "buildNetwork": "Construa sua rede", "weeklyMentorship": "Mentoria semanal", "teamBonuses": "Bônus de equipe", "startEarning": "Comece a ganhar hoje", "upToCommission": "ATÉ Comissão"}, "partners": {"title": "Confiado por líderes da indústria", "subtitle": "Colaborando com inovadores globais para moldar o futuro do desenvolvimento de IA", "strategicPartnerships": "Parcerias Estratégicas", "joinNetwork": "Junte-se à crescente rede de parceiros e empresas que estão transformando o desenvolvimento com a BIELA"}, "demo": {"title": "Veja a IA construir aplicativos e sites em tempo real", "subtitle": "Descubra como as empresas estão transformando sua presença digital com a Biela.dev", "seeInAction": "Veja a Biela.dev em ação", "whatKind": "Que tipo de site você precisa?", "describeWebsite": "Descreva sua ideia de site (por exemplo, 'Um portfólio para um fotógrafo')", "generatePreview": "Gerar <PERSON>-visualização", "nextDemo": "Próxima Demonstração", "startBuilding": "Comece a construir e ganhar com a Biela.dev hoje"}, "footer": {"quickLinks": {"title": "<PERSON><PERSON>", "register": "Registrar", "bielaInAction": "Veja a Biela.dev em ação", "liveOnTelegram": "Estamos ao vivo no Telegram", "affiliate": "Marketing de Afiliados Colaborativo", "partners": "<PERSON><PERSON><PERSON><PERSON>"}, "legal": {"title": "Legal", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. Todos os direitos reservados.", "unauthorized": "O uso, reprodução ou distribuição não autorizada deste serviço, no todo ou em parte, sem permissão escrita explícita, é estritamente proibida."}, "reservations": "O TeachMeCode Institute reserva todos os direitos legais sobre a propriedade intelectual associada à Biela.", "terms": "Termos de Serviço", "privacy": "Política de Privacidade"}, "bottomBar": {"left": "2025 Biela.dev. Impulsionado por %s © Todos os direitos reservados.", "allRights": "Todos os direitos reservados.", "right": "Desenvolvido por %s"}}, "common": {"loading": "Carregando...", "error": "Ocorreu um erro", "next": "Próximo", "previous": "Anterior", "submit": "Enviar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "loginNow": "Entrar agora", "verifyAccount": "Verificar conta"}, "languageSwitcher": {"language": "Idioma"}, "contest": {"bielaDevelopment": "Hackathon de Codificação Vibe", "competition": "COMPETIÇÃO", "firstPlace": "1º LUGAR", "secondPlace": "2º LUGAR", "thirdPlace": "3º LUGAR", "currentLeader": "<PERSON><PERSON><PERSON> atual:", "footerText": "Desenvolvimento com IA por Biela.dev, com o apoio do Instituto TeachMeCode®", "browseHackathonSubmissions": "Navegar pelas submissões do Hackathon", "winnersAnnouncement": "Vencedores do Concurso Anunciados!", "congratulationsMessage": "Parabéns aos nossos incríveis vencedores! Obrigado a todos que participaram deste hackathon incrível.", "conditionActiveReferrals": "Tem 3 indicações ativas", "conditionLikedProject": "Curtiu outro projeto", "winner": "<PERSON><PERSON><PERSON>", "qualificationsMet": "Qualificações:", "noQualifiedParticipant": "Nenhum Participante Qualificado", "requirementsNotMet": "Requisitos não atendidos", "noQualificationDescription": "Nenhum participante atendeu aos requisitos de qualificação para esta posição.", "qualifiedWinners": "🏆 Vencedores Qualificados", "qualifiedWinnersMessage": "Parabéns aos participantes que atenderam a todos os requisitos de qualificação!", "noTopPrizesQualified": "Nenhum Participante Qualificado para os Prêmios Principais", "noTopPrizesMessage": "Infelizmente, nenhum participante atendeu aos requisitos de qualificação para as três primeiras posições de prêmio. Todos os participantes qualificados receberão prêmios do pool de prêmios restante.", "noTopPrizesMessageShort": "Infelizmente, nenhum participante atendeu aos requisitos de qualificação para as três primeiras posições de prêmio."}, "competition": {"header": {"mainTitle": "Sua proposta. Seu estilo. Seu playground.", "timelineTitle": "CRONOGRAMA", "timelineDesc": "Um mês para criar o seu melhor projeto gerado por IA", "eligibilityTitle": "ELEGIBILIDADE", "eligibilityDesc": "Contas ativas no Biela.dev que enviam um projeto", "prizesTitle": "PRÊMIOS", "prizesDesc": "US$ 16.000 em prêmios em din<PERSON>, além de acesso gratuito para os vencedores", "votingTitle": "VOTAÇÃO", "votingDesc": "Baseado na comunidade, um voto por usuário verificado", "tagline": "Deixe sua criatividade falar. Deixe o mundo votar."}, "details": {"howToEnter": "Como Participar", "enter": {"step1": {"title": "Crie um projeto usando a IA do Biela.dev", "desc": "Utilize as poderosas ferramentas de IA do Biela para desenvolver seu projeto inovador"}, "step2": {"title": "Acesse seu painel de usuário", "desc": "Clique em \"Publicar para Hackathon\" ao lado do seu projeto", "subdesc": "Você pode enviar até 3 projetos diferentes"}, "step3": {"title": "O Biela irá processar sua inscrição:", "list1": "Tire uma captura de tela do seu projeto", "list2": "Verifique se suas propostas atendem aos critérios de elegibilidade", "list3": "Gere um resumo e uma descrição"}}, "prizeStructure": "Estrutura dos Prêmios", "prizes": {"firstPlace": "1º Lugar", "firstPlaceValue": "US$ 10.000", "secondPlace": "2º Lugar", "secondPlaceValue": "US$ 5.000", "thirdPlace": "3º Lugar", "thirdPlaceValue": "US$ 1.000", "fourthToTenth": "Do 4º ao 10º Lugar", "fourthToTenthValue": "30 dias de acesso ilimitado"}}, "qualification": {"heading": "Requisitos de Qualificação", "description": "Para se qualificar para os 3 principais prêmios (US$ 10.000, US$ 5.000 e US$ 1.000), você precisa atender aos seguintes critérios:", "criteria1": "Ter pelo menos 3 indicações ativas", "criteria2": "Curtir pelo menos um projeto no mural de exibição", "proTipLabel": "Dica Profissional:", "proTipDesc": "Quanto antes você enviar seu projeto, maior será sua visibilidade e o número de votos!"}, "voting": {"heading": "Diretrizes de Votação", "oneVotePolicyTitle": "Política de Voto Único", "oneVotePolicyDesc": "Você pode votar apenas uma vez e não para o seu próprio projeto", "accountVerificationTitle": "Verificação de Conta", "accountVerificationDesc": "Somente usuários com contas verificadas no Biela.dev podem votar", "tieBreakingTitle": "Desempate", "tieBreakingDesc": "Em caso de empate, o fator decisivo será o número total de estrelas obtidas pelo criador do projeto (usadas ou não)", "howToVoteTitle": "Como Votar", "howToVoteDesc": "Explore o mural de exibição, confira todos os projetos gerados por IA publicados e clique no ícone de coração para votar no seu projeto favorito!"}}, "contestItems": {"projectShowcase": "<PERSON><PERSON><PERSON>", "loading": "Carregando...", "createBy": "<PERSON><PERSON><PERSON> por ", "viewProject": "Ver Projeto", "stars": "Estrelas", "overview": "visão geral", "keyFeatures": "Principais Funcionalidades", "exploreLiveProject": "Explore o projeto ao vivo", "by": "por", "likeLimitReached": "Limite de curtidas atingido", "youCanLikeMaximumOneProject": "Você pode curtir no máximo um projeto. Gostaria de desfazer a curtida no atual e curtir este em vez disso?", "currentlyLikedProject": "Projeto atualmente curtido", "switchMyLike": "Trocar minha curtida", "mustLoggedIntoLikeProject": "Você precisa estar logado para curtir um projeto.", "signInToBielaAccountToVote": "Por favor, faça login na sua conta Biela.dev para participar do processo de votação.", "yourAccountIsNotVerifiedYet": "Sua conta ainda não foi verificada. Por favor, verifique sua conta para poder curtir um projeto.", "accountVerificationEnsureFairVoting": "A verificação da conta garante uma votação justa e ajuda a manter a integridade da competição.", "projectsSubmitted": "Projetos enviados:", "unlikeThisProject": "Descurtir este projeto", "likeThisProject": "Curtir este projeto", "likes": "curtidas", "like": "curtir", "somethingWentWrong": "Algo deu errado. Por favor, tente novamente mais tarde", "voteError": "Erro de votação"}, "textsLiveYoutube": {"first": {"title": "Estás convidado!", "description": "Vamos transmitir em direto esta sexta-feira — esta sessão é sobre construir com <green>propósito</green>, <blue>precisão</blue> e <orange>vibração pura</orange>.", "secondDescription": "Descobre como <green>dominar o vibe coding</green>, <blue>descobrir novas funcionalidades</blue> antes de toda a gente, e <orange>ver projetos reais</orange> serem construídos em direto.", "specialText": "Clica aqui e carrega em \"Notificar-me\" no YouTube — não percas."}, "second": {"title": "<PERSON><PERSON> o primeiro a construir.", "description": "Esta sexta-feira levamos o <green>vibe coding</green> a um novo nível — mostramos-te como <blue>criar com intenção</blue>, <orange>desbloquear novas funcionalidades</orange> e lançar <green>mais rápido do que nunca</green>.", "secondDescription": "<green><PERSON><PERSON> builds</green>, <blue>novas ideias</blue> e <orange>novas oportunidades</orange> para crescer.", "specialText": "Clica aqui para definir o lembrete no YouTube — e faz parte da próxima vaga de criadores."}, "third": {"title": "Reserva já o teu lugar.", "description": "O nosso próximo <green>livestream Biela.dev</green> é esta sexta-feira — e estás <blue>oficialmente convidado</blue>.", "secondDescription": "Vamos mergulhar fundo no <green>vibe coding</green>, revelar <blue>poderosas novas funcionalidades</blue> e ajudar-te a construir <orange>mais inteligente, mais rápido e em grande escala</orange>.", "specialText": "Clica aqui e toca em \"Notificar-me\" no YouTube — a tua próxima grande ideia pode começar aqui."}}, "stream": {"title": "<PERSON><PERSON> o <1><PERSON><PERSON></1> a <3>Construir</3>.", "subtitle": "Participe do nosso livestream de sexta-feira — recursos, projetos e atualizações exclusivas.", "button": "Defina seu lembrete no YouTube."}, "billings": {"modalTitle": "Escolha um Plano", "modalSubtitle": "Escolha entre nossos três planos flexíveis para continuar programando sem limites.", "toggleMonthly": "Mensal", "toggleYearly": "Anual (economize 10%)", "mostPopular": "Mais <PERSON>", "tokensLowerCase": "tokens", "tokensUpperCase": "Tokens", "below": "abaixo", "unlimited": "ILIMITADO", "10Bonus": "Bônus de 10%", "currentPlan": "Plano Atual", "upgradePlan": "Atualizar Plano", "purchaseDetails": "<PERSON><PERSON><PERSON> da Compra", "dateLabel": "Data", "planLabel": "Plano", "amountLabel": "Valor", "whatsNext": "Próximo <PERSON>", "yourTokensAvailable": "Seus tokens agora estão disponíveis na sua conta", "purchaseDetailsStored": "Os de<PERSON>hes da compra foram armazenados na sua conta", "viewPurchaseHistory": "Você pode visualizar seu histórico de compras na seção de cobrança", "thankYou": "<PERSON><PERSON><PERSON>!", "purchaseSuccessful": "Sua compra foi realizada com sucesso", "startCoding": "Começar a Programar", "month": "mês", "year": "ano"}, "feature": {"basic_ai": "Desenvolvimento de IA Básica", "community_support": "Suporte Comunitário", "standard_response": "Tempo de Resposta Padrão", "basic_templates": "Modelos Básicos", "advanced_ai": "Desenvolvimento de IA Avançada", "priority_support": "Suporte Prioritário", "fast_response": "Tempo de Resposta Rápido", "premium_templates": "Modelos Premium", "team_collaboration": "Colaboração em Equipe", "enterprise_ai": "Desenvolvimento de IA Empresarial", "support_24_7": "Suporte Prioritário 24/7", "instant_response": "Tempo de Resposta Instantâneo", "custom_templates": "Modelos Personalizados", "advanced_team": "Funcionalidades Avançadas de Equipe", "custom_integrations": "Integrações Personalizadas", "big_context_window": "<PERSON><PERSON> de Contexto Grande - 5x Maior que a Média", "code_chat_ai": "Acesso à IA de Código e Chat", "tokens_basic": "Tokens suficientes para aprox. 3 sites de apresentação", "standard_webcontainer": "Webcontainer <PERSON><PERSON><PERSON>", "medium_context_window": "<PERSON>la de Contexto Média - Adequada para site, blog ou jogo básico de navegador", "basic_support": "Suporte Básico com Tempo de Resposta Padrão", "supabase_integration": "Integração Supabase", "project_sharing": "Compartilhe Projetos com Seus Amigos", "project_download": "Baixe Seus Projetos", "project_deployment": "Funcionalidade de Implantação", "custom_domain": "Conecte Projetos ao Seu Próprio <PERSON>", "tokens_creator_plus": "Tokens suficientes para aprox. 7 sites de apresentação", "advanced_support": "Suporte Avançado com Tempo de Resposta Mais Rápido", "prioritary_support": "Suporte Prioritário <PERSON>", "early_feature_access": "Acesso Antecipado a Novos Recursos", "human_cto": "CTO Humano Dedicado para Seu Negócio", "community_promotion": "Promova Seu Negócio com Nossa Comunidade"}, "JoinUsLive": "Junta-te a nós em direto", "howToUseBiela": {"subtitle": "Como usar o Biela", "title": "Explicado em minutos", "description": "Explicações breves de como cada recurso funciona"}, "slides": {"newHistoryFeature": "Descubra o novo recurso HISTÓRICO da Biela!", "newHistoryFeatureDescription": "A ABA DE HISTÓRICO mantém seu processo de vibe coding fluido, permitindo que você revise e restaure qualquer versão anterior do seu projeto, para que você mantenha o controle!", "fixPreview": "Como corrigir a pré-visualização no Biela!", "fixPreviewDescription": "Assista ao guia completo e certifique-se de que tudo apareça exatamente como você construiu.", "signUp": "Como se cadastrar no Biela!", "signUpDescription": "Novo no biela.dev? Assista a este guia rápido para aprender como se registrar e verificar sua conta!", "buildWebsite": "Como criar um site com o Biela!", "buildWebsiteDescription": "Aprenda como construir e lançar um site do zero usando o biela.dev — sem escrever uma única linha de código.", "downloadAndImport": "Como baixar e importar projetos e chats no Biela!", "downloadAndImportDescription": "Neste passo a passo, você aprenderá como baixar seus projetos concluídos e importar chats salvos para seu espaço de trabalho — para que você possa retomar exatamente de onde parou e manter o fluxo do Vibe Coding a qualquer momento.", "shareProject": "Como compartilhar seu projeto BIELA Vibe Code!", "shareProjectDescription": "Pronto para compartilhar seu projeto como um profissional? Assista ao guia completo e comece.", "launchProject": "Como lançar seu projeto no seu próprio domínio!", "launchProjectDescription": "Neste vídeo, passamos pelos passos simples para colocar seu site no ar — desde a configuração até a publicação final, você terá tudo o que precisa para passar com confiança da construção ao lançamento.", "deployProject": "Como implantar seu projeto e colocá-lo no ar!", "deployProjectDescription": "Este guia rápido mostra passo a passo como implantar seu site, aplicativo ou dashboard — de forma rápida e tranquila."}, "maintenance": {"title": "Estamos trabalhando para resolver o problema", "description": "Biela.dev está em modo de manutenção. Estamos trabalhando para resolver o problema", "text": "<PERSON><PERSON><PERSON> pela sua paciência."}, "planCard": {"descriptionBeginner": "Perfeito para iniciantes que estão construindo seus primeiros projetos e aprendendo desenvolvimento web", "descriptionCreator": "Ideal para criadores que precisam de mais potência e flexibilidade para projetos avançados", "descriptionProfessional": "Solução completa para desenvolvedores profissionais e equipes que constroem aplicações", "perMillionTokens": "$ {{price}} / por milhão de tokens", "dedicatedCto": "CTO dedicado - 24 horas", "tokens": "Tokens", "perMonth": "por mês", "perYear": "por ano", "freeTokens": "{{ tokens }} Tokens gratuitos", "monthly": "Mensal", "yearly": "<PERSON><PERSON>", "save": "ECONOMIZE 10%"}, "faqLabel": "Perguntas Frequentes", "faqHeading": "<PERSON><PERSON><PERSON> as perguntas que mais recebemos", "faqSubheading": "Comece. Entenda. Explore", "whatAreTokensQuestion": "O que são tokens?", "whatAreTokensAnswer1": "Os tokens são como a IA processa seu projeto. No Biela.dev, eles são usados quando você envia um prompt à IA. Durante esse processo, seus arquivos de projeto são incluídos na mensagem para que a IA entenda seu código. Quanto maior o projeto e mais longa a resposta, mais tokens são consumidos por mensagem.", "whatAreTokensAnswer2": "<PERSON><PERSON><PERSON> disso, uma pequena quantidade de tokens é deduzida da sua conta por cada minuto de uso de um contêiner web, para cobrir o custo do ambiente de desenvolvimento.", "freePlanTokensQuestion": "Quantos tokens recebo no plano gratuito?", "freePlanTokensAnswer": "Cada usuário do plano gratuito recebe 200.000 tokens por dia, renovados diariamente.", "outOfTokensQuestion": "O que acontece se eu ficar sem tokens?", "outOfTokensAnswer": "Sem problemas! Você pode ir até Menu → Faturamento e comprar mais tokens instantaneamente quando precisar.", "changePlanQuestion": "Posso mudar de plano depois?", "changePlanAnswer": "Sim — a qualquer momento. Basta acessar Menu → Faturamento para mudar, fazer upgrade ou downgrade conforme necessário.", "rolloverQuestion": "Os tokens não usados acumulam?", "rolloverAnswer": "Atualmente, os tokens são reiniciados na data de expiração e não acumulam — então use-os ao máximo todos os dias!", "exploreHowToUseBiela": "Explore como usar o Biela", "new": "Novo", "joinVibeCoding": "Participe do Hackathon Vibe Coding", "whatDoYouWantToBuild": "O QUE VOCÊ QUER CONSTRUIR?", "imaginePromptCreate": "Imagine. Solicite. Crie.", "levelOfAmbition": "Qual é o seu nível de ambição", "startGrowScale": "Comece. Cresça. Expanda.", "calloutBar": {"start": "Todos os planos começam gratuitamente com", "tokens": "200.000 tokens por dia", "end": ". <PERSON><PERSON> bar<PERSON> de cadastro. Basta começar a criar."}, "atPriceOf": "pelo preço de", "startFreeNoCreditCard": "Comece Gratuitamente - Sem Cartão de Crédito", "SignIn": "Entrar", "Register": "Cadastrar-se", "Affiliates": "<PERSON><PERSON><PERSON><PERSON>", "UsernameRequired": "Nome de usuário é obrigatório", "PasswordRequired": "Senha é obrigatória", "MissingUsernamePasswordOrTurnstile": "<PERSON><PERSON> de usuá<PERSON>, senha ou <PERSON> Turnstile ausente", "LoginSuccess": "Login bem-sucedido!", "LoginFailed": "Falha no login", "EmailRequired": "Email é obrigatório", "EmailInvalid": "Por favor, insira um endereço de email válido", "CaptchaRequired": "Por favor, complete o CAPTCHA", "ResetLinkSent": "Um link de redefinição foi enviado para seu email.", "ResetFailed": "Falha ao enviar o link de redefinição", "BackToLogin": "Voltar ao login", "EmailPlaceholder": "Seu email", "SendResetLink": "Enviar link de redefinição", "loading": "Carregando", "checkingYourAuthenticity": "Verificando sua autenticidade", "ToUseBielaDev": "Para usar o Biela.dev você deve fazer login em uma conta existente ou criar uma usando uma das opções abaixo", "Sign in with Google": "Entrar com Google", "Sign in with GitHub": "Entrar com GitHub", "Sign in with Email and Password": "Entrar com Email e Senha", "DontHaveAnAccount": "Não tem uma conta?", "SignUp": "Cadastre-se", "ByUsingBielaDev": "Ao usar o Biela.dev, você consente com a coleta de dados de uso.", "EmailOrUsernamePlaceholder": "Email/Nome de usuário", "passwordPlaceholder": "<PERSON><PERSON>", "login.loading": "Carregando", "LoginInToProfile": "Entrar no seu Perfil", "login.back": "Voltar", "forgotPassword": "Esque<PERSON>u a senha?", "PasswordsMismatch": "As senhas não coincidem.", "PhoneRequired": "Número de telefone é obrigatório", "ConfirmPasswordRequired": "Por favor, confirme sua senha", "AcceptTermsRequired": "Você deve aceitar os Termos de Serviço e a Política de Privacidade", "RegistrationFailed": "Falha no cadastro", "EmailConfirmationSent": "Confirmação de email foi enviada. Por favor, confirme seu email e então faça login.", "RegistrationServerError": "Falha no cadastro (o servidor retornou falso).", "SomethingWentWrong": "<PERSON>go deu errado", "CheckEmailConfirmRegistration": "Verifique seu email para confirmar seu cadastro", "EmailConfirmationSentText": "Enviamos um email com um link de confirmação.", "LoginToProfile": "Entrar no perfil", "username": "Nome de usuário", "email": "Email", "PasswordPlaceholder": "<PERSON><PERSON>", "ConfirmPasswordPlaceholder": "Confirmar <PERSON>", "AcceptTermsPrefix": "Eu concordo com os", "TermsOfService": "Termos de Serviço", "AndSeparator": "e", "PrivacyPolicy": "Política de Privacidade", "CreateAccount": "<PERSON><PERSON><PERSON>", "Back": "Voltar", "SignInWithGoogle": "Entrar com o Google", "SignInWithGitHub": "Entrar com o GitHub", "SignInWithEmailAndPassword": "Entrar com email e senha", "translation": {"AIModel": "Modelo de IA", "UpgradePlan": "um plano premium", "PremiumBadge": "PREMIUM", "Active": "Ativo", "projectInfo.features": "Recursos", "Stats": "Estatísticas", "Performance": "<PERSON><PERSON><PERSON><PERSON>", "Cost": "Custo", "UpgradeTooltip": "Desbloqueie atualizando para ", "UpgradeTooltipSuffix": "pacote", "chat": "Cha<PERSON>", "code": "Código"}, "extendedThinkingTooltip": "Permitir que a IA pense mais profundamente antes de responder", "extendedThinkingTooltipDisabled": "Desativar pensamento aprofundado para economizar recursos", "AIModel": "Modelo de IA", "UpgradePlan": "um plano premium", "PremiumBadge": "PREMIUM", "Active": "Ativo", "projectInfo.features": "Recursos", "Stats": "Estatísticas", "Performance": "<PERSON><PERSON><PERSON><PERSON>", "Cost": "Custo", "UpgradeTooltip": "Desbloqueie atualizando para ", "UpgradeTooltipSuffix": "pacote", "HowBielaWorks": "Como a BIELA funciona", "WatchPromptLaunch": "Assista. Escreva. Lance.", "SearchVideos": "Pesquisar na biblioteca...", "NewReleased": "NOVO LANÇAMENTO", "Playing": "Reproduzindo", "NoVideosFound": "Nenhum vídeo encontrado", "TryAdjustingYourSearchTerms": "Tente ajustar os termos de pesquisa", "feedback": {"title": {"issue": "Relatar um problema", "feature": "Solicitar um recurso"}, "description": {"issue": "Encontrou um bug ou está enfrentando um problema? Conte para nós e vamos corrigi-lo o quanto antes.", "feature": "Tem uma ideia para um novo recurso? Adoraríamos ouvir suas sugestões para melhorar nossa plataforma."}, "success": {"title": "<PERSON><PERSON><PERSON>!", "issue": "Seu relatório de problema foi enviado com sucesso.", "feature": "Sua solicitação de recurso foi enviada com sucesso."}, "form": {"fullName": {"label": "Nome completo", "placeholder": "Digite seu nome completo"}, "email": {"label": "Endereço de e-mail", "placeholder": "Digite seu endereço de e-mail"}, "suggestion": {"labelIssue": "Descrição do problema", "labelFeature": "Sugestão de recurso", "placeholderIssue": "Descreva o problema que você está enfrentando...", "placeholderFeature": "Descreva o recurso que você gostaria de ver..."}, "screenshots": {"label": "Capturas de tela (opcional)", "note": "Adicione capturas de tela para nos ajudar a entender melhor o problema", "drop": "Clique para enviar ou arraste e solte", "hint": "PNG, JPG, JPEG até 10MB cada", "count": "{{count}} imagem(ns) selecionada(s)"}}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "submitting": "Enviando...", "submitIssue": "Enviar problema", "submitFeature": "Enviar solicitação"}, "errors": {"fullNameRequired": "Nome completo é obrigatório", "fullNameNoNumbers": "O nome completo não pode conter números", "emailRequired": "Endereço de email é obrigatório", "emailInvalid": "Por favor, digite um endereço de email válido", "suggestionRequired": "Por favor, descreva seu problema ou sugestão"}}, "changelog": {"title": "Registro de alterações", "description": "Sempre. Evoluindo. Para frente.", "TotalReleases": "Lançamentos totais", "ActiveUsers": "Usuários ativos", "FeaturesAdded": "Funcionalidades adicionadas", "BugsFixed": "<PERSON><PERSON><PERSON> corrigi<PERSON>", "totalReleases": "Lançamentos totais", "activeUsers": "Usuários ativos", "featuresAdded": "Funcionalidades adicionadas", "bugsFixed": "<PERSON><PERSON><PERSON> corrigi<PERSON>", "shortCallText": "Tem sugestões ou encontrou um bug? Adoraríamos ouvir você!", "reportIssue": "Reportar um problema", "requestFeature": "Solicitar funcionalidade", "types": {"feature": "Funcionalidade", "improvement": "Melhoria", "bugfix": "Correção de erro", "ui/ux": "UI/UX", "announcement": "<PERSON><PERSON><PERSON>", "general": "G<PERSON>"}, "versions": {"v3.2.4": {"date": "2 de julho de 2025", "changes": {"0": "Melhorada UI/UX para AI Diff View com experiência mais limpa e responsiva.", "1": "Adicionado novo status 'Pensando' durante processamento AI Diff para mostrar quando IA está trabalhando.", "2": "Melhorado 0xProcessing com manuseio avançado de pagamentos e suporte webhook.", "3": "Adicionado suporte para rastreamento de status de faturas e cobranças Stripe.", "4": "Cupons aplicados agora concedem tokens instantaneamente.", "5": "Introduzido suporte para pagamentos em criptomoeda e lógica de faturamento atualizada.", "6": "Adicionado suporte para uploads de imagens SVG e HEIC no Content Studio.", "7": "Limpeza de importações e comentários.", "8": "Melhorado tratamento de erros de deployment (ex. erros de terminal)."}}, "v3.2.3": {"date": "26 de junho de 2025", "changes": {"0": "Corrigidos alguns problemas de UI/UX no painel do usuário.", "1": "Melhorada a estabilidade do WebContainer.", "2": "Modo AI Diff agora trata arquivos pequenos de maneira diferente dos grandes."}}, "v3.2.2": {"date": "24 de junho de 2025", "changes": {"0": "Reorganizado e redesenhado o frontoffice para uma melhor experiência de usuário.", "1": "Implementada esta poderosa página de changelog.", "2": "Aprimorado o algoritmo de reconexão do WebContainer.", "3": "Adicionado suporte para upload de projetos por arquivos ZIP, além de pastas.", "4": "Melhoria no desempenho de todas as chamadas de API com melhor indexação."}}, "v3.2.1": {"date": "24 de junho de 2025", "changes": {"0": "IDE: A seção \"Pastas\" no painel foi redesenhada.", "1": "Content Studio: <PERSON><PERSON><PERSON> você pode copiar imagens dentro do estúdio."}}, "v3.2.0": {"date": "17 de junho de 2025", "changes": {"0": "Modo AI Diff (Experimental): A IA agora escreve apenas as diferenças entre o arquivo original e o atualizado, sem regenerar todo o conteúdo. Isso melhora o desempenho e economiza tokens. Pode ser ativado ou desativado em Configurações → Centro de Controle."}}, "v3.1.6": {"date": "12 de junho de 2025", "changes": {"0": "IDE: Campo de código promocional adicionado para assinaturas e tokens extras."}}, "v3.1.5": {"date": "11 de junho de 2025", "changes": {"0": "IDE: V<PERSON><PERSON>s correções aplicadas ao fluxo de \"Compartilhar um Projeto\"."}}, "v3.1.4": {"date": "10 de junho de 2025", "changes": {"0": "IDE: Verificadas e corrigidas assinaturas de pacotes, incluindo questões com Stripe e criptomoedas."}}, "v3.1.3": {"date": "9 de junho de 2025", "changes": {"0": "IA: Implementada e testada a integração direta da API do Gemini para melhorar a latência."}}, "v3.1.2": {"date": "6 de junho de 2025", "changes": {"0": "Afiliados: Implementadas tarefas de pagamento de afiliados com design alinhado ao novo painel (histórico de pagamentos).", "1": "Content Studio: Nova opção adicionada para decidir se as imagens devem ser enviadas à IA junto com os links. Padrão: não."}}, "v3.1.1": {"date": "5 de junho de 2025", "changes": {"0": "Content Studio: Atualizações feitas para organizar o conteúdo por projeto."}}, "v3.1.0": {"date": "3 de junho de 2025", "changes": {"0": "🚀 Grande atualização do BIELA.dev lançada! 🚀", "1": "A IA agora escreve código 5x mais rápido.", "2": "Terminal: Melhorias de UI/UX para uma experiência de codificação mais suave.", "3": "Conexão Supabase melhorada: interface mais clara e intuitiva.", "4": "Relatórios automáticos de erros desativados; opção personalizável a caminho.", "5": "Novo Centro de Controle (nas configurações do projeto) e interface de testes unitários.", "6": "Downloads agora aguardam até que o código esteja completamente enviado."}}, "v3.0.0": {"date": "3 de junho de 2025", "changes": {"0": "🚀 Atualização poderosa do BIELA.dev! 🚀", "1": "Nova interface do terminal, mais limpa e moderna.", "2": "Ações NPM com um clique diretamente no terminal.", "3": "Detecção inteligente de erros e classificação visual com IA.", "4": "Navegação de arquivos aprimorada via chat direto no WebContainer."}}, "v2.1.1": {"date": "30 de maio de 2025", "changes": {"0": "IDE: <PERSON><PERSON><PERSON> de salvar adicionado ao WebContainer, realizando commit e push no GitHub.", "1": "Dashboard: <PERSON><PERSON><PERSON><PERSON> erro ao renomear projetos importados.", "2": "WebContainer: <PERSON><PERSON><PERSON><PERSON> vazamento de memória crítico."}}, "v2.1.0": {"date": "28 de maio de 2025", "changes": {"0": "🚀 7 mel<PERSON><PERSON> la<PERSON> no BIELA.dev! 🚀", "1": "IDE: Permite vincular seu domínio personalizado ao projeto.", "2": "Visualização de projetos implantados com um clique.", "3": "Correção no rastreamento de custos em projetos duplicados.", "4": "Estimativa de custo por prompt em tempo real.", "5": "Histórico de versões e reversão novamente funcionando.", "6": "Melhoria no autoscroll durante geração de código.", "7": "Itens do menu agora abrem em nova aba ao clicar."}}, "v2.0.4": {"date": "27 de maio de 2025", "changes": {"0": "Dashboard: <PERSON><PERSON><PERSON> nas capturas de tela — sem scroll automático, com scroll manual e animação de carregamento."}}, "v2.0.3": {"date": "20 de maio de 2025", "changes": {"0": "Content Studio: Animação do robô agora exibida em tamanho completo.", "1": "Afiliados: Corrigido problema de exibição no Firefox."}}, "v2.0.2": {"date": "19 de maio de 2025", "changes": {"0": "Content Studio: Número máximo de arquivos por upload aumentado de 10 para 50."}}, "v2.0.1": {"date": "18 de maio de 2025", "changes": {"0": "Content Studio: Co<PERSON><PERSON><PERSON> bug em que as tags de vários arquivos não eram atualizadas sem recarregar.", "1": "Botão de edição agora mais visível.", "2": "Local da pasta automaticamente selecionado com base no último usado.", "3": "A pasta selecionada permanece ao arrastar e soltar.", "4": "Cor do título da aba alterada.", "5": "<PERSON><PERSON><PERSON> \"inserir imagem\" renomeado para \"usar no projeto\".", "6": "Botão de upload agora com fundo verde.", "7": "Altura da barra de pesquisa reduzida.", "8": "Barra de pesquisa escondida quando não há imagens."}}, "v2.0.0": {"date": "16 de maio de 2025", "changes": {"0": "🚀 ATUALIZAÇÃO MAJOR NO BIELA.DEV! 🚀", "1": "Nova tecnologia de WebContainer própria, sem dependência de terceiros.", "2": "Suporte a múltiplos modelos LLM, incluindo Gemini com até 1M de tokens.", "3": "Content Studio: <PERSON>vie suas próprias imagens para os projetos.", "4": "Compartilhamento de projetos entre usuários.", "5": "Vários projetos BIELA agora podem se conectar à mesma base Supabase."}}, "v1.0.5": {"date": "15 de maio de 2025", "changes": {"0": "Pagamentos: Implementado suporte a Stripe.", "1": "Content Studio: Animação exibida quando não há imagens."}}, "v1.0.4": {"date": "13 de maio de 2025", "changes": {"0": "Content Studio: Correções na paginação."}}, "v1.0.3": {"date": "5 de maio de 2025", "changes": {"0": "Lançamento do Content Studio — gerencie suas mídias.", "1": "Frontoffice: Implementado schema.org para aplicativos de software.", "2": "Dashboard de equipe: Notificações adicionadas para novos membros e mensagens.", "3": "IDE: Corrigido erro ao fechar o modal clicando dentro do WebContainer."}}, "v1.0.2": {"date": "2 de maio de 2025", "changes": {"0": "Afiliados: Redesign da área de membros.", "1": "Afiliados: Ícones atualizados no painel de afiliados.", "2": "IDE: Botão \"Supabase Connect\" agora é \"Banco de Dados\" com novo ícone e status conectado."}}, "v1.0.1": {"date": "1 de maio de 2025", "changes": {"0": "IDE (Aba de Configurações): Integração com banco de dados implementada.", "1": "Dashboard: Correção na paginação."}}, "v1.0.0": {"date": "18 de abril de 2025", "changes": {"0": "🚀 Bem-vindo ao BIELA.dev: seu assistente de codificação com IA, gratuito para todos! 🚀", "1": "Lançamento oficial da plataforma. BIELA.dev é gratuito e acessível a todos para desenvolvimento assistido por IA."}}}}}