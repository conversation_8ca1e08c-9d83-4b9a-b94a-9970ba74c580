{"meta": {"home": {"title": "biela.dev | KI-gestützter Web- & App-Builder – Erstellen mit Prompts", "description": "Verwandle deine Ideen mit biela.dev in Live-Websites oder Apps. Erstelle mühelos individuelle digitale Produkte mit KI-gesteuerten Prompts."}, "privacy": {"title": "biela.dev <PERSON>tzrichtlinie", "description": "<PERSON><PERSON><PERSON><PERSON>, wie biela.dev deine persönlichen Daten sammelt, verwendet und schützt."}, "terms": {"title": "biela.dev Nutzungsbedingungen", "description": "Lies die Allgemeinen Geschäftsbedingungen für die Nutzung der KI-gestützten Entwicklungsplattform von biela.dev."}, "changelog": {"title": "biela.dev Changelog - Immer. Weiter. Entwicklung.", "description": "<PERSON><PERSON><PERSON><PERSON>, wie biela.dev mit jedem Release weiterentwickelt wird"}, "competitionterms": {"title": "Teilnahmebedingungen für den Biela Development Wettbewerb", "description": "Nimm am offiziellen einmonatigen Wettbewerb von Biela.dev und dem TeachMeCode Institute teil. Präsentiere deine digitalen Projekte, erhalte Anerkennung durch Community-Engagement und kämpfe um Top-Platzierungen. Einsendeschluss ist der 14. Mai 2025."}, "contest": {"title": "biela.dev Development Wettbewerb 2025 – Gewinne $10.000", "description": "Nimm am biela.dev Wettbewerb teil, präsentiere deine mit KI erstellten Projekte und gewinne Geldpreise."}, "howTo": {"title": "So verwendest du Biela.dev – Vollständiger Leitfaden zum Erstellen von KI-gestützten Apps", "description": "<PERSON><PERSON><PERSON><PERSON>, wie du mit Biela.dev schnell und ohne Programmierkenntnisse Web-Apps erstellst. Ein vollständiger Schritt-für-Schritt-Leitfaden mit Tutorials und praxisnahen Tipps.", "keywords": "biela, biela.dev, an<PERSON><PERSON><PERSON> biela, KI-A<PERSON> erstellen, no code, schnelle Entwicklung, biela leitfaden, biela tutorial", "ogTitle": "So verwendest du Biela.dev – Schritt-für-Schritt-Anleitung", "ogDescription": "<PERSON><PERSON>, wie du mit Biela.dev KI-Apps erstellst. Von der Idee bis zum Launch, ganz ohne Programmierung.", "twitterTitle": "So verwendest du Biela.dev – Vollständiger Leitfaden", "twitterDescription": "Erstelle KI-Apps auf Biela.dev ohne Programmierkenntnisse. Folge dieser einfachen Schritt-für-Schritt-Anleitung!"}}, "navbar": {"home": "Startseite", "demo": "Demo", "community": "Community", "affiliate": "Affiliate", "partners": "Partner", "contest": "Vibe Coding Hackathon", "subscriptions": "Abonnements", "pricing": "<PERSON><PERSON>"}, "counter": {"days": "Tage", "hours": "Stunden", "minutes": "Minuten", "seconds": "Sekunden", "centisec": "Cs"}, "hero": {"title": "Verwandeln Sie Ihre Idee in Minuten in eine Live-Website oder App", "subtitle": "<PERSON><PERSON> du es <1>dir vorstel<PERSON></1> kannst, kannst du es <5>programmieren</5>.", "subtitle2": "Kostenlos starten. Codieren Sie alles. Verwandeln Sie Ihre Fähigkeiten bei jeder Aufforderung in eine Chance", "timeRunningOut": "Die Zeit läuft ab!", "launchDate": "Version WoodSnake", "experimentalVersion": "Beta-Start am 15. April 2025", "earlyAdopter": "Melden Sie sich jetzt an, um sich die Vorteile für Frühnutzer vor dem offiziellen Start zu sichern", "tryFree": "<PERSON><PERSON><PERSON> testen", "seeAiAction": "KI in Aktion sehen", "tellBiela": "<PERSON><PERSON> Si<PERSON> Biela, dass sie erstellen soll", "inputPlaceholder": "Wenn du es dir vorstellen kannst, kann BIELA es programmieren. Was machen wir heute?", "chat": "Cha<PERSON>", "code": "Code", "checklist": "Checkliste", "checklistTitle": "<PERSON><PERSON><PERSON>, um", "checklistItems": {"trackDevelopment": "Entwicklungstätigkeiten verfolgen", "setRequirements": "Projektanforderungen festlegen", "createTesting": "Testprotokolle erstellen"}, "registerNow": "Jetzt registrieren", "attachFiles": "Füge deiner Eingabe Dateien hinzu", "voiceInput": "<PERSON><PERSON><PERSON>", "enhancePrompt": "Verbessere die Eingabe", "cleanupProject": "Bereinige das Projekt", "suggestions": {"weatherDashboard": "<PERSON><PERSON><PERSON>-Dashboard", "ecommercePlatform": "Baue eine E-Commerce-Plattform", "socialMediaApp": "Gestalte eine Social-Media-App", "portfolioWebsite": "<PERSON><PERSON><PERSON> eine Portfolio-Website", "taskManagementApp": "<PERSON>rst<PERSON> eine Aufgabenverwaltungs-App", "fitnessTracker": "<PERSON><PERSON> einen Fitness-Tracker", "recipeSharingPlatform": "Gestalte eine Plattform zum Teilen von Rezepten", "travelBookingSite": "<PERSON><PERSON><PERSON> eine Reisebuchungsseite", "learningPlatform": "Baue eine Lernplattform", "musicStreamingApp": "Gestalte eine Musik-Streaming-App", "realEstateListing": "<PERSON><PERSON><PERSON> eine Immobilienanzeige", "jobBoard": "Baue eine Jobbörse"}}, "videoGallery": {"barber": {"title": "Barbershop-Website", "description": "<PERSON><PERSON>, wie Biela.dev in wenigen Minuten eine vollständige Barbershop-Website erstellt"}, "tictactoe": {"title": "<PERSON><PERSON>l", "description": "<PERSON><PERSON>, wie KI ein <PERSON>-<PERSON>piel schneller erste<PERSON>t, als du '<PERSON><PERSON>' sagen kannst"}, "coffeeShop": {"title": "Coffee-Shop-Website", "description": "<PERSON><PERSON>, wie Biela.dev eine Coffee-Shop-Website erstellt"}, "electronicsStore": {"title": "Elektronikladen-Website", "description": "<PERSON><PERSON>, wie Biela.dev in wenigen Minuten einen kompletten Online-Shop erstellt"}, "seoAgency": {"title": "SEO-Agentur-Website", "description": "<PERSON><PERSON> zu, wie KI eine SEO-Agentur-Website erstellt"}}, "telegram": {"title": "Treten Sie unserem Telegram bei", "subtitle": "Verbinden Sie sich mit der Biela-Community", "description": "Erhalten Sie sofortige Unterstützung, teilen Sie Ihre Projekte und verbinden Sie sich mit KI-Enthusiasten aus der ganzen Welt. <br/><br/> Unsere Telegram-Community ist der schnellste Weg, um mit Biela.dev auf dem Laufenden zu bleiben.", "stats": {"members": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "support": "Support", "countries": "<PERSON><PERSON><PERSON>", "projects": "Projekte"}, "joinButton": "Treten Sie unserer Telegram-Community bei", "communityTitle": "Biela.dev Community", "membersCount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineCount": "jetzt online", "messages": {"1": "Ich habe gerade meine E-Commerce-Seite mit Biela.dev in weniger als einer Stunde gestartet!", "2": "Das sieht fantastisch aus! Wie hast du den Produktkatalog gehandhabt?", "3": "Biela hat es automatisch gehandhabt! Ich habe einfach beschrieben, was ich wollte.", "4": "Ich baue gerade eine Portfolio-Seite. Die KI-Vorschläge sind unglaublich!", "5": "Hat jemand die neuen responsiven Vorlagen ausprobiert? Sie sehen auf mobilen Geräten fantastisch aus."}}, "joinNetwork": {"title": "Treten Sie unserem Netzwerk bei", "subtitle": "Vernetzen. Wachsen. Verdienen.", "affiliateProgram": "Partnerprogramm", "registerBring": "Registrieren Sie sich und bringen Sie 3 Partner mit", "aiCourse": "KI-Engineering-<PERSON><PERSON>", "courseDescription": "Kostenloser umfassender KI-Kurs mit TeachMeCode", "digitalBook": "Digitales Buch", "bookDescription": "\"Die Kunst des Prompt-Engineerings\" exklusive digitale Ausgabe", "exclusiveBenefits": "Exklusive Vorteile", "benefitsDescription": "Lebenslange Einnahmen und spezielle Mitgliederprivilegien", "registerNow": "Jetzt registrieren", "teamEarnings": "Team-<PERSON><PERSON><PERSON>", "buildNetwork": "Bauen Sie Ihr Netzwerk auf", "weeklyMentorship": "Wöchentliches Mentoring", "teamBonuses": "Team-<PERSON><PERSON>", "startEarning": "Beginnen Si<PERSON> heute zu verdienen", "upToCommission": "BIS ZU Provision"}, "partners": {"title": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>", "subtitle": "Zusammenarbeit mit globalen Innovatoren, um die Zukunft der KI-gestützten Entwicklung zu gestalten", "strategicPartnerships": "Strategische Partnerschaften", "joinNetwork": "Schließe dich dem wachsenden Netzwerk von Partnern und Unternehmen an, die die Entwicklung mit BIELA transformieren."}, "demo": {"title": "<PERSON><PERSON> zu, wie KI Apps und Websites in Echtzeit erstellt", "subtitle": "Ent<PERSON>cken Si<PERSON>, wie Unternehmen ihre digitale Präsenz mit Biela.dev transformieren", "seeInAction": "Biela.dev in Aktion sehen", "whatKind": "Welche Art von Website benötigen Sie?", "describeWebsite": "Beschreiben Sie Ihre Website-Idee (z.B. 'Ein Portfolio für einen Fotografen')", "generatePreview": "<PERSON><PERSON><PERSON><PERSON>", "nextDemo": "Nächste Demo", "startBuilding": "Beginnen Si<PERSON> noch heute mit dem Aufbau und Verdienen mit Biela.dev"}, "footer": {"quickLinks": {"title": "Schnellzugriffe", "register": "Registrieren", "bielaInAction": "<PERSON>eh dir Biela.dev in Aktion an", "liveOnTelegram": "Wir sind live auf Telegram", "affiliate": "Kollaboratives Affiliate-Marketing", "partners": "Partner"}, "legal": {"title": "Rechtliches", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. Alle Rechte vorbehalten.", "unauthorized": "Die unbefugte Nutzung, Vervielfältigung oder Verbreitung dieses Dienstes, ganz oder teilweise, ohne ausdrückliche schriftliche Genehmigung ist strengstens untersagt."}, "reservations": "Das TeachMeCode Institute behält sich alle gesetzlichen Rechte an dem mit Biela verbundenen geistigen Eigentum vor."}, "bottomBar": {"left": "2025 Biela.dev. Bereitgestellt von %s © Alle Rechte vorbehalten.", "allRights": "Alle Rechte vorbehalten.", "right": "Entwickelt von %s"}}, "common": {"loading": "Wird geladen...", "error": "Ein Fehler ist aufgetreten", "next": "<PERSON><PERSON>", "previous": "Zurück", "submit": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Abbrechen", "close": "Schließen", "loginNow": "Jetzt anmelden", "verifyAccount": "Konto verifizieren"}, "languageSwitcher": {"language": "<PERSON><PERSON><PERSON>"}, "contest": {"bielaDevelopment": "Vibe-Coding-Hackathon", "competition": "WETTBEWERB", "firstPlace": "1. PLATZ", "secondPlace": "2. PLATZ", "thirdPlace": "3. PLATZ", "currentLeader": "Aktueller Führender:", "footerText": "KI-gestützte Entwicklung von Biela.dev, unterstützt vom TeachMeCode® Institut", "browseHackathonSubmissions": "Hackathon-Einreichungen durchsuchen"}, "competition": {"header": {"mainTitle": "<PERSON><PERSON>. Dein Stil. Dein Spielplatz.", "timelineTitle": "ZEITPLAN", "timelineDesc": "<PERSON><PERSON>, um dein bestes KI-generiertes Projekt zu erstellen", "eligibilityTitle": "TEILNAHMEBERECHTIGUNG", "eligibilityDesc": "Aktive Biela.dev-<PERSON><PERSON><PERSON>, die ein Projekt einreichen", "prizesTitle": "PREISE", "prizesDesc": "16.000 $ in Barpreisen plus freier Zugang für die Gewinner", "votingTitle": "ABSTIMMUNG", "votingDesc": "Community-b<PERSON><PERSON>, ein Vote pro verifiziertem Nutzer", "tagline": "Lass deine Kreativität sprechen. Lass die Welt abstimmen."}, "details": {"howToEnter": "So nimmst du teil", "enter": {"step1": {"title": "<PERSON><PERSON><PERSON> ein Projekt mit der KI von Biela.dev", "desc": "Nutze Bielas leistungsstarke KI-Tools, um dein innovatives Projekt zu erstellen"}, "step2": {"title": "<PERSON><PERSON>e zu deinem Nutzer-Dashboard", "desc": "<PERSON><PERSON><PERSON> Sie auf \"<PERSON><PERSON><PERSON> veröffentlichen\" neben Ihrem Projekt", "subdesc": "Du kannst bis zu 3 verschiedene Projekte einreichen"}, "step3": {"title": "Biela wird deinen Eintrag verarbeiten:", "list1": "<PERSON>he einen Screenshot deines Projekts", "list2": "Überprüfe deine Eingaben auf Teilnahmeberechtigung", "list3": "Erzeuge eine Zusammenfassung und Beschreibung"}}, "prizeStructure": "Preisstruktur", "prizes": {"firstPlace": "1. Platz", "firstPlaceValue": "$10.000", "secondPlace": "2. <PERSON><PERSON>", "secondPlaceValue": "$5.000", "thirdPlace": "3. <PERSON><PERSON>", "thirdPlaceValue": "$1.000", "fourthToTenth": "4.<PERSON>10. <PERSON><PERSON>", "fourthToTenthValue": "30 Tage unbegrenzter Zugang"}}, "qualification": {"heading": "Qualifikationsanforderungen", "description": "Um dich für die Top 3 Preise ($10.000, $5.000 und $1.000) zu qualifizieren, musst du die folgenden Kriterien erfüllen:", "criteria1": "Habe mindestens 3 aktive Empfehlungen", "criteria2": "<PERSON><PERSON><PERSON><PERSON>t dir mindestens ein Projekt von der Ausstellungswand", "proTipLabel": "Profi-Tipp:", "proTipDesc": "Je früher du dein Projekt e<PERSON>ich<PERSON>, desto mehr Sichtbarkeit und Stimmen erhält es!"}, "voting": {"heading": "Abstimmungsrichtlinien", "oneVotePolicyTitle": "Ein-Stimmen-Regel", "oneVotePolicyDesc": "Du kannst nur einmal abstimmen und nicht für dein eigenes Projekt", "accountVerificationTitle": "Kontoverifizierung", "accountVerificationDesc": "<PERSON>ur Nutzer mit verifizierten Biela.dev-Konten können abstimmen", "tieBreakingTitle": "Stichentscheid", "tieBreakingDesc": "Bei Gleichstand entscheidet die Gesamtzahl der vom Ersteller des Projekts verdienten Sterne (ob genutzt oder ungenutzt)", "howToVoteTitle": "So stimmst du ab", "howToVoteDesc": "Durchsuche die Ausstellungswand, erkunde alle veröffentlichten KI-generierten Projekte und klicke auf das Herzsymbol, um für dein Lieblingsprojekt zu stimmen!"}}, "contestItems": {"projectShowcase": "Projektpräsentation", "loading": "Laden...", "createBy": "<PERSON><PERSON><PERSON><PERSON> von ", "viewProject": "<PERSON><PERSON><PERSON> an<PERSON><PERSON>", "stars": "<PERSON><PERSON>", "overview": "Übersicht", "keyFeatures": "Hauptmerkmale", "exploreLiveProject": "Live-Projekt erkunden", "by": "von", "likeLimitReached": "Like-<PERSON><PERSON> er<PERSON>", "youCanLikeMaximumOneProject": "Sie können maximal ein Projekt liken. Möchten Sie das aktuelle Like entfernen und stattdessen dieses liken?", "currentlyLikedProject": "Aktuell geliktes Projekt", "switchMyLike": "<PERSON><PERSON> wechs<PERSON>n", "mustLoggedIntoLikeProject": "<PERSON><PERSON> müssen eingeloggt sein, um ein Projekt zu liken.", "signInToBielaAccountToVote": "Bitte melden Si<PERSON> sich bei Ihrem Biela.dev-Konto an, um am Abstimmungsprozess teilzunehmen.", "yourAccountIsNotVerifiedYet": "Ihr Konto ist noch nicht verifiziert. Bitte verifizieren Sie Ihr Konto, um ein Projekt liken zu können.", "accountVerificationEnsureFairVoting": "Die Kontoverifizierung stellt sicher, dass die Abstimmung fair bleibt und die Integrität des Wettbewerbs gewahrt wird.", "projectsSubmitted": "Eingereichte Projekte:", "unlikeThisProject": "Dieses Projekt nicht mehr mögen", "likeThisProject": "Dieses Projekt mögen", "likes": "Gefällt mir", "like": "Gefällt mir", "somethingWentWrong": "Etwas ist schiefgelaufen. Bitte versuchen Sie es später noch einmal", "voteError": "Abstimmungsfehler"}, "textsLiveYoutube": {"first": {"title": "Du bist eingeladen!", "description": "Wir gehen diesen Freitag live — und diese Session dreht sich ganz ums <PERSON>uen mit <green>Zweck</green>, <blue>Präzision</blue> und <orange>purem Vibe</orange>.", "secondDescription": "<PERSON><PERSON>, wie du <green>Vibe-Coding meisterst</green>, <blue>neue Funktionen entdeckst</blue> bevor alle anderen, und <orange>echte Projekte live entstehen lässt</orange>.", "specialText": "<PERSON><PERSON><PERSON> hier und drücke „Erinnere mich“ auf YouTube — verpasse nichts."}, "second": {"title": "Se<PERSON> der Erste beim <PERSON>.", "description": "Diesen Freitag heben wir <green>Vibe-Coding</green> auf ein neues Level — wir zeigen dir, wie du <blue>mit Absicht kreierst</blue>, <orange>neue Funktionen freischaltest</orange> und <green>schneller als je zuvor</green> startest.", "secondDescription": "<green>Neue Builds</green>, <blue>neue Ideen</blue> und <orange>neue Chancen</orange> zum <PERSON>.", "specialText": "<PERSON><PERSON><PERSON> hier, um deine YouTube-Erinnerung zu setzen — und sei Teil der nächsten Welle von Creators."}, "third": {"title": "<PERSON><PERSON><PERSON> dir deinen <PERSON>.", "description": "Unser nächster <green>Biela.dev Livestream</green> findet diesen Freitag statt — und du bist <blue>offiziell eingeladen</blue>.", "secondDescription": "Wir tauchen tief in <green>Vibe-Coding</green> ein, enthü<PERSON> <blue>mächtige neue Funktionen</blue> und helfen dir, <orange>kl<PERSON><PERSON>, schneller und größer</orange> zu bauen.", "specialText": "<PERSON><PERSON>e hier und tippe auf „Erinnere mich“ auf YouTube — hier könnte deine nächste großartige Idee beginnen."}}, "stream": {"title": "Sei <1>der <PERSON><PERSON><PERSON></1>, der <3>baut</3>.", "subtitle": "Schalte unseren Livestream am Freitag ein – Features, Projekte und exklusive Updates.", "button": "Stelle deine YouTube-Erinnerung ein."}, "billings": {"modalTitle": "<PERSON><PERSON><PERSON><PERSON> einen Plan", "modalSubtitle": "Wähle aus unseren drei flexiblen Plänen, um unbegrenzt weiter zu coden.", "toggleMonthly": "<PERSON><PERSON><PERSON>", "toggleYearly": "<PERSON><PERSON><PERSON><PERSON> (10 % sparen)", "mostPopular": "Am beliebtesten", "tokensLowerCase": "tokens", "tokensUpperCase": "Tokens", "below": "unten", "unlimited": "UNBEGRENZT", "10Bonus": "10 % Bonus", "currentPlan": "Aktueller Plan", "upgradePlan": "Plan upgraden", "purchaseDetails": "Kaufdetails", "dateLabel": "Datum", "planLabel": "Plan", "amountLabel": "Betrag", "whatsNext": "Wie geht es weiter", "yourTokensAvailable": "<PERSON><PERSON> sind jetzt in deinem Konto verfügbar", "purchaseDetailsStored": "Kaufdetails sind in deinem Konto gespeichert", "viewPurchaseHistory": "Du kannst deine Kaufhistorie im Abrechnungsbereich einsehen", "thankYou": "Danke!", "purchaseSuccessful": "<PERSON><PERSON> war erfolgreich", "startCoding": "<PERSON>e mit dem Coden", "month": "<PERSON><PERSON>", "year": "<PERSON><PERSON><PERSON>"}, "feature": {"basic_ai": "Basis KI-Entwicklung", "community_support": "Community-Support", "standard_response": "Standard-Antwortzeit", "basic_templates": "Basisvorlagen", "advanced_ai": "Fortgeschrittene KI-Entwicklung", "priority_support": "Priorisierter Support", "fast_response": "Schnelle Antwortzeit", "premium_templates": "Premium-Vorlagen", "team_collaboration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterprise_ai": "Enterprise KI-Entwicklung", "support_24_7": "24/7 Priorisierter Support", "instant_response": "Sofortige Antwortzeit", "custom_templates": "Individuelle Vorlagen", "advanced_team": "Erweiterte Team-Funktionen", "custom_integrations": "Benutzerdefinierte Integrationen", "big_context_window": "Großes Kontextfenster – 5x größer als das mittlere", "code_chat_ai": "Zugriff auf Code- & Chat-KI", "tokens_basic": "Genügend Token für ca. 3 Präsentationswebsites", "standard_webcontainer": "Dedizierter Standard-Webcontainer", "medium_context_window": "<PERSON><PERSON><PERSON> Kontextfenster – geeignet für Website, Blog oder einfaches Browserspiel", "basic_support": "Basis-Support mit Standard-Antwortzeit", "supabase_integration": "Supabase-Integration", "project_sharing": "Projekte mit Freunden teilen", "project_download": "Proje<PERSON>e herunt<PERSON>n", "project_deployment": "Bereitstellungsfunktion", "custom_domain": "Projekte mit eigener Domain verbinden", "tokens_creator_plus": "Genügend Token für ca. 7 Präsentationswebsites", "advanced_support": "Erweiterter Support mit schnellerer Antwortzeit", "prioritary_support": "Dedizierter priorisierter Support", "early_feature_access": "<PERSON><PERSON><PERSON> zu ne<PERSON>", "human_cto": "Dedizierter menschlicher CTO für Ihr Unternehmen", "community_promotion": "Ihr Unternehmen mit unserer Community fördern"}, "JoinUsLive": "Sei live dabei", "howToUseBiela": {"subtitle": "So verwendest du Biela", "title": "In wenigen Minuten erklärt", "description": "<PERSON><PERSON><PERSON>, wie jede Funktion funktioniert"}, "slides": {"newHistoryFeature": "Entdecke das neue VERLAUFS-Feature von Biela!", "newHistoryFeatureDescription": "Der VERLAUF-Tab sorgt für einen reibungslosen Vibe-Coding-Prozess, indem er es dir ermöglicht, frühere Versionen deines Projekts erneut aufzurufen und wiederherzustellen – damit du die volle Kontrolle behältst!", "fixPreview": "So behebst du die Vorschau in Biela!", "fixPreviewDescription": "Sieh dir die vollständige Anleitung an und stelle sicher, dass alles so angezeigt wird, wie du es gebaut hast.", "signUp": "So registrierst du dich bei Biela!", "signUpDescription": "Neu bei biela.dev? <PERSON>hau dir diese kurze Anleitung an, um zu erfahren, wie du dich registrierst und dein Konto verifizierst!", "buildWebsite": "So erstellst du eine Website mit Biela!", "buildWebsiteDescription": "<PERSON><PERSON>, wie du mit biela.dev eine Website von Grund auf erstellst und veröffentlichst – ganz ohne eine einzige Codezeile zu schreiben.", "downloadAndImport": "So lädst du Projekte & Chats herunter und importierst sie in Biela!", "downloadAndImportDescription": "In dieser Schritt-für-Schritt-Anleitung erfährst du, wie du abgeschlossene Projekte herunterlädst und gespeicherte Chats in deinen Arbeitsbereich importierst – damit du nahtlos weitermachen kannst.", "shareProject": "So teilst du dein Vibe-Code-Proje<PERSON> von BIELA!", "shareProjectDescription": "<PERSON><PERSON><PERSON>, dein Projekt professionell zu teilen? Sieh dir die vollständige Anleitung an und leg los.", "launchProject": "So veröffentlichst du dein Projekt auf deiner eigenen Domain!", "launchProjectDescription": "In diesem Video zeigen wir dir die einfachen Schritte, um deine Website live zu schalten – vom Setup bis zur Veröffentlichung.", "deployProject": "So stellst du dein Projekt bereit und gehst live!", "deployProjectDescription": "Diese kurze Anleitung führt dich durch alle Schritte, um deine Website, App oder dein Dashboard schnell und reibungslos bereitzustellen."}, "maintenance": {"title": "Wir arbeiten daran, das Problem zu beheben", "description": "Biela.dev ist derzeit im Wartungsmodus. Wir arbeiten daran, alles wieder online zu bringen.", "text": "Vielen Dank für Ihre Geduld."}, "planCard": {"descriptionBeginner": "Perfekt für Einsteiger, die ihre ersten Projekte erstellen und Webentwicklung lernen", "descriptionCreator": "Ideal für Kreative, die mehr Leistung und Flexibilität für fortgeschrittene Projekte benötigen", "descriptionProfessional": "Komplettlösung für professionelle Entwickler und Teams, die Anwendungen erstellen", "perMillionTokens": "$ {{price}} / pro 1M Tokens", "dedicatedCto": "Dedizierter CTO – 24 Stunden", "tokens": "Tokens", "perMonth": "pro <PERSON><PERSON>", "perYear": "pro Jahr", "freeTokens": "{{ tokens }} <PERSON><PERSON><PERSON><PERSON><PERSON>s", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON><PERSON><PERSON>", "save": "SPARE 10 %"}, "faqLabel": "Häufig gestellte Fragen", "faqHeading": "Unsere meistgestellten Fragen an<PERSON>hen", "faqSubheading": "Starten. Verstehen. Entdecken", "whatAreTokensQuestion": "Was sind <PERSON>?", "whatAreTokensAnswer1": "Tokens sind die Art und Weise, wie die KI dein Projekt verarbeitet. Auf Biela.dev werden Tokens verwendet, wenn du eine Eingabe an die KI sendest. Dabei werden deine Projektdateien in die Nachricht eingebunden, damit die KI deinen Code verstehen kann. Je größer dein Projekt und je länger die Antwort der KI, desto mehr Tokens werden benötigt.", "whatAreTokensAnswer2": "Außerdem wird eine kleine An<PERSON> von To<PERSON>s pro Minute von deinem Konto abgezogen, wenn du einen Webcontainer nutzt, um die Kosten für die Entwicklungsumgebung zu decken.", "freePlanTokensQuestion": "Wie viele Tokens bekomme ich im kostenlosen Plan?", "freePlanTokensAnswer": "Jeder Benutzer im kostenlosen Plan erhält täglich 200.000 Tokens, die jeden Tag erneuert werden.", "outOfTokensQuestion": "Was passiert, wenn ich keine Tokens mehr habe?", "outOfTokensAnswer": "<PERSON>ine Sorge! Du kannst jederzeit zu Menü → Abrechnung gehen und sofort zusätzliche Tokens kaufen, wenn nötig.", "changePlanQuestion": "Kann ich meinen Plan später ändern?", "changePlanAnswer": "Ja — j<PERSON><PERSON><PERSON>. <PERSON><PERSON>e ein<PERSON>ch zu Menü → <PERSON><PERSON><PERSON><PERSON><PERSON>, um deinen Plan zu ändern, ein Upgrade oder Downgrade durchzuführen.", "rolloverQuestion": "Werden ungenutzte Tokens übertragen?", "rolloverAnswer": "Zurzeit werden Tokens am Ablaufdatum zurückgesetzt und nicht übertragen — nutze sie also täglich optimal!", "exploreHowToUseBiela": "<PERSON><PERSON><PERSON><PERSON>, wie man <PERSON><PERSON><PERSON> verwendet", "new": "<PERSON>eu", "joinVibeCoding": "<PERSON>tt dem Vibe Coding Hackathon bei", "whatDoYouWantToBuild": "WAS MÖCHTEST DU ERSTELLEN?", "imaginePromptCreate": "Stelle es dir vor. Prompt. <PERSON><PERSON><PERSON>.", "levelOfAmbition": "Wie ehrgeizig bist du?", "startGrowScale": "Starten. Wachsen. Skalieren.", "calloutBar": {"start": "Alle Pläne starten kostenlos mit", "tokens": "200.000 Tokens pro Tag", "end": ". Keine Registrierung notwendig. Starte sofort mit dem Erstellen."}, "atPriceOf": "zum <PERSON><PERSON> von", "startFreeNoCreditCard": "<PERSON><PERSON><PERSON> starten – keine K<PERSON> er<PERSON>", "SignIn": "Anmelden", "Register": "Registrieren", "Affiliates": "Partnerprogramm", "UsernameRequired": "Benutzername ist erforderlich", "PasswordRequired": "Passwort ist erforderlich", "MissingUsernamePasswordOrTurnstile": "Benutzername, Passwort oder Turnstile-Token fehlen", "LoginSuccess": "Anmeldung erfolgreich!", "LoginFailed": "Anmeldung fehlgeschlagen", "EmailRequired": "E-Mail ist erforderlich", "EmailInvalid": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "CaptchaRequired": "Bitte vervollständigen Sie das CAPTCHA", "ResetLinkSent": "Ein Zurücksetzungslink wurde an Ihre E-Mail gesendet.", "ResetFailed": "Zurücksetzungslink konnte nicht gesendet werden", "BackToLogin": "Zurück zur Anmeldung", "EmailPlaceholder": "Ihre E-Mail", "SendResetLink": "Zurücksetzungslink senden", "loading": "<PERSON><PERSON><PERSON>", "checkingYourAuthenticity": "Authentizität wird überprüft", "ToUseBielaDev": "Um Biela.dev zu nutzen, müssen <PERSON> sich in ein bestehendes Konto einloggen oder eines mit einer der untenstehenden Optionen erstellen", "Sign in with Google": "Mit Google anmelden", "Sign in with GitHub": "Mit GitHub anmelden", "Sign in with Email and Password": "Mit E-Mail und Passwort anmelden", "DontHaveAnAccount": "Sie haben noch kein Konto?", "SignUp": "Registrieren", "ByUsingBielaDev": "Durch die Nutzung von Biela.dev stimmen Sie der Erfassung von Nutzungsdaten zu.", "EmailOrUsernamePlaceholder": "E-Mail/Benutzername", "passwordPlaceholder": "Passwort", "login.loading": "<PERSON><PERSON><PERSON>", "LoginInToProfile": "Melden Sie sich bei Ihrem Profil an", "login.back": "Zurück", "forgotPassword": "Passwort vergessen?", "PasswordsMismatch": "Passwörter stimmen nicht überein.", "PhoneRequired": "Telefonnummer ist erforderlich", "ConfirmPasswordRequired": "Bitte bestätigen Sie Ihr Passwort", "AcceptTermsRequired": "Sie müssen den Nutzungsbedingungen und der Datenschutzrichtlinie zustimmen", "RegistrationFailed": "Registrierung fehlgeschlagen", "EmailConfirmationSent": "Bestätigungs-E-Mail wurde gesendet. Bitte bestätigen Sie Ihre E-Mail und melden Sie sich dann an.", "RegistrationServerError": "Registrierung fehlgeschlagen (Server antwortete mit false).", "SomethingWentWrong": "Etwas ist schiefgelaufen", "CheckEmailConfirmRegistration": "Überprüfen Sie Ihre E-Mails, um Ihre Registrierung zu bestätigen", "EmailConfirmationSentText": "Wir haben Ihnen eine E-Mail mit einem Bestätigungslink gesendet.", "LoginToProfile": "Zum Profil anmelden", "username": "<PERSON><PERSON><PERSON><PERSON>", "email": "E-Mail", "PasswordPlaceholder": "Passwort", "ConfirmPasswordPlaceholder": "Passwort bestätigen", "AcceptTermsPrefix": "Ich stimme den", "TermsOfService": "Nutzungsbedingungen", "AndSeparator": "und", "PrivacyPolicy": "Datenschutzrichtlinie", "CreateAccount": "<PERSON><PERSON> er<PERSON>", "Back": "Zurück", "SignInWithGoogle": "Mit Google anmelden", "SignInWithGitHub": "Mit GitHub anmelden", "SignInWithEmailAndPassword": "Mit E-Mail und Passwort anmelden", "translation": {"AIModel": "KI-Modell", "UpgradePlan": "ein Premium-Plan", "PremiumBadge": "PREMIUM", "Active": "Aktiv", "projectInfo.features": "Funktionen", "Stats": "Statistiken", "Performance": "Le<PERSON><PERSON>", "Cost": "<PERSON><PERSON>", "UpgradeTooltip": "Freischalten durch Upgrade auf ", "UpgradeTooltipSuffix": "<PERSON><PERSON>", "chat": "Cha<PERSON>", "code": "Code"}, "extendedThinkingTooltip": "Aktiviere erweitertes Denken, damit die KI tiefere Antworten geben kann", "extendedThinkingTooltipDisabled": "Deaktiviere erweitertes Denken, um Ressourcen zu sparen", "AIModel": "KI-Modell", "UpgradePlan": "ein Premium-Plan", "PremiumBadge": "PREMIUM", "Active": "Aktiv", "projectInfo.features": "Funktionen", "Stats": "Statistiken", "Performance": "Le<PERSON><PERSON>", "Cost": "<PERSON><PERSON>", "UpgradeTooltip": "Freischalten durch Upgrade auf ", "UpgradeTooltipSuffix": "<PERSON><PERSON>", "HowBielaWorks": "<PERSON><PERSON> funktioniert", "WatchPromptLaunch": "Ansehen. Prompten. Starten.", "SearchVideos": "In der Bibliothek suchen...", "NewReleased": "NEU VERÖFFENTLICHT", "Playing": "Wird abgespielt", "NoVideosFound": "Keine Videos gefunden", "TryAdjustingYourSearchTerms": "Versuchen Sie, <PERSON>hre Suchbegriffe anzupassen", "feedback": {"title": {"issue": "Ein Problem melden", "feature": "Eine Funktion anfragen"}, "description": {"issue": "Haben Si<PERSON> einen Fehler gefunden oder ein Problem festgestellt? <PERSON>sen Si<PERSON> es uns wissen – wir kümmern uns darum.", "feature": "Haben Sie eine Idee für eine neue Funktion? Wir freuen uns auf Ihre Vorschläge zur Verbesserung unserer Plattform."}, "success": {"title": "Vielen Dank!", "issue": "Ihr Problem wurde erfolgreich übermittelt.", "feature": "Ihre Funktionsanfrage wurde erfolgreich übermittelt."}, "form": {"fullName": {"label": "Vollständiger Name", "placeholder": "Geben Sie Ihren vollständigen Namen ein"}, "email": {"label": "E-Mail-Adresse", "placeholder": "Geben Sie Ihre E-Mail-Adresse ein"}, "suggestion": {"labelIssue": "Problembeschreibung", "labelFeature": "Funktionsvorschlag", "placeholderIssue": "Beschreiben Sie das Problem, das Sie haben...", "placeholderFeature": "Beschreiben Sie die Funktion, die Si<PERSON> sich wünschen..."}, "screenshots": {"label": "Screenshots (optional)", "note": "<PERSON>ügen Sie Screenshots hinzu, damit wir das Problem besser nachvollziehen können", "drop": "<PERSON>lick<PERSON> Si<PERSON> zum Hochladen oder ziehen Sie Dateien hierher", "hint": "PNG, JPG, JPEG – jeweils bis zu 10 MB", "count": "{{count}} Bild(er) ausgewählt"}}, "buttons": {"cancel": "Abbrechen", "submitting": "Wird gesendet...", "submitIssue": "Problem senden", "submitFeature": "<PERSON><PERSON><PERSON> senden"}, "errors": {"fullNameRequired": "Vollständiger Name ist erforderlich", "fullNameNoNumbers": "Der vollständige Name darf keine Zahlen enthalten", "emailRequired": "E-Mail-Adresse ist erforderlich", "emailInvalid": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "suggestionRequired": "Bitte beschreiben Sie Ihr Problem oder Ihren Vorschlag"}}, "changelog": {"title": "Änderungsprotokoll", "description": "Immer. In Entwicklung. Vorwärts.", "TotalReleases": "Veröffentlichungen insgesamt", "ActiveUsers": "Aktive Nutzer", "FeaturesAdded": "Neue Funktionen", "BugsFixed": "<PERSON><PERSON><PERSON><PERSON>", "totalReleases": "Gesamte Versionen", "activeUsers": "Aktive Benutzer", "featuresAdded": "Hinzugefügte Features", "bugsFixed": "Behobene Bugs", "shortCallText": "Haben Sie Vorschläge oder einen Bug gefunden? Wir würden gerne von <PERSON>en hören!", "reportIssue": "<PERSON>", "requestFeature": "Feature Anfragen", "types": {"feature": "Feature", "improvement": "Verbesserung", "bugfix": "Bugfix", "ui/ux": "UI/UX", "announcement": "Ankündigung", "general": "Allgemein"}, "versions": {"v3.2.3": {"date": "26. <PERSON><PERSON> 2025", "changes": {"0": "Einige UI/UX-Probleme im Benutzer-Dashboard behoben.", "1": "WebContainer-Stabilität verbessert.", "2": "AI Diff-<PERSON><PERSON>, um kleine Dateien anders als große Dateien zu behandeln."}}, "v3.2.2": {"date": "24. <PERSON><PERSON> 2025", "changes": {"0": "Frontoffice reorganisiert und neu gestaltet für eine bessere Benutzererfahrung.", "1": "Diese großartige Changelog-Seite implementiert.", "2": "Den Wiederverbindungsalgorithmus des WebContainers verbessert.", "3": "Unterstützung für das Hochladen von Projekten über ZIP-<PERSON><PERSON> hinzugefügt, zusätz<PERSON> zu Ordner-Uploads.", "4": "Leistung aller API-Aufrufe durch bessere Indexierung verbessert."}}, "v3.2.1": {"date": "24. <PERSON><PERSON> 2025", "changes": {"0": "IDE: <PERSON> \"Ordner\"-Bereich des Benutzer-Dashboards wurde für eine verbesserte Erfahrung neu gestaltet.", "1": "Content Studio: Sie können jetzt Bilder im Content Studio kopieren."}}, "v3.2.0": {"date": "17. <PERSON><PERSON> 2025", "changes": {"0": "AI Diff-Modus (Experimentell): Die KI schreibt jetzt nur die Unterschiede zwischen der ursprünglichen Datei und der aktualisierten Version, anstatt die gesamte Datei zu regenerieren. Dies verbessert die Leistung erheblich und optimiert die Token-Nutzung. Sie können diese experimentelle Funktion im Bereich Einstellungen → Kontrollzentrum Ihres Projekts aktivieren oder deaktivieren."}}, "v3.1.6": {"date": "12. <PERSON><PERSON> 2025", "changes": {"0": "IDE: Ein Gutscheincode-Feld wurde für Abonnements und zusätzliche Token hinzugefügt."}}, "v3.1.5": {"date": "11. <PERSON><PERSON> 2025", "changes": {"0": "IDE: Verschiedene Korrekturen wurden am \"Projekt Teilen\"-Flow angewendet."}}, "v3.1.4": {"date": "10. <PERSON><PERSON> 2025", "changes": {"0": "IDE: Paket-Abonnements wurden überprüft und korrigiert, wodurch Crypto-Einbindungs- und Stripe-Implementierungsprobleme behoben wurden."}}, "v3.1.3": {"date": "9. <PERSON><PERSON> 2025", "changes": {"0": "KI: Direkte API-Integration für Gemini implementiert und getestet, um bessere Latenz zu erreichen."}}, "v3.1.2": {"date": "6. <PERSON><PERSON> 2025", "changes": {"0": "Affiliate: Affiliate-Auszahlungsaufgaben mit Design-Konsistenz mit dem neuen Dashboard-Informationsmodal (Auszahlungshistorie) implementiert.", "1": "Content Studio: Eine neue Option wurde hinz<PERSON>, damit <PERSON> en<PERSON>che<PERSON> können, ob sie Bilde<PERSON> zusammen mit Links an die KI senden. Standardmäßig ist dies auf \"nein\" gesetzt."}}, "v3.1.1": {"date": "5. <PERSON><PERSON> 2025", "changes": {"0": "Content Studio: Updates wurden vorgenommen, um das Content Studio pro Projekt zu organisieren."}}, "v3.1.0": {"date": "3. <PERSON><PERSON> 2025", "changes": {"0": "🚀 Großes BIELA.dev Update ist gerade gelandet! 🚀", "1": "Die KI schreibt jetzt 5x schneller Code als heute Morgen. Ja, <PERSON><PERSON> haben richtig gelesen. Wir haben BIELAs Leistung aufgeladen, damit <PERSON> in Windese<PERSON> von der Idee zum funktionierenden Code gelangen.", "2": "Terminal: Jetzt noch besser: Aufbauend auf der Überarbeitung von heute Morgen haben wir zusätzlichen UI/UX-Schliff für einen reibungsloseren Coding-Flow hinzugefügt.", "3": "Supabase-Verbindungsverbesserungen: Eine sauberere, intuitivere Oberfläche macht Setup und Integration mühelos.", "4": "Fehlerbehandlungs-Update (Auto-Behandlung von Fehlern): Automatische Fehlerberichterstattung wurde zurückgenommen. Wir bereiten eine neue Einstellung vor, damit <PERSON>e zwischen manueller und KI-unterstützter Behandlung wählen können.", "5": "Neues Kontrollzentrum (in Projekteinstellungen) & Unit-Testing-Schnittstelle: Erstes Feature: Unit-Testing-Terminal umschalten. Viele weitere Steuerungen kommen!", "6": "Verbesserte Download-Logik: Downloads warten jetzt, bis Ihr Code vollständig gepusht ist und gewährleisten jedes Mal vollständige und genaue Exporte."}}, "v3.0.0": {"date": "3. <PERSON><PERSON> 2025", "changes": {"0": "🚀 BIELA.dev Update - Hat gerade einen Power-Boost erhalten! 🚀", "1": "Überarbeitete Terminal-UI: Ein eleganteres, saubereres Design, das die Arbeit im Terminal zu einem Vergnügen macht.", "2": "Ein-Klick-NPM-Aktionen: Führen Sie npm install, npm run build oder npm run dev sofort mit neuen Aktionsschaltflächen aus - kein Tippen erforderlich! (das Feature ist in das Terminal integriert)", "3": "Intelligentere Fehlerbehandlung & Fehlerfarbenklassifikation: Vorschaufehler werden jetzt automatisch an die KI gesendet, damit sie Ihnen beim schnelleren Debuggen helfen kann. (Sie werden reibungslose Bug-Behebung durch BIELA erleben, während Sie Ihre Projekte entwickeln)", "4": "Verbesserte Dateinavigation: Ein Klick auf einen Dateinamen im Chat öffnet ihn jetzt direkt im WebContainer. Einfach tippen und bearbeiten!"}}, "v2.1.1": {"date": "30. <PERSON> 2025", "changes": {"0": "IDE: Ein Speichern-Button wurde für den WebContainer hinzugefügt, der bei manuellen Updates erscheint und ein Commit und Push zu GitHub auslöst.", "1": "Dashboard: Ein Fehler beim Umbenennen importierter Projekte vom Benutzer-Dashboard wurde behoben.", "2": "WebContainer: Eine dringende Behebung für ein Speicherleck wurde implementiert."}}, "v2.1.0": {"date": "28. <PERSON> 2025", "changes": {"0": "🚀 BIELA.dev Update — 7 neue Verbesserungen sind gerade gelandet! 🚀", "1": "IDE: Wir haben die Möglichkeit hinzugefügt, Ihre eigene Domain direkt von der Oberfläche aus mit Ihrem Biela-Projekt zu verknüpfen.", "2": "Bereitgestellte Projektvorschauen: Sie können jetzt Ihre bereitgestellten Projekte mit einem einzigen Klick auf das Augensymbol vorschauen - Sie müssen nicht jede<PERSON>, um zu wissen, was drin ist.", "3": "Kostenverfolgung in duplizierten Projekten behoben: Wir haben ein Problem behoben, bei dem duplizierte Projekte Kostenschätzungen vom ursprünglichen Projekt erbten. Jetzt beginnen sie bei null und geben Ihnen eine genaue Verfolgung pro Projekt.", "4": "Live-Kostenschätzung (pro Prompt): Kostenschätzungen werden jetzt nach jedem Prompt aktualisiert, nicht nur einmal am Tag. Das gibt Ihnen Echtzeit-Fe<PERSON><PERSON> dar<PERSON><PERSON>, was es kosten würde, dasselbe mit einer Dev-Agentur zu entwickeln - und wie viel Sie mit BIELA sparen.", "5": "Verlaufs-Tab & Rollback behoben: Sie können jetzt wieder sicher im Projektverlauf zurückgehen. Nach unseren jüngsten größeren Updates hatte dies Probleme - jetzt ist es butterweich.", "6": "Autoscroll-Verbesserungen: <PERSON>ine eingefrorenen Bildschirme mehr beim Codieren! Wir haben das Problem behoben, bei dem das Scrollen während der Generierung hinterherhinkte. Genießen Sie nahtlose Flows.", "7": "Menüseiten öffnen sich jetzt in neuen Tabs: <PERSON><PERSON> in Labs codieren und auf andere Menüelemente klicken, öffnen sie sich jetzt in einem neuen Tab, damit Sie nie Ihre aktuelle Arbeit verlieren."}}, "v2.0.4": {"date": "27. <PERSON> 2025", "changes": {"0": "Dashboard: Screenshot-Verbesserungen im Benutzer-Dashboard umfassen die Entfernung des Auto-Scrolls, benutzergesteuerte Scroll-Erlaubnis und das Hinzufügen einer Animation zum Laden von Screenshots."}}, "v2.0.3": {"date": "20. <PERSON> 2025", "changes": {"0": "Content Studio: Die Roboter-Animation im Content Studio wird jetzt in voller Größe angezeigt.", "1": "Affiliate: Anzeigeprobleme im Affiliate-Dashboard bei der Anzeige in Firefox behoben."}}, "v2.0.2": {"date": "19. <PERSON> 2025", "changes": {"0": "Content Studio: Die maximale An<PERSON>, die gleichzeitig im Content Studio erlaubt sind, wurde auf 50 erhöht (von 10)."}}, "v2.0.1": {"date": "18. <PERSON> 2025", "changes": {"0": "Content Studio: Ein Problem behoben, bei dem Tags für mehrere Dateien nicht aktualisiert wurden, es sei denn, die Seite wurde aktualisiert.", "1": "Content Studio: Der Bearbeiten-Button bei der Auswahl eines Bildes ist jetzt sichtbarer.", "2": "Content Studio: <PERSON><PERSON> wählt der Ordnerstandort jetzt automatisch den zuletzt erstellten oder zuletzt ausgewählten Ordner aus.", "3": "Content Studio: Der ausgewählte Ordner wird jetzt beim Z<PERSON>hen und <PERSON><PERSON>gen von Bildern beibehalten.", "4": "Content Studio: Der Tab-Titel verwendet jetzt eine neue Farbe (an<PERSON><PERSON> von g<PERSON>ü<PERSON>).", "5": "Content Studio: Der \"Bild einfügen\"-<PERSON><PERSON> wurde in \"im Projekt verwenden\" umbenannt.", "6": "Content Studio: <PERSON> Date<PERSON>-hochladen-Button hat jetzt einen grünen Hintergrund.", "7": "Content Studio: Die Höhe der Suchleiste wurde für bessere Ausrichtung verringert.", "8": "Content Studio: Die Suchleiste wird nicht mehr angezeigt, wenn es keine Bilder für den Benutzer gibt."}}, "v2.0.0": {"date": "16. <PERSON> 2025", "changes": {"0": "🚀 GROSSE UPDATES BEI BIELA.DEV! 🚀", "1": "Unsere eigene Web-Container-Technologie: Wir haben unsere eigene Web-Container-Technologie gebaut, die völlig unabhängig von externen Anbietern ist! Das gibt uns beispiellose Flexibilität, neue Features schneller und besser als je zuvor zu entwickeln.", "2": "Mehrere LLM-Optionen für Code-Generierung: Wir unterstützen jetzt mehrere KI-Modelle für Code-Generierung jenseits von Anthropic! Sie können jetzt Googles Gemini für Ihre Coding-Projekte verwenden, was einige große Vorteile bringt. Gemini bietet ein massives 1.000.000 Token Kontextfenster!", "3": "Content Studio: <PERSON><PERSON><PERSON>, <PERSON>hre Projekte: <PERSON><PERSON><PERSON> Si<PERSON> sich vollständig mit unserem neuen Content Studio aus! Jetzt können Sie Ihre eigenen Fotos hochladen, um sie in Ihren Vibe Coding-Projekten zu verwenden.", "4": "Projekt-Sharing: <PERSON>usammenarbeit wurde gerade besser! Jetzt können Sie Ihre Projekte mit anderen Benutzern teilen.", "5": "Verbindung zu bestehenden Supabase-Projekten: Diese mächtige neue Funktion ermöglicht es Ihnen, mehrere Biela.dev-Projekte mit derselben Supabase-Datenbank zu verbinden."}}, "v1.0.5": {"date": "15. <PERSON> 2025", "changes": {"0": "Zahlungen: Stripe-Zahlungsverarbeitung wurde implementiert.", "1": "Content Studio: Eine Animation (wie der Roboter) wird jetzt angezeigt, wenn es kein Bild auf der ersten Seite gibt."}}, "v1.0.4": {"date": "13. <PERSON> 2025", "changes": {"0": "Content Studio: Paginierungsprobleme im Content Studio behoben."}}, "v1.0.3": {"date": "5. <PERSON> 2025", "changes": {"0": "Content Studio gestartet - ein Ort zum Speichern und Verwalten all Ihrer Medien.", "1": "Frontoffice: Schema.org für Software-Apps auf dem biela_frontoffice implementiert.", "2": "Team-Dashboard: Benachrichtigungen wurden zum Team-Dashboard hinzugefügt (z.B. e<PERSON> <PERSON><PERSON>, wenn ein neues Mitglied beitritt oder eine neue Nachricht im Chat erscheint).", "3": "IDE: Ein Bug behoben, bei dem sich das Modal aus dem Menü nicht richtig schloss, wenn in bestimmte Bereiche des WebContainers geklickt wurde, während ein Prompt gegeben wurde."}}, "v1.0.2": {"date": "2. <PERSON> 2025", "changes": {"0": "Affiliate: Neugestaltung des Team-Mitglieder-Bereichs implementiert.", "1": "Affiliate: Affiliate-Dashboard-Icons wurden aktualisiert.", "2": "IDE: Der \"Supabase Verbinden\"-Button-Text wurde zu \"Datenbank\" mit einem neuen Icon geändert und zeigt jetzt \"Datenbank Verbunden\" in blau an, wenn verbunden."}}, "v1.0.1": {"date": "1. <PERSON> 2025", "changes": {"0": "IDE (Einstellungs-Tab): Datenbankintegration im Einstellungs-Chat implementiert.", "1": "Dashboard: Paginierung im Benutzer-Dashboard behoben."}}, "v1.0.0": {"date": "18. April 2025", "changes": {"0": "🚀 Willkommen bei Biela.dev: Ihr KI-gesteuerter Coding-Begleiter, kostenlos für alle! 🚀", "1": "Wir freuen uns, die erste öffentliche Veröffentlichung von Biela.dev anzukündigen! Wir glauben daran, modernste KI-unterstützte Webentwicklung für alle zugänglich zu machen, deshalb ist unsere Plattform vom ersten Tag an völlig kostenlos."}}}}}