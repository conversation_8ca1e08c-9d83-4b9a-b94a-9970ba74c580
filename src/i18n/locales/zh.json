{"meta": {"home": {"title": "biela.dev | AI驱动的网站和应用构建器 – 使用提示词构建", "description": "使用biela.dev将您的想法转变为实时网站或应用。使用AI驱动的提示词轻松创建自定义数字产品"}, "privacy": {"title": "biela.dev隐私政策", "description": "了解biela.dev如何收集、使用和保护您的个人信息。"}, "terms": {"title": "biela.dev服务条款", "description": "查看biela.dev人工智能驱动开发平台的使用条款和条件"}, "changelog": {"title": "biela.dev 更新日志 - 永远。进化。向前。", "description": "了解 biela.dev 如何随着时间的推移不断发展"}, "competitionterms": {"title": "Biela开发竞赛条款与条件", "description": "加入Biela开发竞赛——由Biela.dev和TeachMeCode学院联合发起的一项为期一个月的官方挑战赛。展示你的数字作品，通过社区互动赢得认可，并争夺最高荣誉。截止日期为2025年5月14日。"}, "contest": {"title": "biela.dev开发竞赛2025 – 赢取$10,000", "description": "加入biela.dev竞赛，展示您的AI构建项目并赢取现金奖励"}, "howTo": {"title": "如何使用 Biela.dev – 构建 AI 驱动应用的完整指南", "description": "了解如何使用 Biela.dev 快速构建 Web 应用程序，无需编写代码。完整的分步指南，包含教程和实用技巧。", "keywords": "biela, biela.dev, 如何使用 biela, 创建 AI 应用, 无需代码, 快速开发, biela 指南, biela 教程", "ogTitle": "如何使用 Biela.dev – 分步指南", "ogDescription": "学习如何使用 Biela.dev 构建 AI 应用。从想法到上线，无需编码。", "twitterTitle": "如何使用 Biela.dev – 完整指南", "twitterDescription": "使用 Biela.dev 构建 AI 应用，无需编写代码。请按照这个简单的分步指南操作！"}}, "navbar": {"home": "主页", "demo": "演示", "community": "社区", "affiliate": "联盟", "partners": "合作伙伴", "contest": "Vibe 编码黑客松", "subscriptions": "订阅", "pricing": "定价"}, "counter": {"days": "天", "hours": "小时", "minutes": "分钟", "seconds": "秒", "centisec": "百分之一秒"}, "hero": {"title": "在几分钟内将你的创意变成一个实时网站或应用", "subtitle": "如果你能<1>想象</1>它，你就能<5>编码</5>它。", "subtitle2": "免费开始。编写一切。将你的技能在每次提示中转化为机遇.", "timeRunningOut": "时间紧迫！", "launchDate": "WoodSnake 版本", "experimentalVersion": "Beta 测试版发布：2025年4月15日", "earlyAdopter": "立即加入，抢先享受早期用户福利，赶在正式发布前", "tryFree": "免费试用", "seeAiAction": "观看 AI 实时演示", "tellBiela": "告诉 Biela 创建", "inputPlaceholder": "如果你能想象它，BIELA 就能编写它的代码，我们今天做什么？", "chat": "聊天", "code": "编码", "checklist": "清单", "checklistTitle": "使用清单来", "checklistItems": {"trackDevelopment": "跟踪开发任务", "setRequirements": "设定项目需求", "createTesting": "创建测试协议"}, "registerNow": "立即注册", "attachFiles": "将文件附加到你的提示中", "voiceInput": "使用语音输入", "enhancePrompt": "优化提示", "cleanupProject": "整理项目", "suggestions": {"weatherDashboard": "创建天气仪表板", "ecommercePlatform": "构建电商平台", "socialMediaApp": "设计社交媒体应用", "portfolioWebsite": "生成作品集网站", "taskManagementApp": "创建任务管理应用", "fitnessTracker": "构建健身追踪器", "recipeSharingPlatform": "设计食谱分享平台", "travelBookingSite": "创建旅行预订网站", "learningPlatform": "构建学习平台", "musicStreamingApp": "设计音乐流媒体应用", "realEstateListing": "创建房地产列表", "jobBoard": "构建招聘网站"}}, "videoGallery": {"barber": {"title": "理发店网站", "description": "看看 Biela.dev 如何在几分钟内创建一个完整的理发店网站"}, "tictactoe": {"title": "井字游戏", "description": "观看 AI 创建井字游戏，比你说‘井字游戏’还快"}, "coffeeShop": {"title": "咖啡店网站", "description": "看看 Biela.dev 如何创建一个咖啡店网站"}, "electronicsStore": {"title": "电子商店网站", "description": "看看 Biela.dev 如何在几分钟内创建一个完整的在线商店"}, "seoAgency": {"title": "SEO代理机构网站", "description": "观看 AI 创建一个SEO代理机构网站"}}, "telegram": {"title": "加入我们的 Telegram", "subtitle": "与 Biela 社区连接", "description": "获得即时支持，分享你的项目，并与来自世界各地的 AI 爱好者交流。<br/><br/>我们的 Telegram 社区是关注 Biela.dev 的最快方式。", "stats": {"members": "成员", "support": "支持", "countries": "国家", "projects": "项目"}, "joinButton": "加入我们的 Telegram 社区", "communityTitle": "Biela.dev 社区", "membersCount": "成员", "onlineCount": "当前在线", "messages": {"1": "我刚刚在不到一小时内用 Biela.dev 启动了我的电商网站！", "2": "看起来太棒了！你是怎么处理产品目录的？", "3": "Biela 自动处理了！我只是描述了我的需求。", "4": "我正在构建一个作品集网站。AI 的建议太棒了！", "5": "有没有人试过新的响应式模板？它们在移动设备上看起来非常棒。"}}, "joinNetwork": {"title": "加入我们的网络", "subtitle": "连接。成长。获利。", "affiliateProgram": "联盟计划", "registerBring": "注册并邀请 3 位联盟成员", "aiCourse": "AI 工程课程", "courseDescription": "与 TeachMeCode 合作的免费全面 AI 课程", "digitalBook": "数字图书", "bookDescription": "《The Art of Prompt Engineering》独家数字版", "exclusiveBenefits": "独家福利", "benefitsDescription": "终身收益和会员专享特权", "registerNow": "立即注册", "teamEarnings": "团队收益", "buildNetwork": "构建你的网络", "weeklyMentorship": "每周指导", "teamBonuses": "团队奖金", "startEarning": "今天开始赚钱", "upToCommission": "最高可获得佣金"}, "partners": {"title": "受行业领导者信赖", "subtitle": "与全球创新者合作，共同塑造 AI 开发的未来", "strategicPartnerships": "战略合作伙伴", "joinNetwork": "加入不断壮大的合作伙伴和企业网络，与 BIELA 一起变革开发"}, "demo": {"title": "观看 AI 实时构建应用与网站", "subtitle": "探索企业如何通过 Biela.dev 转变数字化形象", "seeInAction": "观看 Biela.dev 的实际演示", "whatKind": "你需要哪种类型的网站？", "describeWebsite": "描述你的网页创意（例如，‘为摄影师制作的作品集’）", "generatePreview": "生成预览", "nextDemo": "下一个演示", "startBuilding": "今天就开始用 Biela.dev 构建并赚钱"}, "footer": {"quickLinks": {"title": "快速链接", "register": "注册", "bielaInAction": "观看 Biela.dev 实时演示", "liveOnTelegram": "我们在 Telegram 上直播", "affiliate": "协作式联盟营销", "partners": "合作伙伴"}, "legal": {"title": "法律声明", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. 保留所有权利.", "unauthorized": "未经明确书面许可，严禁全部或部分使用、复制或分发本服务."}, "reservations": "TeachMeCode Institute 保留与 Biela 相关知识产权的所有法律权利."}, "bottomBar": {"left": "2025 Biela.dev. 由 %s 提供支持 © 保留所有权利.", "allRights": "保留所有权利.", "right": "由 %s 开发"}}, "common": {"loading": "加载中...", "error": "发生错误", "next": "下一步", "previous": "上一步", "submit": "提交", "cancel": "取消", "close": "关闭", "loginNow": "立即登录", "verifyAccount": "验证账户"}, "languageSwitcher": {"language": "语言"}, "contest": {"bielaDevelopment": "氛围编码黑客松", "competition": "竞赛", "firstPlace": "第一名", "secondPlace": "第二名", "thirdPlace": "第三名", "currentLeader": "当前领先者：", "footerText": "由Biela.dev提供AI开发技术，TeachMeCode®研究院支持", "browseHackathonSubmissions": "浏览黑客松提交内容"}, "competition": {"header": {"mainTitle": "你的提示。你的风格。你的游乐场。", "timelineTitle": "时间表", "timelineDesc": "一个月打造你最出色的AI生成项目", "eligibilityTitle": "参赛资格", "eligibilityDesc": "Biela.dev上提交项目的活跃账户", "prizesTitle": "奖品", "prizesDesc": "16,000美元现金奖项及获奖者免费使用权", "votingTitle": "投票", "votingDesc": "社区驱动，每位验证用户仅能投一次票", "tagline": "让你的创意发声，让世界投票。"}, "details": {"howToEnter": "如何参与", "enter": {"step1": {"title": "使用Biela.dev的AI创建项目", "desc": "利用Biela强大的AI工具构建你的创新项目"}, "step2": {"title": "进入你的用户面板", "desc": "点击项目旁边的“发布到黑客松”", "subdesc": "你最多可以提交3个不同的项目"}, "step3": {"title": "Biela会处理你的参赛作品：", "list1": "截取你的项目截图", "list2": "检查你的提示是否符合参赛资格", "list3": "生成摘要和描述"}}, "prizeStructure": "奖项结构", "prizes": {"firstPlace": "第一名", "firstPlaceValue": "$10,000", "secondPlace": "第二名", "secondPlaceValue": "$5,000", "thirdPlace": "第三名", "thirdPlaceValue": "$1,000", "fourthToTenth": "第四至第十名", "fourthToTenthValue": "30天无限制访问"}}, "qualification": {"heading": "资格要求", "description": "要获得前三名奖金（10,000美元、5,000美元和1,000美元），你需要满足以下条件：", "criteria1": "至少拥有3个活跃推荐", "criteria2": "至少给展览墙上的一个项目点赞", "proTipLabel": "专业提示：", "proTipDesc": "你提交得越早，项目获得的关注和投票就越多！"}, "voting": {"heading": "投票指南", "oneVotePolicyTitle": "单一投票政策", "oneVotePolicyDesc": "每人仅可投一次票，且不得为自己的项目投票", "accountVerificationTitle": "账户验证", "accountVerificationDesc": "只有经过验证的Biela.dev账户才能投票", "tieBreakingTitle": "平局决胜", "tieBreakingDesc": "若出现平局，决定因素将是项目创作者获得的总星数（无论是否使用）", "howToVoteTitle": "如何投票", "howToVoteDesc": "浏览展览墙，查看所有发布的AI生成项目，然后点击爱心图标为你喜爱的项目投票！"}}, "contestItems": {"projectShowcase": "项目展示", "loading": "加载中...", "createBy": "创建者: ", "viewProject": "查看项目", "stars": "星星", "overview": "概览", "keyFeatures": "主要功能", "exploreLiveProject": "探索实时项目", "by": "由", "likeLimitReached": "达到点赞上限", "youCanLikeMaximumOneProject": "您最多可以点赞一个项目。您是否希望取消当前的点赞并改为点赞此项目？", "currentlyLikedProject": "当前点赞的项目", "switchMyLike": "切换我的点赞", "mustLoggedIntoLikeProject": "您必须登录才能点赞项目。", "signInToBielaAccountToVote": "请登录您的 Biela.dev 账户以参与投票过程。", "yourAccountIsNotVerifiedYet": "您的账户尚未验证。请验证您的账户以点赞项目。", "accountVerificationEnsureFairVoting": "账户验证确保公平投票，并有助于维护比赛的公正性。", "projectsSubmitted": "已提交的项目：", "unlikeThisProject": "取消喜欢此项目", "likeThisProject": "喜欢此项目", "likes": "点赞", "like": "点赞", "somethingWentWrong": "出了点问题。请稍后再试", "voteError": "投票错误"}, "textsLiveYoutube": {"first": {"title": "诚邀您的参与！", "description": "本周五我们将进行直播——本次环节专注于以<green>目标</green>、<blue>精准</blue>和<orange>纯粹氛围</orange>进行构建。", "secondDescription": "了解如何<green>精通 vibe 编码</green>、<blue>发现新功能</blue>，并<orange>观看真实项目</orange>实时构建。", "specialText": "点击此处并在 YouTube 上点击「通知我」—不要错过。"}, "second": {"title": "抢先构建。", "description": "本周五，我们将把<green>vibe 编码</green>提升到新高度—向您展示如何<blue>有意图地创建</blue>、<orange>解锁新功能</orange>，并以前所未有的速度<green>快速发布</green>。", "secondDescription": "<green>新构建</green>、<blue>新创意</blue>以及<orange>新机遇</orange>。", "specialText": "点击此处设置 YouTube 提醒—成为下一波创作者的一员。"}, "third": {"title": "预留您的席位。", "description": "我们的下一场<green>Biela.dev 直播</green>将在本周五举行—您已<blue>正式受邀</blue>。", "secondDescription": "我们将深入探讨<green>vibe 编码</green>、揭示<blue>强大新功能</blue>，并帮助您以<orange>更智能、更快速、更大规模</orange>进行构建。", "specialText": "点击此处并在 YouTube 上点击「通知我」—您的下一个大创意或将在此启航。"}}, "stream": {"title": "成为<1>第一个</1><3>构建</3>的人。", "subtitle": "加入我们周五的直播 — 功能、项目和独家更新。", "button": "设置您的 YouTube 提醒。"}, "billings": {"modalTitle": "选择一个计划", "modalSubtitle": "从我们的三个灵活计划中选择，以便无限制地继续编码。", "toggleMonthly": "按月", "toggleYearly": "按年（节省10%）", "mostPopular": "最受欢迎", "tokensLowerCase": "代币", "tokensUpperCase": "代币", "below": "以下", "unlimited": "无限制", "10Bonus": "10% 奖励", "currentPlan": "当前计划", "upgradePlan": "升级计划", "purchaseDetails": "购买详情", "dateLabel": "日期", "planLabel": "计划", "amountLabel": "金额", "whatsNext": "下一步", "yourTokensAvailable": "您的代币现已存入账户", "purchaseDetailsStored": "购买详情已存储在您的账户中", "viewPurchaseHistory": "您可以在账单部分查看购买历史", "thankYou": "谢谢！", "purchaseSuccessful": "购买成功", "startCoding": "开始编码", "month": "月", "year": "年"}, "feature": {"basic_ai": "基础人工智能开发", "community_support": "社区支持", "standard_response": "标准响应时间", "basic_templates": "基础模板", "advanced_ai": "高级人工智能开发", "priority_support": "优先支持", "fast_response": "快速响应时间", "premium_templates": "高级模板", "team_collaboration": "团队协作", "enterprise_ai": "企业级人工智能开发", "support_24_7": "全天候优先支持", "instant_response": "即时响应时间", "custom_templates": "自定义模板", "advanced_team": "高级团队功能", "custom_integrations": "自定义集成", "big_context_window": "大上下文窗口 - 比中等窗口大5倍", "code_chat_ai": "访问代码与聊天人工智能", "tokens_basic": "足够约支持3个展示网站的代币", "standard_webcontainer": "专用标准网络容器", "medium_context_window": "中等上下文窗口 - 适用于网站、博客或基础浏览器游戏", "basic_support": "基础支持，标准响应时间", "supabase_integration": "Supabase集成", "project_sharing": "与好友共享项目", "project_download": "下载您的项目", "project_deployment": "部署功能", "custom_domain": "将项目连接到您自己的域名", "tokens_creator_plus": "足够约支持7个展示网站的代币", "advanced_support": "高级支持，更快响应时间", "prioritary_support": "专用优先支持", "early_feature_access": "新功能提前体验", "human_cto": "为您的业务提供专属人类CTO", "community_promotion": "通过我们的社区推广您的业务"}, "JoinUsLive": "加入我们的直播", "howToUseBiela": {"subtitle": "如何使用 Biela", "title": "几分钟内讲解", "description": "简要说明每个功能的工作原理"}, "slides": {"newHistoryFeature": "发现 Biela 的新历史功能！", "newHistoryFeatureDescription": "历史选项卡可帮助您无缝管理 Vibe 编码过程，让您可以返回并恢复项目的任何先前版本，保持完全掌控！", "fixPreview": "如何修复 Biela 上的预览问题！", "fixPreviewDescription": "观看完整指南，确保显示效果与构建时完全一致。", "signUp": "如何注册 Biela！", "signUpDescription": "第一次使用 biela.dev？观看本快速指南，学习如何注册并验证您的账户！", "buildWebsite": "如何使用 Biela 构建网站！", "buildWebsiteDescription": "学习如何使用 biela.dev 从零开始构建并发布网站 —— 无需编写任何代码。", "downloadAndImport": "如何下载并导入项目与聊天记录！", "downloadAndImportDescription": "在本分步指南中，您将学习如何下载已完成的项目，并将保存的聊天记录导入工作区 —— 以便您随时继续保持 Vibe 编码的流程。", "shareProject": "如何分享您的 Vibe Code BIELA 项目！", "shareProjectDescription": "准备好专业地分享您的项目了吗？观看完整指南并立即开始。", "launchProject": "如何在您自己的域名上发布项目！", "launchProjectDescription": "在本视频中，我们将一步步引导您从设置到最终发布网站，助您自信地从构建走向上线。", "deployProject": "如何部署项目并上线！", "deployProjectDescription": "本快速指南将带您逐步完成部署网站、应用或仪表板的每一个步骤 —— 快速流畅。"}, "maintenance": {"title": "我们正在努力解决这个问题", "description": "Biela.dev 正在维护模式下运行。我们正在努力解决这个问题", "text": "感谢您的耐心等待。"}, "planCard": {"descriptionBeginner": "非常适合刚开始构建项目并学习网页开发的初学者", "descriptionCreator": "非常适合需要更强大功能和灵活性来处理高级项目的创作者", "descriptionProfessional": "为构建应用程序的专业开发人员和团队提供完整解决方案", "perMillionTokens": "$ {{price}} / 每百万代币", "dedicatedCto": "专属 CTO - 24 小时服务", "tokens": "代币", "perMonth": "每月", "perYear": "每年", "freeTokens": "{{ tokens }} 免费代币", "monthly": "每月", "yearly": "每年", "save": "节省 10%"}, "faqLabel": "常见问题", "faqHeading": "查看我们最常被问的问题", "faqSubheading": "开始。了解。探索", "whatAreTokensQuestion": "什么是令牌（tokens）？", "whatAreTokensAnswer1": "令牌是 AI 处理你的项目的方式。在 Biela.dev，当你向 AI 发送提示时会使用令牌。在此过程中，你的项目文件会被包含在消息中，以帮助 AI 理解你的代码。项目越大，AI 回复越长，所需的令牌也就越多。", "whatAreTokensAnswer2": "此外，每当你使用 Web 容器时，系统会每分钟从你的账户中扣除少量令牌，用于支付开发环境的成本。", "freePlanTokensQuestion": "免费套餐每天有多少令牌？", "freePlanTokensAnswer": "每个使用免费套餐的用户每天可获得 200,000 个令牌，每日刷新。", "outOfTokensQuestion": "如果我用完了令牌会发生什么？", "outOfTokensAnswer": "别担心！你可以随时前往 菜单 → 账单 页面，立即购买更多令牌。", "changePlanQuestion": "我可以以后更改套餐吗？", "changePlanAnswer": "可以 — 任何时候都可以。只需前往 菜单 → 账单，根据你的需求进行升级、降级或切换套餐。", "rolloverQuestion": "未使用的令牌会结转吗？", "rolloverAnswer": "目前，令牌会在到期日重置，不会结转 — 所以请每天充分利用它们！", "exploreHowToUseBiela": "探索如何使用 Biela", "new": "新", "joinVibeCoding": "加入 Vibe Coding 黑客松", "whatDoYouWantToBuild": "你想构建什么？", "imaginePromptCreate": "想象。提示。创造。", "levelOfAmbition": "你的雄心有多大？", "startGrowScale": "开始。成长。扩展。", "calloutBar": {"start": "所有计划均可免费开始", "tokens": "每天 200,000 个代币", "end": "。无需注册流程。立即开始构建。"}, "atPriceOf": "价格为", "startFreeNoCreditCard": "免费开始 - 无需信用卡", "SignIn": "登录", "Register": "注册", "Affiliates": "联盟", "UsernameRequired": "需要用户名", "PasswordRequired": "需要密码", "MissingUsernamePasswordOrTurnstile": "缺少用户名、密码或Turnstile令牌", "LoginSuccess": "登录成功！", "LoginFailed": "登录失败", "EmailRequired": "需要电子邮件", "EmailInvalid": "请输入有效的电子邮件地址", "CaptchaRequired": "请完成验证码", "ResetLinkSent": "重置链接已发送到您的电子邮件。", "ResetFailed": "发送重置链接失败", "BackToLogin": "返回登录", "EmailPlaceholder": "您的电子邮件", "SendResetLink": "发送重置链接", "loading": "加载中", "checkingYourAuthenticity": "正在验证您的身份", "ToUseBielaDev": "要使用 Biela.dev，您必须登录现有帐户或使用以下选项之一创建帐户", "Sign in with Google": "使用 Google 登录", "Sign in with GitHub": "使用 GitHub 登录", "Sign in with Email and Password": "使用电子邮件和密码登录", "DontHaveAnAccount": "还没有帐户？", "SignUp": "注册", "ByUsingBielaDev": "使用 Biela.dev 即表示您同意我们收集使用数据。", "EmailOrUsernamePlaceholder": "电子邮件/用户名", "passwordPlaceholder": "密码", "login.loading": "加载中", "LoginInToProfile": "登录到您的个人资料", "login.back": "返回", "forgotPassword": "忘记密码？", "PasswordsMismatch": "密码不匹配", "PhoneRequired": "需要电话号码", "ConfirmPasswordRequired": "请确认您的密码", "AcceptTermsRequired": "您必须接受服务条款和隐私政策", "RegistrationFailed": "注册失败", "EmailConfirmationSent": "确认电子邮件已发送。请确认您的电子邮件后登录。", "RegistrationServerError": "注册失败（服务器返回 false）。", "SomethingWentWrong": "出现问题", "CheckEmailConfirmRegistration": "请检查您的电子邮件以确认注册", "EmailConfirmationSentText": "我们已向您发送确认链接的电子邮件。", "LoginToProfile": "登录到个人资料", "username": "用户名", "email": "电子邮件", "PasswordPlaceholder": "密码", "ConfirmPasswordPlaceholder": "确认密码", "AcceptTermsPrefix": "我同意", "TermsOfService": "服务条款", "AndSeparator": "和", "PrivacyPolicy": "隐私政策", "CreateAccount": "创建帐户", "Back": "返回", "SignInWithGoogle": "使用 Google 登录", "SignInWithGitHub": "使用 GitHub 登录", "SignInWithEmailAndPassword": "使用电子邮件和密码登录", "translation": {"AIModel": "AI模型", "UpgradePlan": "高级计划", "PremiumBadge": "高级", "Active": "已激活", "projectInfo.features": "功能", "Stats": "统计", "Performance": "性能", "Cost": "成本", "UpgradeTooltip": "通过升级解锁 ", "UpgradeTooltipSuffix": "套餐", "chat": "聊天", "code": "代码"}, "extendedThinkingTooltip": "允许AI在回应前进行更深层次的思考", "extendedThinkingTooltipDisabled": "为节省资源，已禁用深层思考", "AIModel": "AI模型", "UpgradePlan": "高级计划", "PremiumBadge": "高级", "Active": "已激活", "projectInfo.features": "功能", "Stats": "统计", "Performance": "性能", "Cost": "成本", "UpgradeTooltip": "通过升级解锁 ", "UpgradeTooltipSuffix": "套餐", "HowBielaWorks": "BIELA 如何运作", "WatchPromptLaunch": "观看。提示。启动。", "SearchVideos": "在资料库中搜索...", "NewReleased": "最新发布", "Playing": "正在播放", "NoVideosFound": "未找到视频", "TryAdjustingYourSearchTerms": "尝试调整您的搜索词", "feedback": {"title": {"issue": "报告问题", "feature": "请求新功能"}, "description": {"issue": "发现 bug 或遇到问题？请告诉我们，我们会尽快修复。", "feature": "有新的功能建议？欢迎反馈，帮助我们改进平台。"}, "success": {"title": "谢谢您！", "issue": "您的问题报告已成功提交。", "feature": "您的功能请求已成功提交。"}, "form": {"fullName": {"label": "全名", "placeholder": "请输入您的全名"}, "email": {"label": "电子邮件地址", "placeholder": "请输入您的电子邮件地址"}, "suggestion": {"labelIssue": "问题描述", "labelFeature": "功能建议", "placeholderIssue": "描述您遇到的问题……", "placeholderFeature": "描述您希望看到的功能……"}, "screenshots": {"label": "截图（可选）", "note": "添加截图可以帮助我们更好地理解问题", "drop": "点击上传或拖放", "hint": "支持 PNG、JPG、JPEG，每张不超过 10MB", "count": "已选择 {{count}} 张图片"}}, "buttons": {"cancel": "取消", "submitting": "提交中……", "submitIssue": "提交问题", "submitFeature": "提交请求"}, "errors": {"fullNameRequired": "全名为必填项", "fullNameNoNumbers": "全名不能包含数字", "emailRequired": "邮箱地址为必填项", "emailInvalid": "请输入有效的邮箱地址", "suggestionRequired": "请描述您的问题或建议"}}, "changelog": {"title": "更新日志", "description": "始终在进化，持续向前", "TotalReleases": "版本总数", "ActiveUsers": "活跃用户", "FeaturesAdded": "新增功能", "BugsFixed": "修复的错误", "shortCallText": "有建议或发现了 bug？我们非常乐意听取您的反馈！", "reportIssue": "报告问题", "requestFeature": "请求新功能", "totalReleases": "总发布数", "activeUsers": "活跃用户", "featuresAdded": "新增功能", "bugsFixed": "修复错误", "types": {"feature": "功能", "improvement": "改进", "bugfix": "错误修复", "ui/ux": "用户界面/用户体验", "announcement": "公告", "general": "一般"}, "versions": {"v3.2.3": {"date": "2025年6月26日", "changes": {"0": "修复了用户仪表板中的一些用户界面/用户体验问题。", "1": "改进了WebContainer的稳定性。", "2": "改进了AI差异模式，以不同方式处理小文件和大文件。"}}, "v3.2.2": {"date": "2025年6月24日", "changes": {"0": "重新组织和重新设计前台办公室，以获得更好的用户体验。", "1": "实现了这个令人印象深刻的更新日志页面。", "2": "改进了WebContainer的重连算法。", "3": "除了文件夹上传外，还添加了通过ZIP文件上传项目的支持。", "4": "通过利用更好的索引改进了所有API调用的性能。"}}, "v3.2.1": {"date": "2025年6月24日", "changes": {"0": "IDE：用户仪表板的\"文件夹\"部分已重新设计，以获得改进的体验。", "1": "内容工作室：现在您可以在内容工作室中复制图像。"}}, "v3.2.0": {"date": "2025年6月17日", "changes": {"0": "AI差异模式（实验性）：AI现在只写原始文件和更新版本之间的差异，而不是重新生成整个文件。这显著提高了性能并优化了令牌使用。您可以从项目的设置→控制中心区域启用或禁用此实验功能。"}}, "v3.1.6": {"date": "2025年6月12日", "changes": {"0": "IDE：为订阅和额外令牌添加了优惠券代码字段。"}}, "v3.1.5": {"date": "2025年6月11日", "changes": {"0": "IDE：对\"分享项目\"流程应用了各种修复。"}}, "v3.1.4": {"date": "2025年6月10日", "changes": {"0": "IDE：重新检查和纠正了包订阅，解决了加密包含和Stripe实现问题。"}}, "v3.1.3": {"date": "2025年6月9日", "changes": {"0": "AI：实现并测试了Gemini的直接API集成以实现更好的延迟。"}}, "v3.1.2": {"date": "2025年6月6日", "changes": {"0": "联盟：实现了联盟支付任务，与新仪表板信息模态的设计保持一致（支付历史）。", "1": "内容工作室：添加了一个新选项，供用户决定是否将图像与链接一起发送给AI。默认情况下，这设置为\"否\"。"}}, "v3.1.1": {"date": "2025年6月5日", "changes": {"0": "内容工作室：进行了更新，按项目组织内容工作室。"}}, "v3.1.0": {"date": "2025年6月3日", "changes": {"0": "🚀 BIELA.dev重大更新刚刚到来！🚀", "1": "AI现在写代码的速度比今天早上快5倍。是的，您没看错。我们已经为BIELA的性能充电，这样您就可以在瞬间从想法到工作代码。", "2": "终端：现在更好了：基于今天早上的改进，我们添加了额外的UI/UX改进，以获得更流畅的编码流程。", "3": "Supabase连接改进：更清洁、更直观的界面使设置和集成毫不费力。", "4": "错误处理更新（错误的自动处理）：自动错误报告已被撤回。我们正在准备一个新设置，这样您就可以在手动和AI辅助处理之间进行选择。", "5": "新控制中心（在项目设置中）和单元测试界面：第一个功能：切换单元测试终端。更多控制即将到来！", "6": "改进的下载逻辑：下载现在等待您的代码完全推送，确保每次都有完整准确的导出。"}}, "v3.0.0": {"date": "2025年6月3日", "changes": {"0": "🚀 BIELA.dev更新 - 刚刚获得了动力提升！🚀", "1": "改进的终端UI：更时尚、更清洁的设计，使在终端中工作成为一种乐趣。", "2": "一键NPM操作：使用新的操作按钮立即运行npm install、npm run build或npm run dev，无需键入！（该功能集成到终端中）", "3": "更智能的错误处理和错误颜色分类：预览错误现在自动发送给AI，以便它可以帮助您更快地调试。（在开发项目时，您将体验到BIELA的流畅错误修复）", "4": "改进的文件导航：点击聊天中的文件名现在直接在WebContainer中打开它。只需点击并编辑！"}}, "v2.1.1": {"date": "2025年5月30日", "changes": {"0": "IDE：为WebContainer添加了保存按钮，在进行手动更新时出现，触发提交并推送到GitHub。", "1": "仪表板：修复了从用户仪表板重命名导入项目时发生的错误。", "2": "WebContainer：实现了内存泄漏的紧急修复。"}}, "v2.1.0": {"date": "2025年5月28日", "changes": {"0": "🚀 BIELA.dev更新 — 7项新改进刚刚到来！🚀", "1": "IDE：我们添加了将您自己的域名链接到您的biela项目的可能性，直接从界面。", "2": "部署项目预览：现在您可以通过单击眼睛图标预览您的部署项目，无需打开每个项目来了解里面的内容。", "3": "修复了重复项目中的成本跟踪：我们纠正了重复项目从原始项目继承成本估算的问题。现在它们从零开始，为您提供每个项目的准确跟踪。", "4": "实时成本估算（每个提示）：成本估算现在在每个提示后更新，而不仅仅是每天一次。这为您提供实时反馈，了解使用开发机构开发相同内容的成本，以及您使用BIELA节省了多少。", "5": "历史选项卡和回滚修复：现在您可以再次安全地回滚项目历史。在我们最近的重大更新之后，这有问题，现在非常流畅。", "6": "自动滚动改进：编码时不再有冻结的屏幕！我们修复了生成期间滚动滞后的问题。享受流畅的流程。", "7": "菜单页面现在在新选项卡中打开：如果您在Labs中编码并点击其他菜单项，它们现在在新选项卡中打开，这样您永远不会丢失当前正在进行的工作。"}}, "v2.0.4": {"date": "2025年5月27日", "changes": {"0": "仪表板：用户仪表板上的屏幕截图改进包括删除自动滚动，允许用户控制的滚动，并为加载屏幕截图添加动画。"}}, "v2.0.3": {"date": "2025年5月20日", "changes": {"0": "内容工作室：内容工作室中的机器人动画现在以全尺寸显示。", "1": "联盟：修复了在Firefox中查看时联盟仪表板上的显示问题。"}}, "v2.0.2": {"date": "2025年5月19日", "changes": {"0": "内容工作室：内容工作室中一次允许的最大文件数已增加到50个（从10个）。"}}, "v2.0.1": {"date": "2025年5月18日", "changes": {"0": "内容工作室：修复了多个文件的标签不更新的问题，除非页面被刷新。", "1": "内容工作室：选择图像时的编辑按钮现在更加可见。", "2": "内容工作室：上传时，文件夹位置现在自动选择最后创建或最后选择的文件夹。", "3": "内容工作室：拖放图像时现在保留选择的文件夹。", "4": "内容工作室：选项卡标题现在使用新颜色（而不是绿色）。", "5": "内容工作室：\"插入图像\"按钮已重命名为\"在项目中使用\"。", "6": "内容工作室：上传文件按钮现在有绿色背景。", "7": "内容工作室：搜索栏的高度已降低以获得更好的对齐。", "8": "内容工作室：如果用户没有图像，则不再显示搜索栏。"}}, "v2.0.0": {"date": "2025年5月16日", "changes": {"0": "�� BIELA.DEV的重大更新！🚀", "1": "我们自己的Web容器技术：我们构建了自己的Web容器技术，完全独立于任何外部提供商！这为我们提供了前所未有的灵活性，可以比以往任何时候更快更好地开发新功能。", "2": "代码生成的多个LLM选项：我们现在支持除Anthropic之外的多个AI模型进行代码生成！现在您可以使用Google的Gemini进行编码项目，这带来了一些重大优势。Gemini提供了100万个令牌的巨大上下文窗口！", "3": "内容工作室：您的照片，您的项目：通过我们新的内容工作室充分表达自己！现在您可以上传自己的照片用于您的Vibe Coding项目。", "4": "项目共享：协作刚刚变得更好！现在您可以与其他用户共享您的项目。", "5": "连接到现有的Supabase项目：这个强大的新功能允许您将多个Biela.dev项目连接到同一个Supabase数据库。"}}, "v1.0.5": {"date": "2025年5月15日", "changes": {"0": "支付：已实现Stripe支付处理。", "1": "内容工作室：当第一页没有图像时，现在显示动画（如机器人）。"}}, "v1.0.4": {"date": "2025年5月13日", "changes": {"0": "内容工作室：修复了内容工作室中的分页问题。"}}, "v1.0.3": {"date": "2025年5月5日", "changes": {"0": "推出内容工作室 - 存储和管理所有媒体的地方。", "1": "前台办公室：在biela_frontoffice上为软件应用实现了schema.org。", "2": "团队仪表板：已添加团队仪表板通知（例如，当新成员加入或聊天中出现新消息时出现红点）。", "3": "IDE：修复了当在给出提示时点击WebContainer中的某些区域时菜单模态不能正确关闭的错误。"}}, "v1.0.2": {"date": "2025年5月2日", "changes": {"0": "联盟：实现了团队成员区域的重新设计。", "1": "联盟：联盟仪表板图标已更新。", "2": "IDE：\"Supabase连接\"按钮文本已更改为带有新图标的\"数据库\"，现在在连接时以蓝色显示\"数据库已连接\"。"}}, "v1.0.1": {"date": "2025年5月1日", "changes": {"0": "IDE（设置选项卡）：在设置聊天中实现了数据库集成。", "1": "仪表板：修复了用户仪表板中的分页。"}}, "v1.0.0": {"date": "2025年4月18日", "changes": {"0": "🚀 欢迎来到Biela.dev：您的AI驱动编码伴侣，对所有人免费！🚀", "1": "我们很兴奋地宣布Biela.dev的初始公开发布！我们相信让尖端的AI辅助Web开发对所有人都可访问，这就是为什么我们的平台从第一天开始就完全免费。"}}}}}