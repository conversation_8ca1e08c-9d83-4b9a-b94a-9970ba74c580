{"meta": {"home": {"title": "biela.dev | एआई-संचालित वेब और ऐप बिल्डर – प्रॉम्प्ट के साथ बनाएँ", "description": "अपने विचारों को biela.dev के साथ लाइव वेबसाइट या ऐप में बदलें। कस्टम डिजिटल प्रोडक्ट्स को आसानी से बनाने के लिए एआई-संचालित प्रॉम्प्ट्स का उपयोग करें।"}, "privacy": {"title": "biela.dev गोपनीयता नीति", "description": "जानें कि biela.dev आपकी व्यक्तिगत जानकारी को कैसे एकत्रित करता है, उपयोग करता है और उसकी सुरक्षा करता है।"}, "terms": {"title": "biela.dev सेवा की शर्तें", "description": "biela.dev के एआई-संचालित विकास प्लेटफ़ॉर्म के उपयोग के नियमों और शर्तों की समीक्षा करें।"}, "changelog": {"title": "biela.dev चेंजलॉग - हमेशा। विकास। आगे।", "description": "जानें कि biela.dev कैसे प्रत्येक रिलीज़ के साथ विकास करता है"}, "competitionterms": {"title": "<PERSON><PERSON>a विकास प्रतियोगिता की शर्तें और नियम", "description": "Biela.dev और TeachMeCode Institute द्वारा आयोजित एक आधिकारिक एक महीने की चुनौती—Biela Development प्रतियोगिता में भाग लें। अपने डिजिटल निर्माण को प्रदर्शित करें, समुदाय की भागीदारी के माध्यम से पहचान अर्जित करें, और सर्वोच्च सम्मान के लिए प्रतिस्पर्धा करें। प्रविष्टियाँ 14 मई 2025 तक जमा करनी होंगी।"}, "contest": {"title": "biela.dev विकास प्रतियोगिता 2025 – $10,000 जीतें", "description": "अपनी AI-निर्मित परियोजनाओं को प्रदर्शित करने और नकद पुरस्कार जीतने के लिए biela.dev प्रतियोगिता में भाग लें"}, "howTo": {"title": "Biel<PERSON>.dev का उपयोग कैसे करें – एआई-संचालित ऐप्स बनाने के लिए संपूर्ण मार्गदर्शिका", "description": "Biela.dev के साथ तेज़ी से और बिना कोड के वेब ऐप्स कैसे बनाएं, यह जानें। ट्यूटोरियल और व्यावहारिक सुझावों के साथ चरण-दर-चरण मार्गदर्शिका।", "keywords": "biela, biela.dev, biela कैसे उपयोग करें, एआई ऐप्स बनाएं, नो-कोड, तेज़ विकास, biela मार्गदर्शिका, biela ट्यूटोरियल", "ogTitle": "B<PERSON><PERSON>.dev का उपयोग कैसे करें – चरण-दर-चरण मार्गदर्शिका", "ogDescription": "Biela.dev के साथ एआई ऐप्स बनाना सीखें। विचार से लॉन्च तक, कोडिंग की आवश्यकता नहीं।", "twitterTitle": "B<PERSON>a.dev का उपयोग कैसे करें – संपूर्ण मार्गदर्शिका", "twitterDescription": "Biela.dev पर बिना कोडिंग के एआई ऐप्स बनाएं। इस आसान चरण-दर-चरण मार्गदर्शिका का पालन करें!"}}, "navbar": {"home": "मुखपृष्ठ", "demo": "डेमो", "community": "समुदाय", "affiliate": "एफ़िलिएट", "partners": "साझेदार", "contest": "VC हैकाथॉन", "pricing": "मूल्य निर्धारण"}, "counter": {"days": "दिन", "hours": "घंटे", "minutes": "मिनट", "seconds": "सेकंड", "centisec": "सेंटीसेक"}, "hero": {"title": "अपने विचार को कुछ ही मिनटों में एक लाइव वेबसाइट या ऐप में बदलें", "subtitle": "यदि आप <1>कल्पना</1> कर सकते हैं, तो आप उसे <5>कोड</5> कर सकते हैं।", "subtitle2": "मुफ्त में शुरू करें। कुछ भी कोड करें। हर प्रॉम्प्ट के साथ अपने कौशल को अवसर में बदलें।", "timeRunningOut": "समय तेजी से समाप्त हो रहा है!", "launchDate": "संस्करण WoodSnake", "experimentalVersion": "बीटा लॉन्च: 15 अप्रैल 2025", "earlyAdopter": "आधिकारिक लॉन्च से पहले शुरुआती लाभ प्राप्त करने के लिए अभी जुड़ें", "tryFree": "मुफ़्त में आज़माएँ", "seeAiAction": "AI को कार्य में देखें", "tellBiela": "<PERSON><PERSON><PERSON> को बताएं क्या बनाना है", "inputPlaceholder": "यदि आप कल्पना कर सकते हैं, तो BIELA उसे कोड कर सकता है। आज क्या करें?", "chat": "चैट", "code": "कोड", "checklist": "चेकलिस्ट", "checklistTitle": "चेकलिस्ट का उपयोग करें", "checklistItems": {"trackDevelopment": "डेवलपमेंट कार्यों को ट्रैक करें", "setRequirements": "परियोजना आवश्यकताओं को निर्धारित करें", "createTesting": "टेस्टिंग प्रोटोकॉल बनाएं"}, "registerNow": "अभी रजिस्टर करें", "attachFiles": "अपने प्रॉम्प्ट में फाइलें जोड़ें", "voiceInput": "वॉइस इनपुट का उपयोग करें", "enhancePrompt": "प्रॉम्प्ट को सुधारें", "cleanupProject": "प्रोजेक्ट क्लीनअप करें", "suggestions": {"weatherDashboard": "एक मौसम डैशबोर्ड बनाएं", "ecommercePlatform": "ई-कॉमर्स प्लेटफ़ॉर्म बनाएँ", "socialMediaApp": "सोशल मीडिया ऐप डिज़ाइन करें", "portfolioWebsite": "पोर्टफोलियो वेबसाइट जनरेट करें", "taskManagementApp": "टास्क मैनेजमेंट ऐप बनाएं", "fitnessTracker": "फिटनेस ट्रैकर बनाएं", "recipeSharingPlatform": "रेसिपी शेयरिंग प्लेटफ़ॉर्म डिज़ाइन करें", "travelBookingSite": "ट्रैवल बुकिंग साइट बनाएं", "learningPlatform": "लर्निंग प्लेटफ़ॉर्म बनाएँ", "musicStreamingApp": "म्यूजिक स्ट्रीमिंग ऐप डिज़ाइन करें", "realEstateListing": "रियल एस्टेट लिस्टिंग बनाएं", "jobBoard": "जॉब बोर्ड बनाएँ"}}, "videoGallery": {"barber": {"title": "बार्बर शॉप वेबसाइट", "description": "देखें कि कैसे Biela.dev कुछ ही मिनटों में एक पूरी बार्बर शॉप वेबसाइट बनाता है"}, "tictactoe": {"title": "टिक टैक टो गेम", "description": "देखें कि एआई कितनी तेजी से टिक टैक टो बनाता है"}, "coffeeShop": {"title": "कॉफ़ी शॉप वेबसाइट", "description": "देखें कि कैसे Biela.dev एक कॉफ़ी शॉप बनाता है"}, "electronicsStore": {"title": "इलेक्ट्रॉनिक्स स्टोर वेबसाइट", "description": "देखें कि कैसे Biela.dev कुछ ही मिनटों में एक पूरी ऑनलाइन स्टोर बनाता है"}, "seoAgency": {"title": "एसईओ एजेंसी वेबसाइट", "description": "देखें कि कैसे एआई एक एसईओ एजेंसी वेबसाइट बनाता है"}}, "telegram": {"title": "हमारे टेलीग्राम से जुड़ें", "subtitle": "<PERSON><PERSON><PERSON> समुदाय से जुड़ें", "description": "तत्काल सहायता प्राप्त करें, अपने प्रोजेक्ट साझा करें, और दुनिया भर के एआई उत्साही लोगों से जुड़ें। <br/><br/> हमारी टेलीग्राम कम्युनिटी Biela.dev से अपडेट रहने का सबसे तेज़ तरीका है।", "stats": {"members": "सदस्य", "support": "सहायता", "countries": "देश", "projects": "प्रोजेक्ट्स"}, "joinButton": "हमारे टेलीग्राम समुदाय से जुड़ें", "communityTitle": "Biela.dev समुदाय", "membersCount": "सदस्य", "onlineCount": "अभी ऑनलाइन", "messages": {"1": "मैंने अभी-अभी अपना ई-कॉमर्स साइट Biela.dev से एक घंटे के अंदर लॉन्च किया!", "2": "यह शानदार लग रहा है! आपने प्रोडक्ट कैटलॉग कैसे मैनेज किया?", "3": "B<PERSON>a ने खुद-ब-खुद किया! मैंने सिर्फ बताया कि मुझे क्या चाहिए।", "4": "मैं अभी एक पोर्टफोलियो साइट बना रहा हूँ। AI के सुझाव अद्भुत हैं!", "5": "क्या किसी ने नए रिस्पॉन्सिव टेम्पलेट्स ट्राई किए हैं? मोबाइल पर शानदार लगते हैं।"}}, "joinNetwork": {"title": "हमारे नेटवर्क से जुड़ें", "subtitle": "जुड़ें। बढ़ें। कमाएँ।", "affiliateProgram": "एफ़िलिएट प्रोग्राम", "registerBring": "रजिस्टर करें और 3 एफ़िलिएट जोड़ें", "aiCourse": "एआई इंजीनियरिंग कोर्स", "courseDescription": "TeachMeCode के साथ फ्री कंप्रीहेंसिव एआई कोर्स", "digitalBook": "डिजिटल बुक", "bookDescription": "\"द आर्ट ऑफ़ प्रॉम्प्ट इंजीनियरिंग\" की एक्सक्लूसिव डिजिटल एडिशन", "exclusiveBenefits": "विशेष लाभ", "benefitsDescription": "आजीवन कमाई और विशेष सदस्य विशेषाधिकार", "registerNow": "अभी रजिस्टर करें", "teamEarnings": "टीम कमाई", "buildNetwork": "अपना नेटवर्क बनाएं", "weeklyMentorship": "साप्ता<PERSON><PERSON>क मार्गदर्शन", "teamBonuses": "टीम बोनस", "startEarning": "आज ही कमाना शुरू करें", "upToCommission": "कमीशन तक"}, "partners": {"title": "उद्योग के नेताओं द्वारा विश्वसनीय", "subtitle": "वैश्विक नवप्रवर्तकों के साथ मिलकर एआई विकास का भविष्य तैयार कर रहे हैं", "strategicPartnerships": "रणनीतिक साझेदारियाँ", "joinNetwork": "साझेदारों और कंपनियों के बढ़ते नेटवर्क से जुड़ें जो BIELA के साथ विकास को रूपांतरित कर रहे हैं"}, "demo": {"title": "AI को रीयल-टाइम में ऐप्स और वेबसाइट बनाते हुए देखें", "subtitle": "देखें कि व्यवसाय कैसे Biela.dev के साथ अपनी डिजिटल उपस्थिति को बदल रहे हैं", "seeInAction": "B<PERSON><PERSON>.dev को कार्य में देखें", "whatKind": "आपको किस प्रकार की वेबसाइट चाहिए?", "describeWebsite": "अपनी वेबसाइट का विचार बताएं (जैसे, 'फोटोग्राफर के लिए पोर्टफोलियो')", "generatePreview": "पूर्वावलोकन उत्पन्न करें", "nextDemo": "अगला डेमो", "startBuilding": "आज ही B<PERSON><PERSON>.dev के साथ निर्माण और कमाई शुरू करें"}, "footer": {"quickLinks": {"title": "त्वरित लिंक", "register": "रजिस्टर करें", "bielaInAction": "B<PERSON><PERSON>.dev को कार्य में देखें", "liveOnTelegram": "हम टेलीग्राम पर लाइव हैं", "affiliate": "सहयोगात्मक एफ़िलिएट मार्केटिंग", "partners": "साझेदार"}, "legal": {"title": "कानूनी", "glassCard": {"copyright": "© 2025 TeachMeCode Institute. सर्वाधिकार सुरक्षित।", "unauthorized": "इस सेवा का बिना अनुमति के उपयोग, पुनरुत्पादन या वितरण—पूरी तरह या आंशिक रूप में—कठोर रूप से निषिद्ध है।"}, "reservations": "TeachMeCode Institute Biela से संबंधित बौद्धिक संपदा पर सभी कानूनी अधिकार सुरक्षित रखता है।", "terms": "सेवा की शर्तें", "privacy": "गोपनीयता नीति"}, "bottomBar": {"left": "2025 Biela.dev. द्वारा संचालित %s © सर्वाधिकार सुरक्षित।", "allRights": "सर्वाधिकार सुरक्षित।", "right": "विकसितकर्ता: %s"}}, "common": {"loading": "लोड हो रहा है...", "error": "एक त्रुटि हुई", "next": "अगला", "previous": "पिछला", "submit": "सबमिट करें", "cancel": "रद्<PERSON> करें", "close": "ब<PERSON><PERSON> करें", "loginNow": "अभी लॉगिन करें", "verifyAccount": "खाता सत्यापित करें"}, "languageSwitcher": {"language": "भाषा", "english": "अंग्रेज़ी", "spanish": "स्पेनिश", "french": "फ़्रेंच", "german": "जर्मन", "russian": "रूसी", "arabic": "अरबी", "azerbaijani": "अज़रबैजानी"}, "contest": {"bielaDevelopment": "वाइब कोडिंग हैकाथॉन", "competition": "प्रतियोगिता", "firstPlace": "प्रथम स्थान", "secondPlace": "द्वितीय स्थान", "thirdPlace": "तृतीय स्थान", "currentLeader": "वर्तमान नेता:", "footerText": "AI-संचालित विकास Biela.dev द्वारा, TeachMeCode® Institute द्वारा संचालित", "browseHackathonSubmissions": "हैकाथॉन प्रस्तुतियाँ ब्राउज़ करें", "winnersAnnouncement": "🎉 प्रतियोगिता विजेताओं की घोषणा! 🎉", "congratulationsMessage": "हमारे अद्भुत विजेताओं को बधाई! इस अविश्वसनीय हैकाथॉन में भाग लेने वाले सभी का धन्यवाद।", "conditionActiveReferrals": "3 सक्रिय रेफरल हैं", "conditionLikedProject": "एक अन्य प्रोजेक्ट को पसंद किया", "winner": "विजेता", "qualificationsMet": "योग्यताएं:", "noQualifiedParticipant": "कोई योग्य प्रतिभागी नहीं", "requirementsNotMet": "आवश्यकताएं पूरी नहीं हुईं", "noQualificationDescription": "इस स्थिति के लिए किसी भी प्रतिभागी ने योग्यता आवश्यकताओं को पूरा नहीं किया।", "qualifiedWinners": "🏆 योग्य विजेता", "qualifiedWinnersMessage": "उन प्रतिभागियों को बधाई जिन्होंने सभी योग्यता आवश्यकताओं को पूरा किया!", "noTopPrizesQualified": "शीर्ष पुरस्कारों के लिए कोई प्रतिभागी योग्य नहीं", "noTopPrizesMessage": "दुर्भाग्य से, किसी भी प्रतिभागी ने पहली तीन पुरस्कार स्थितियों के लिए योग्यता आवश्यकताओं को पूरा नहीं किया। सभी योग्य प्रतिभागियों को शेष पुरस्कार पूल से पुरस्कृत किया जाएगा।", "noTopPrizesMessageShort": "दुर्भाग्य से, किसी भी प्रतिभागी ने पहली तीन पुरस्कार स्थितियों के लिए योग्यता आवश्यकताओं को पूरा नहीं किया।"}, "competition": {"header": {"mainTitle": "आपका प्रॉम्प्ट। आपकी शैली। आपका मैदान।", "timelineTitle": "समयरेखा", "timelineDesc": "अपने सर्वश्रेष्ठ AI-निर्मित प्रोजेक्ट को बनाने के लिए एक महीना", "eligibilityTitle": "पात्रता", "eligibilityDesc": "सक्रिय Biela.dev खाते जिन्होंने प्रोजेक्ट सबमिट किया है", "prizesTitle": "पुरस्कार", "prizesDesc": "$16,000 नकद पुरस्कार + विजेताओं को निःशुल्क एक्सेस", "votingTitle": "मत<PERSON><PERSON>न", "votingDesc": "समुदाय-आधारित, प्रत्येक सत्यापित उपयोगकर्ता को एक वोट", "tagline": "अपनी रचनात्मकता को बोलने दें। दुनिया को मतदान करने दें।"}, "details": {"howToEnter": "भाग कैसे लें", "enter": {"step1": {"title": "Biela.dev AI का उपयोग करके प्रोजेक्ट बनाएं", "desc": "अपने नवाचारपूर्ण प्रोजेक्ट को बनाने के लिए Biela के शक्तिशाली एआई टूल्स का उपयोग करें"}, "step2": {"title": "अपने यूज़र डैशबोर्ड पर जाएँ", "desc": "\"हैकाथॉन के लिए प्रकाशित करें\" पर क्लिक करें", "subdesc": "आप अधिकतम 3 प्रोजेक्ट सबमिट कर सकते हैं"}, "step3": {"title": "<PERSON><PERSON>a आपकी प्रविष्टि को प्रोसेस करेगा:", "list1": "आपके प्रोजेक्ट का स्क्रीनशॉट लेगा", "list2": "आपके प्रॉम्प्ट की पात्रता जाँचेगा", "list3": "सारांश और विवरण उत्पन्न करेगा"}}, "prizeStructure": "पुरस्कार संरचना", "prizes": {"firstPlace": "प्रथम स्थान", "firstPlaceValue": "$10,000", "secondPlace": "द्वितीय स्थान", "secondPlaceValue": "$5,000", "thirdPlace": "तृतीय स्थान", "thirdPlaceValue": "$1,000", "fourthToTenth": "चौथे–दसवें स्थान", "fourthToTenthValue": "30 दिन की असीमित पहुँच"}}, "qualification": {"heading": "पात्रता आवश्यकताएँ", "description": "शीर्ष 3 पुरस्कारों ($10,000, $5,000, और $1,000) के लिए पात्र होने हेतु आपको निम्नलिखित शर्तें पूरी करनी होंगी:", "criteria1": "कम से कम 3 सक्रिय रेफ़रल होने चाहिए", "criteria2": "प्रदर्शनी दीवार से 1 प्रोजेक्ट को पसंद करें", "proTipLabel": "प्रो टिप:", "proTipDesc": "जितना जल्दी आप अपना प्रोजेक्ट सबमिट करेंगे, उतनी ही अधिक दृश्यता और वोट आपको मिलेंगे!"}, "voting": {"heading": "मतदान दिशानिर्देश", "oneVotePolicyTitle": "एक वोट नीति", "oneVotePolicyDesc": "आप केवल एक बार वोट कर सकते हैं, और अपने ही प्रोजेक्ट के लिए नहीं", "accountVerificationTitle": "खाता सत्यापन", "accountVerificationDesc": "केवल सत्यापित Biela.dev खाते वाले उपयोगकर्ता ही वोट कर सकते हैं", "tieBreakingTitle": "टाई-ब्रेकिंग", "tieBreakingDesc": "यदि वोट बराबर हो जाते हैं, तो निर्णायक कारक होगा परियोजना के निर्माता द्वारा अर्जित कुल सितारों की संख्या (चाहे उपयोग किए गए हों या नहीं)", "howToVoteTitle": "कैसे वोट करें", "howToVoteDesc": "शोकेस वॉल ब्राउज़ करें, सभी प्रकाशित AI-निर्मित प्रोजेक्ट्स देखें और अपने पसंदीदा को वोट देने के लिए हार्ट आइकन पर क्लिक करें!"}}, "contestItems": {"projectShowcase": "प्रोजेक्ट शोकेस", "loading": "लोड हो रहा है...", "createBy": "द्वारा बनाया गया ", "viewProject": "प्रोजेक्ट देखें", "stars": "सितारे", "overview": "अवलोकन", "keyFeatures": "मुख्य विशेषताएँ", "exploreLiveProject": "लाइव प्रोजेक्ट देखें", "by": "द्वारा", "likeLimitReached": "पसंद करने की सीमा पूरी हो गई है", "youCanLikeMaximumOneProject": "आप अधिकतम एक प्रोजेक्ट को पसंद कर सकते हैं। क्या आप वर्तमान को अनलाइक कर इसको पसंद करना चाहेंगे?", "currentlyLikedProject": "वर्तमान में पसंद किया गया प्रोजेक्ट", "switchMyLike": "मेरी पसंद बदलें", "mustLoggedIntoLikeProject": "प्रोजेक्ट को पसंद करने के लिए आपको लॉगिन करना होगा।", "signInToBielaAccountToVote": "कृपया वोटिंग में भाग लेने के लिए अपने Biela.dev खाते में साइन इन करें।", "yourAccountIsNotVerifiedYet": "आपका खाता अभी तक सत्यापित नहीं है। कृपया प्रोजेक्ट को पसंद करने के लिए सत्यापन करें।", "accountVerificationEnsureFairVoting": "खाता सत्यापन निष्पक्ष मतदान सुनिश्चित करता है और प्रतियोगिता की विश्वसनीयता बनाए रखता है।", "projectsSubmitted": "जमा किए गए प्रोजेक्ट:", "unlikeThisProject": "इस प्रोजेक्ट को अनलाइक करें", "likeThisProject": "इस प्रोजेक्ट को पसंद करें", "likes": "पसंद", "like": "पसंद करें", "somethingWentWrong": "कुछ गलत हो गया। कृपया बाद में पुनः प्रयास करें", "voteError": "वोट त्रुटि"}, "textsLiveYoutube": {"first": {"title": "आप आमंत्रित हैं!", "description": "हम इस शुक्रवार लाइव हो रहे हैं—और यह सेशन <green>उद्देश्य</green>, <blue>सटीकता</blue>, और <orange>शुद्ध वाइब</orange> के साथ निर्माण पर आधारित है।", "secondDescription": "<green>वाइब कोडिंग में महारत</green> हासिल करें, <blue>नए फ़ीचर्स</blue> सबसे पहले जानें, और <orange>वास्तविक प्रोजेक्ट्स</orange> को लाइव बनते देखें।", "specialText": "यहाँ क्लिक करें और YouTube पर \"Notify Me\" दबाएँ — इसे मिस न करें।"}, "second": {"title": "पहले बनाएं।", "description": "इस शुक्रवार, हम <green>वाइब कोडिंग</green> को एक नई ऊंचाई पर ले जा रहे हैं — आपको दिखाएंगे कि <blue>इरादे से कैसे बनाएं</blue>, <orange>नए फीचर्स कैसे अनलॉक करें</orange>, और <green>अब तक की सबसे तेज़ लॉन्चिंग</green> कैसे करें।", "secondDescription": "<green>नए निर्माण</green>, <blue>नए विचार</blue>, और <orange>नए अवसर</orange> उभरते हुए।", "specialText": "यहाँ क्लिक करें और YouTube रिमाइंडर सेट करें — अगली क्रिएटर लहर का हिस्सा बनें।"}, "third": {"title": "अपनी जगह सुरक्षित करें।", "description": "हमारा अगला <green>Biela.dev लाइवस्ट्रीम</green> इस शुक्रवार हो रहा है — और आप <blue>आधिकारिक रूप से आमंत्रित</blue> हैं।", "secondDescription": "हम गहराई से <green>वाइब कोडिंग</green> में उतरेंगे, <blue>शक्तिशाली नए फीचर्स</blue> दिखाएंगे, और <orange>स्मार्ट, तेज़ और बड़ा</orange> निर्माण करने में आपकी मदद करेंगे।", "specialText": "यहाँ क्लिक करें और YouTube पर \"Notify Me\" दबाएँ — आपकी अगली बड़ी आईडिया यहीं से शुरू हो सकती है।"}}, "stream": {"title": "<1>पहले</1> <3>बनाएं</3>।", "subtitle": "हमारा शुक्रवार लाइवस्ट्रीम देखें — फीचर्स, प्रोजेक्ट्स, और एक्सक्लूसिव अपडेट्स।", "button": "अपने YouTube रिमाइंडर को सेट करें।"}, "JoinUsLive": "लाइव हमारे साथ जुड़ें", "howToUseBiela": {"subtitle": "<PERSON><PERSON><PERSON> का उपयोग कैसे करें", "title": "मिनटों में समझाया गया", "description": "प्रत्येक फ़ीचर कैसे काम करता है इसकी संक्षिप्त व्याख्या"}, "slides": {"newHistoryFeature": "B<PERSON>a की नई HISTORY सुविधा की खोज करें!", "newHistoryFeatureDescription": "HISTORY टैब आपके प्रोजेक्ट के किसी भी पिछले संस्करण को दोबारा देखने और पुनर्स्थापित करने की अनुमति देकर आपके वाइब कोडिंग प्रक्रिया को सहज बनाए रखता है, ताकि आप नियंत्रण में रह सकें!", "fixPreview": "<PERSON><PERSON><PERSON> पर प्रीव्यू को कैसे ठीक करें!", "fixPreviewDescription": "पूरा गाइड देखें और सुनिश्चित करें कि सब कुछ बिल्कुल वैसा ही दिखे जैसा आपने बनाया था।", "signUp": "<PERSON><PERSON>a पर कैसे साइन अप करें!", "signUpDescription": "biela.dev पर नए हैं? रजिस्टर करने और अपना खाता सत्यापित करने का तरीका जानने के लिए यह त्वरित गाइड देखें!", "buildWebsite": "<PERSON><PERSON><PERSON> के साथ वेबसाइट कैसे बनाएं!", "buildWebsiteDescription": "biela.dev का उपयोग करके शुरुआत से वेबसाइट बनाना और लॉन्च करना सीखें — बिना कोई कोड लिखे।", "downloadAndImport": "<PERSON><PERSON>a पर प्रोजेक्ट और चैट को कैसे डाउनलोड और इंपोर्ट करें!", "downloadAndImportDescription": "इस चरण-दर-चरण मार्गदर्शिका में, आप सीखेंगे कि अपने पूर्ण प्रोजेक्ट्स को कैसे डाउनलोड करें और सेव की गई चैट्स को अपने वर्कस्पेस में कैसे इंपोर्ट करें — ताकि आप जहां से छोड़ा था वहीं से आगे बढ़ सकें।", "shareProject": "अपने Vibe Code BIELA प्रोजेक्ट को कैसे शेयर करें!", "shareProjectDescription": "क्या आप अपने प्रोजेक्ट को प्रो की तरह शेयर करने के लिए तैयार हैं? पूरा गाइड देखें और शुरुआत करें।", "launchProject": "अपने प्रोजेक्ट को अपनी खुद की डोमेन पर कैसे लॉन्च करें!", "launchProjectDescription": "इस वीडियो में, हम आपकी वेबसाइट को लाइव करने के लिए सरल चरणों का प्रदर्शन करते हैं — सेटअप से लेकर अंतिम प्रकाशन तक, आपको बिल्ड से लॉन्च तक आत्मविश्वास से जाने के लिए सब कुछ मिलेगा।", "deployProject": "अपने प्रोजेक्ट को कैसे डिप्लॉय करें और लाइव जाएं!", "deployProjectDescription": "यह त्वरित गाइड आपकी साइट, ऐप या डैशबोर्ड को तेजी और आसानी से डिप्लॉय करने के हर चरण से आपको मार्गदर्शन देता है।"}, "maintenance": {"title": "हम समस्या को हल कर रहे हैं", "description": "Biela.dev वर्तमान में मेंटेनेंस मोड में है। हम समस्या को हल कर रहे हैं", "text": "धन्यवाद आपके इंतजार के लिए।"}, "planCard": {"descriptionBeginner": "उन शुरुआती लोगों के लिए आदर्श जो अपनी पहली परियोजनाओं का निर्माण कर रहे हैं और वेब विकास सीख रहे हैं", "descriptionCreator": "उन रचनाकारों के लिए आदर्श जिन्हें उन्नत परियोजनाओं के लिए अधिक शक्ति और लचीलापन चाहिए", "descriptionProfessional": "उन पेशेवर डेवलपर्स और टीमों के लिए एक संपूर्ण समाधान जो एप्लिकेशन बना रहे हैं", "perMillionTokens": "$ {{price}} / प्रति मिलियन टोकन", "dedicatedCto": "समर्पित CTO - 24 घंटे", "tokens": "टोकन", "perMonth": "प्रति माह", "perYear": "प्रति वर्ष", "freeTokens": "{{ tokens }} निःशुल्क टोकन", "monthly": "मासिक", "yearly": "वार्षिक", "save": "10% बचाएं"}, "faqLabel": "अक्सर पूछे जाने वाले प्रश्न", "faqHeading": "हमारे अक्सर पूछे जाने वाले प्रश्न देखें", "faqSubheading": "आरंभ करें। समझें। अन्वेषण करें।", "whatAreTokensQuestion": "टोकन क्या होते हैं?", "whatAreTokensAnswer1": "टोकन यह दर्शाते हैं कि एआई आपके प्रोजेक्ट को कैसे प्रोसेस करता है। Biela.dev पर, जब आप एआई को प्रॉम्प्ट भेजते हैं, तो टोकन उपयोग में आते हैं। इस प्रक्रिया के दौरान, आपके प्रोजेक्ट की फाइलें मैसेज में शामिल होती हैं ताकि एआई आपका कोड समझ सके। आपका प्रोजेक्ट जितना बड़ा होगा और एआई की प्रतिक्रिया जितनी लंबी होगी, उतने अधिक टोकन की आवश्यकता होगी।", "whatAreTokensAnswer2": "इसके अलावा, जब आप वेबकंटेनर का उपयोग करते हैं, तो प्रत्येक मिनट के लिए आपके खाते से थोड़े टोकन काटे जाते हैं, ताकि विकास पर्यावरण की लागत पूरी की जा सके।", "freePlanTokensQuestion": "मुफ्त प्लान में कितने टोकन मिलते हैं?", "freePlanTokensAnswer": "मुफ्त प्लान वाले प्रत्येक उपयोगकर्ता को प्रतिदिन 200,000 टोकन मिलते हैं, जो हर दिन रिफ्रेश होते हैं।", "outOfTokensQuestion": "अगर मेरे टोकन खत्म हो जाएं तो क्या होगा?", "outOfTokensAnswer": "चिंता न करें! आप मेनू → बिलिंग पर जाकर कभी भी तुरंत अतिरिक्त टोकन खरीद सकते हैं।", "changePlanQuestion": "क्या मैं बाद में अपना प्लान बदल सकता हूँ?", "changePlanAnswer": "हाँ — कभी भी। मेनू → बिलिंग पर जाएँ और अपनी ज़रूरतों के अनुसार प्लान अपग्रेड, डाउनग्रेड या बदलें।", "rolloverQuestion": "क्या अप्रयुक्त टोकन अगले दिन के लिए बचते हैं?", "rolloverAnswer": "वर्तमान में, टोकन अपनी समाप्ति तिथि पर रीसेट हो जाते हैं और अगले दिन के लिए नहीं बचते — इसलिए सुनिश्चित करें कि आप उन्हें हर दिन पूरा उपयोग करें!", "exploreHowToUseBiela": "<PERSON><PERSON><PERSON> का उपयोग कैसे करें देखें", "new": "नया", "joinVibeCoding": "Vibe Coding हैकथॉन में शामिल हों", "whatDoYouWantToBuild": "आप क्या बनाना चाहते हैं?", "imaginePromptCreate": "कल्पना करें। प्रॉम्प्ट दें। निर्माण करें।", "levelOfAmbition": "आपकी महत्वाकांक्षा का स्तर क्या है?", "startGrowScale": "शुरुआत करें। विकास करें। स्केल करें।", "calloutBar": {"start": "सभी योजनाएँ मुफ़्त में शुरू होती हैं", "tokens": "प्रति दिन 200,000 टोकन", "end": "। कोई साइनअप बाधा नहीं। बस निर्माण शुरू करें।"}, "atPriceOf": "की कीमत पर", "startFreeNoCreditCard": "मुफ़्त में शुरू करें - कोई क्रेडिट कार्ड नहीं", "SignIn": "साइन इन करें", "Register": "रजिस्टर करें", "Affiliates": "सहयोगी", "UsernameRequired": "उपयोगकर्ता नाम आवश्यक है", "PasswordRequired": "पासवर्ड आवश्यक है", "MissingUsernamePasswordOrTurnstile": "उपयोगकर्ता नाम, पासवर्ड या Turnstile टोकन गायब है", "LoginSuccess": "लॉगिन सफल!", "LoginFailed": "लॉगिन विफल", "EmailRequired": "ईमेल आवश्यक है", "EmailInvalid": "कृपया एक वैध ईमेल पता दर्ज करें", "CaptchaRequired": "कृपया CAPTCHA पूरा करें", "ResetLinkSent": "रीसेट लिंक आपके ईमेल पर भेजा गया है।", "ResetFailed": "रीसेट लिंक भेजने में विफल", "BackToLogin": "लॉगिन पर वापस जाएं", "EmailPlaceholder": "आपका ईमेल", "SendResetLink": "रीसेट लिंक भेजें", "loading": "लोड हो रहा है", "checkingYourAuthenticity": "आपकी प्रमाणिकता की जांच की जा रही है", "ToUseBielaDev": "Biela.dev का उपयोग करने के लिए आपको किसी मौजूदा खाते में लॉगिन करना होगा या नीचे दिए गए विकल्पों में से किसी एक का उपयोग करके एक खाता बनाना होगा", "Sign in with Google": "Google के साथ साइन इन करें", "Sign in with GitHub": "<PERSON><PERSON><PERSON><PERSON> के साथ साइन इन करें", "Sign in with Email and Password": "ईमेल और पासवर्ड के साथ साइन इन करें", "DontHaveAnAccount": "खाता नहीं है?", "SignUp": "साइन अप करें", "ByUsingBielaDev": "Biela.dev का उपयोग करके आप उपयोग डेटा संग्रहण के लिए सहमति देते हैं।", "EmailOrUsernamePlaceholder": "ईमेल/उपयोगकर्ता नाम", "passwordPlaceholder": "पासवर्ड", "login.loading": "लोड हो रहा है", "LoginInToProfile": "अपने प्रोफ़ाइल में लॉगिन करें", "login.back": "वापस जाएं", "forgotPassword": "पासवर्ड भूल गए?", "PasswordsMismatch": "पासवर्ड मेल नहीं खा रहे हैं।", "PhoneRequired": "फोन नंबर आवश्यक है", "ConfirmPasswordRequired": "कृपया अपना पासवर्ड पुष्टि करें", "AcceptTermsRequired": "आपको सेवा की शर्तें और गोपनीयता नीति स्वीकार करनी होगी", "RegistrationFailed": "पंजीकरण विफल", "EmailConfirmationSent": "ईमेल पुष्टिकरण भेजा गया। कृपया अपना ईमेल पुष्टि करें और फिर लॉगिन करें।", "RegistrationServerError": "पंजीकरण विफल (सर्वर ने अस्वीकार कर दिया)।", "SomethingWentWrong": "कुछ गलत हो गया", "CheckEmailConfirmRegistration": "पंजीकरण की पुष्टि के लिए अपना ईमेल जांचें", "EmailConfirmationSentText": "हमने आपको पुष्टिकरण लिंक के साथ एक ईमेल भेजा है।", "LoginToProfile": "प्रोफ़ाइल में लॉगिन करें", "username": "उपयोगकर्ता नाम", "email": "ईमेल", "PasswordPlaceholder": "पासवर्ड", "ConfirmPasswordPlaceholder": "पासवर्ड की पुष्टि करें", "AcceptTermsPrefix": "मैं सहमत हूँ", "TermsOfService": "सेवा की शर्तें", "AndSeparator": "और", "PrivacyPolicy": "गोपनीयता नीति", "CreateAccount": "खाता बनाएं", "Back": "वापस जाएं", "SignInWithGoogle": "Google के साथ साइन इन करें", "SignInWithGitHub": "<PERSON><PERSON><PERSON><PERSON> के साथ साइन इन करें", "SignInWithEmailAndPassword": "ईमेल और पासवर्ड के साथ साइन इन करें", "translation": {"AIModel": "एआई मॉडल", "UpgradePlan": "एक प्रीमियम योजना", "PremiumBadge": "प्रीमियम", "Active": "सक्रिय", "projectInfo.features": "विशेषताएँ", "Stats": "आँकड़े", "Performance": "प्रदर्शन", "Cost": "लागत", "UpgradeTooltip": "अपग्रेड करके अनलॉक करें ", "UpgradeTooltipSuffix": "पैकेज", "chat": "चैट", "code": "कोड"}, "extendedThinkingTooltip": "उत्तर देने से पहले एआई को गहराई से सोचने की अनुमति दें", "extendedThinkingTooltipDisabled": "संसाधनों को बचाने के लिए गहरी सोच अक्षम करें", "AIModel": "एआई मॉडल", "UpgradePlan": "एक प्रीमियम योजना", "PremiumBadge": "प्रीमियम", "Active": "सक्रिय", "projectInfo.features": "विशेषताएँ", "Stats": "आँकड़े", "Performance": "प्रदर्शन", "Cost": "लागत", "UpgradeTooltip": "अपग्रेड करके अनलॉक करें ", "UpgradeTooltipSuffix": "पैकेज", "HowBielaWorks": "BIELA कैसे काम करता है", "WatchPromptLaunch": "देखें। लिखें। लॉन्च करें।", "SearchVideos": "लाइब्रेरी में खोजें...", "NewReleased": "नया जारी किया गया", "Playing": "चला रहा है", "NoVideosFound": "कोई वीडियो नहीं मिला", "TryAdjustingYourSearchTerms": "अपने खोज शब्दों को समायोजित करने का प्रयास करें", "feedback": {"title": {"issue": "कोई समस्या रिपोर्ट करें", "feature": "कोई फ़ीचर का अनुरोध करें"}, "description": {"issue": "क्या आपको कोई बग मिला या किसी समस्या का सामना हो रहा है? हमें बताएं और हम जल्द से जल्द इसे ठीक करेंगे।", "feature": "क्या आपके पास किसी नए फ़ीचर का सुझाव है? हमारी प्लेटफ़ॉर्म को बेहतर बनाने के लिए हम आपके सुझावों को सुनना चाहेंगे।"}, "success": {"title": "धन्यवाद!", "issue": "आपकी समस्या रिपोर्ट सफलतापूर्वक सबमिट की गई है।", "feature": "आपका फ़ीचर अनुरोध सफलतापूर्वक सबमिट किया गया है।"}, "form": {"fullName": {"label": "पूरा नाम", "placeholder": "अपना पूरा नाम दर्ज करें"}, "email": {"label": "ईमेल पता", "placeholder": "अपना ईमेल पता दर्ज करें"}, "suggestion": {"labelIssue": "समस्या का विवरण", "labelFeature": "फ़ीचर सुझाव", "placeholderIssue": "उस समस्या का वर्णन करें जिसका आप अनुभव कर रहे हैं...", "placeholderFeature": "उस फ़ीचर का वर्णन करें जिसे आप देखना चाहते हैं..."}, "screenshots": {"label": "स्क्रीनशॉट (वैकल्पिक)", "note": "हमें समस्या को बेहतर समझने में मदद करने के लिए स्क्रीनशॉट जोड़ें", "drop": "अपलोड करने के लिए क्लिक करें या ड्रैग और ड्रॉप करें", "hint": "<PERSON><PERSON>, <PERSON><PERSON>, JPEG प्रत्येक अधिकतम 10MB", "count": "{{count}} चित्र चयनित"}}, "buttons": {"cancel": "रद्<PERSON> करें", "submitting": "सबमिट किया जा रहा है...", "submitIssue": "समस्या सबमिट करें", "submitFeature": "अनुरोध सबमिट करें"}, "errors": {"fullNameRequired": "पूरा नाम आवश्यक है", "fullNameNoNumbers": "पूरा नाम संख्याएँ नहीं होनी चाहिए", "emailRequired": "ईमेल पता आवश्यक है", "emailInvalid": "कृपया एक मान्य ईमेल पता दर्ज करें", "suggestionRequired": "कृपया अपनी समस्या या सुझाव का वर्णन करें"}}, "changelog": {"title": "परिवर्तन लॉग", "description": "हमेशा. विकसित. आगे।", "TotalReleases": "कुल रिलीज़", "ActiveUsers": "सक्रिय उपयोगकर्ता", "FeaturesAdded": "जोड़ी गई विशेषताएँ", "BugsFixed": "सुधारे गए बग्स", "shortCallText": "कोई सुझाव है या कोई बग मिला है? हम आपसे सुनना पसंद करेंगे!", "reportIssue": "समस्या की रिपोर्ट करें", "requestFeature": "एक फ़ीचर का अनुरोध करें", "totalReleases": "कुल रिलीज़", "activeUsers": "सक्रिय उपयोगकर्ता", "featuresAdded": "जोड़े गए फीचर्स", "bugsFixed": "ठीक किए गए बग्स", "types": {"feature": "फीचर", "improvement": "सुधार", "bugfix": "बग फिक्स", "ui/ux": "UI/UX", "announcement": "घोषणा", "general": "सामान्य"}, "versions": {"v3.2.3": {"date": "26 जून 2025", "changes": {"0": "उपयोगकर्ता डैशबोर्ड में कुछ UI/UX समस्याओं को ठीक किया गया।", "1": "WebContainer की स्थिरता में सुधार किया गया।", "2": "AI Diff मोड को छोटी फाइलों को बड़ी फाइलों से अलग तरीके से हैंडल करने के लिए सुधारा गया।"}}, "v3.2.2": {"date": "24 जून 2025", "changes": {"0": "बेहतर उपयोगकर्ता अनुभव के लिए फ्रंटऑफिस को पुनर्गठित और पुनः डिज़ाइन किया गया।", "1": "इस शानदार चेंजलॉग पेज को लागू किया गया।", "2": "WebContainer के पुनः कनेक्शन एल्गोरिदम में सुधार किया गया।", "3": "फ़ोल्डर अपलोड के अतिरिक्त ZIP फाइलों के माध्यम से प्रोजेक्ट अपलोड करने का समर्थन जोड़ा गया।", "4": "बेहतर इंडेक्सिंग का लाभ उठाकर सभी API कॉल्स की परफॉर्मेंस में सुधार किया गया।"}}, "v3.2.1": {"date": "24 जून 2025", "changes": {"0": "IDE: उपयोगकर्ता डैशबोर्ड के \"फ़ोल्डर\" सेक्शन को बेहतर अनुभव के लिए पुनः डिज़ाइन किया गया।", "1": "Content Studio: अब आप Content Studio के भीतर इमेज कॉपी कर सकते हैं।"}}, "v3.2.0": {"date": "17 जून 2025", "changes": {"0": "AI Diff मोड (प्रायोगिक): AI अब पूरी फाइल को पुनः जेनरेट करने के बजाय केवल मूल फाइल और अपडेटेड वर्जन के बीच अंतर लिखता है। यह प्रदर्शन को काफी बेहतर बनाता है और टोकन उपयोग को अनुकूलित करता है। आप अपने प्रोजेक्ट के सेटिंग्स → कंट्रोल सेंटर क्षेत्र से इस प्रायोगिक फीचर को सक्षम या अक्षम कर सकते हैं।"}}, "v3.1.6": {"date": "12 जून 2025", "changes": {"0": "IDE: सब्सक्रिप्शन और अतिरिक्त टोकन के लिए कूपन कोड फील्ड जोड़ा गया।"}}, "v3.1.5": {"date": "11 जून 2025", "changes": {"0": "IDE: \"प्रोजेक्ट शेयर करें\" फ्लो में विभिन्न सुधार लागू किए गए।"}}, "v3.1.4": {"date": "10 जून 2025", "changes": {"0": "IDE: पैकेज सब्सक्रिप्शन को दोबारा जांचा और सुधारा गया, क्रिप्टो शामिल करने और Stripe implementation की समस्याओं को हल किया गया।"}}, "v3.1.3": {"date": "9 जून 2025", "changes": {"0": "AI: बेहतर लेटेंसी प्राप्त करने के लिए Gemini के लिए डायरेक्ट API इंटीग्रेशन को लागू और परीक्षण किया गया।"}}, "v3.1.2": {"date": "6 जून 2025", "changes": {"0": "एफिलिएट: नए डैशबोर्ड के इंफॉर्मेशन मॉडल के साथ डिज़ाइन कंसिस्टेंसी के साथ एफिलिएट पेमेंट टास्क लागू किए गए (पेमेंट हिस्ट्री)।", "1": "Content Studio: उपयोगकर्ताओं के लिए यह तय करने का नया विकल्प जोड़ा गया कि वे AI को लिंक के साथ इमेज भेजना चाहते हैं या नहीं। डिफ़ॉल्ट रूप से, यह \"नहीं\" पर सेट है।"}}, "v3.1.1": {"date": "5 जून 2025", "changes": {"0": "Content Studio: Content Studio को प्रोजेक्ट के अनुसार व्यवस्थित करने के लिए अपडेट किए गए।"}}, "v3.1.0": {"date": "3 जून 2025", "changes": {"0": "🚀 मेजर BIELA.dev अपडेट अभी आया है! 🚀", "1": "AI अब आज सुबह से 5 गुना तेज़ कोड लिखता है। हाँ, आपने सही पढ़ा। हमने BIELA की परफॉर्मेंस को सुपरचार्ज किया है ताकि आप आइडिया से काम करने वाले कोड तक तुरंत पहुंच सकें।", "2": "Terminal: अब और भी बेहतर: आज सुबह के revamp पर आधारित, हमने स्मूदर कोडिंग फ्लो के लिए अतिरिक्त UI/UX पॉलिश जोड़ा है।", "3": "<PERSON><PERSON><PERSON> कनेक्शन सुधार: एक साफ, सहज इंटरफ़ेस सेटअप और इंटीग्रेशन को आसान बनाता है।", "4": "एरर हैंडलिंग अपडेट (एरर्स की ऑटो-हैंडलिंग): ऑटोमेटिक एरर रिपोर्टिंग को वापस ले लिया गया है। हम एक नई सेटिंग तैयार कर रहे हैं ताकि आप मैनुअल और AI-असिस्टेड हैंडलिंग के बीच चुन सकें।", "5": "नया कंट्रोल सेंटर (प्रोजेक्ट सेटिंग्स में) और यूनिट टेस्टिंग इंटरफ़ेस: पहला फीचर: यूनिट टेस्टिंग टर्मिनल को टॉगल करना। कई और कंट्रोल आ रहे हैं!", "6": "सुधारा गया डाउनलोड लॉजिक: डाउनलोड अब तब तक इंतज़ार करते हैं जब तक आपका कोड पूरी तरह से पुश नहीं हो जाता — हर बार पूरे और सटीक एक्सपोर्ट्स सुनिश्चित करना।"}}, "v3.0.0": {"date": "3 जून 2025", "changes": {"0": "🚀 BIELA.dev अपडेट - अभी पावर बूस्ट मिला है! 🚀", "1": "नवीनीकृत टर्मिनल UI: एक स्लीकर, क्लीनर डिज़ाइन जो टर्मिनल में काम करना आनंददायक बनाता है।", "2": "वन-क्लिक NPM एक्शन्स: नए एक्शन बटन के साथ तुरंत npm install, npm run build, या npm run dev चलाएं—टाइपिंग की जरूरत नहीं! (यह फीचर टर्मिनल में इंटीग्रेटेड है)", "3": "स्मार्ट एरर हैंडलिंग और एरर कलर क्लासिफिकेशन: प्रीव्यू एरर्स अब ऑटोमेटिकली AI को भेजे जाते हैं ताकि वह आपको तेज़ी से डिबग करने में मदद कर सके। (आप अपने प्रोजेक्ट्स डेवलप करते समय BIELA द्वारा स्मूथ बग फिक्सिंग का अनुभव करेंगे)", "4": "सुधारा गया फाइल नेवीगेशन: चैट में फाइल नेम पर क्लिक करने से अब वह सीधे WebContainer में खुल जाती है। बस टैप करें और एडिट करें!"}}, "v2.1.1": {"date": "30 मई 2025", "changes": {"0": "IDE: WebContainer के लिए एक सेव बटन जोड़ा गया है, जो मैनुअल अपडेट के समय दिखाई देता है और GitHub पर कमिट और पुश को ट्रिगर करता है।", "1": "डैशबोर्ड: यूजर डैशबोर्ड से इंपोर्टेड प्रोजेक्ट्स का नाम बदलते समय आने वाली एरर को ठीक किया गया।", "2": "WebContainer: मेमोरी लीक के लिए एक अर्जेंट फिक्स लागू किया गया।"}}, "v2.1.0": {"date": "28 मई 2025", "changes": {"0": "🚀 BIELA.dev अपडेट — 7 नए सुधार अभी आए हैं! 🚀", "1": "IDE: हमने इंटरफ़ेस से सीधे आपके बिएला प्रोजेक्ट से अपने डोमेन को लिंक करने की संभावना जोड़ी है।", "2": "डिप्लॉयड प्रोजेक्ट प्रीव्यू: अब आप आई आइकन पर एक क्लिक से अपने डिप्लॉयड प्रोजेक्ट्स को प्रीव्यू कर सकते हैं — अंदर क्या है जानने के लिए हर एक को खोलने की जरूरत नहीं।", "3": "डुप्लिकेटेड प्रोजेक्ट्स में कॉस्ट ट्रैकिंग फिक्स: हमने उस समस्या को ठीक किया है जहां डुप्लिकेटेड प्रोजेक्ट्स ओरिजिनल प्रोजेक्ट से कॉस्ट एस्टिमेशन इनहेरिट कर रहे थे। अब वे जीरो से शुरू होते हैं, आपको प्रति प्रोजेक्ट सटीक ट्रैकिंग देते हैं।", "4": "लाइव कॉस्ट एस्टिमेशन (प्रति प्रॉम्प्ट): कॉस्ट एस्टिमेशन अब हर प्रॉम्प्ट के बाद अपडेट होते हैं, दिन में सिर्फ एक बार नहीं। यह आपको रियल-टाइम फीडबैक देता है कि डेव एजेंसी के साथ वही चीज़ डेवलप करने में कितना खर्च होगा — और BIELA के साथ आप कितना बचाते हैं।", "5": "हिस्ट्री टैब और रोलबैक फिक्स: अब आप फिर से सुरक्षित रूप से प्रोजेक्ट हिस्ट्री को रोलबैक कर सकते हैं। हमारे हाल के मेजर अपडेट्स के बाद, इसमें समस्याएं थीं — अब यह बहुत स्मूथ है।", "6": "ऑटोस्क्रॉल सुधार: कोडिंग के दौरान अब कोई फ्रोज़न स्क्रीन नहीं! हमने उस समस्या को ठीक किया है जहां जेनरेशन के दौरान स्क्रॉलिंग पीछे रह जाती थी। सीमलेस फ्लो का आनंद लें।", "7": "मेन्यू पेजेस अब नए टैब्स में खुलते हैं: अगर आप Labs में कोडिंग कर रहे हैं और दूसरे मेन्यू आइटम्स पर क्लिक करते हैं, तो वे अब नए टैब में खुलते हैं ताकि आप कभी भी अपना चालू काम न खोएं।"}}, "v2.0.4": {"date": "27 मई 2025", "changes": {"0": "डैशबोर्ड: यूजर डैशबोर्ड पर स्क्रीनशॉट सुधार में ऑटो-स्क्रॉल हटाना, यूजर-कंट्रोल्ड स्क्रॉलिंग की अनुमति देना, और स्क्रीनशॉट लोडिंग के लिए एनीमेशन जोड़ना शामिल है।"}}, "v2.0.3": {"date": "20 मई 2025", "changes": {"0": "Content Studio: Content Studio में रोबोट एनीमेशन अब फुल साइज़ में डिस्प्ले होता है।", "1": "एफिलिएट: Firefox में देखने पर एफिलिएट डैशबोर्ड में डिस्प्ले इश्यूज़ को ठीक किया गया।"}}, "v2.0.2": {"date": "19 मई 2025", "changes": {"0": "Content Studio: Content Studio में एक साथ अनुमतित फाइलों की अधिकतम संख्या 50 तक बढ़ाई गई (10 से)।"}}, "v2.0.1": {"date": "18 मई 2025", "changes": {"0": "Content Studio: एक समस्या को ठीक किया गया जहां मल्टिपल फाइलों के टैग्स तब तक अपडेट नहीं हो रहे थे जब तक पेज रिफ्रेश न हो।", "1": "Content Studio: इमेज सेलेक्ट करते समय एडिट बटन अब अधिक विज़िबल है।", "2": "Content Studio: अपलोड करते समय, फ़ोल्डर लोकेशन अब ऑटोमेटिकली लास्ट क्रिएटेड या लास्ट सेलेक्टेड फ़ोल्डर को सेलेक्ट करता है।", "3": "Content Studio: इमेजेस को ड्रैग एंड ड्रॉप करते समय सेलेक्टेड फ़ोल्डर अब रिटेन होता है।", "4": "Content Studio: टैब टाइटल अब नया कलर यूज़ करता है (ग्रीन की बजाय)।", "5": "Content Studio: \"इमेज इन्सर्ट\" बटन का नाम बदलकर \"प्रोजेक्ट में यूज़ करें\" कर दिया गया।", "6": "Content Studio: अपलोड फाइल्स बटन का अब ग्रीन बैकग्राउंड है।", "7": "Content Studio: बेहतर एलाइनमेंट के लिए सर्च बार की हाइट कम की गई।", "8": "Content Studio: अगर यूजर के लिए कोई इमेज नहीं है तो सर्च बार अब नहीं दिखाया जाता।"}}, "v2.0.0": {"date": "16 मई 2025", "changes": {"0": "🚀 BIELA.DEV में मेजर अपडेट्स! 🚀", "1": "हमारी अपनी वेब कंटेनर टेक्नोलॉजी: हमने अपनी स्वयं की वेब कंटेनर टेक्नोलॉजी बनाई है जो किसी भी बाहरी प्रोवाइडर से पूरी तरह स्वतंत्र है! यह हमें पहले से कहीं ज्यादा तेज़ और बेहतर नए फीचर्स डेवलप करने के लिए अभूतपूर्व लचीलापन देता है।", "2": "कोड जेनरेशन के लिए मल्टिपल LLM ऑप्शन्स: अब हम Anthropic के अलावा कोड जेनरेशन के लिए मल्टिपल AI मॉडल्स को सपोर्ट करते हैं! अब आप अपने कोडिंग प्रोजेक्ट्स के लिए Google का Gemini यूज़ कर सकते हैं, जो कुछ मेजर एडवांटेजेस लेकर आता है। Gemini 1,000,000 टोकन का मैसिव कॉन्टेक्स्ट विंडो ऑफर करता है!", "3": "Content Studio: आपकी फोटोज़, आपके प्रोजेक्ट्स: हमारे नए Content Studio के साथ खुद को पूरी तरह एक्सप्रेस करें! अब आप अपने Vibe Coding प्रोजेक्ट्स में यूज़ करने के लिए अपनी फोटोज़ अपलोड कर सकते हैं।", "4": "प्रोजेक्ट शेयरिंग: कोलैबोरेशन अभी बेहतर हो गया! अब आप दूसरे यूजर्स के साथ अपने प्रोजेक्ट्स शेयर कर सकते हैं।", "5": "मौजूदा Supabase प्रोजेक्ट्स से कनेक्ट करें: यह पावरफुल नया फीचर आपको मल्टिपल Biela.dev प्रोजेक्ट्स को एक ही Supabase डेटाबेस से कनेक्ट करने की अनुमति देता है।"}}, "v1.0.5": {"date": "15 मई 2025", "changes": {"0": "पेमेंट्स: Stripe पेमेंट प्रोसेसिंग को इम्प्लीमेंट किया गया है।", "1": "Content Studio: जब फर्स्ट पेज पर कोई इमेज नहीं होती तो अब एक एनीमेशन (रोबोट की तरह) डिस्प्ले होता है।"}}, "v1.0.4": {"date": "13 मई 2025", "changes": {"0": "Content Studio: Content Studio में पेजिनेशन इश्यूज़ को ठीक किया गया।"}}, "v1.0.3": {"date": "5 मई 2025", "changes": {"0": "Content Studio लॉन्च किया गया - आपके सभी मीडिया को स्टोर और मैनेज करने की जगह।", "1": "फ्रंटऑफिस: biela_frontoffice पर सॉफ्टवेयर ऐप्स के लिए schema.org को इम्प्लीमेंट किया गया।", "2": "टीम डैशबोर्ड: टीम डैशबोर्ड में नोटिफिकेशन्स जोड़े गए हैं (जैसे नया मेंबर जॉइन करने पर या चैट में नया मैसेज आने पर रेड डॉट)।", "3": "IDE: एक बग को ठीक किया गया जहां प्रॉम्प्ट देते समय WebContainer के अंदर कुछ एरियाज़ में क्लिक करने पर मेन्यू से मॉडल सही तरीके से बंद नहीं हो रहा था।"}}, "v1.0.2": {"date": "2 मई 2025", "changes": {"0": "एफिलिएट: टीम मेंबर एरिया का रीडिज़ाइन इम्प्लीमेंट किया गया।", "1": "एफिलिएट: एफिलिएट डैशबोर्ड आइकन्स को अपडेट किया गया।", "2": "IDE: \"Supabase Connect\" बटन टेक्स्ट को नए आइकन के साथ \"डेटाबेस\" में बदल दिया गया है, और अब यह कनेक्ट होने पर नीले रंग में \"डेटाबेस कनेक्टेड\" डिस्प्ले करता है।"}}, "v1.0.1": {"date": "1 मई 2025", "changes": {"0": "IDE (सेटिंग्स टैब): सेटिंग्स चैट पर डेटाबेस इंटीग्रेशन को इम्प्लीमेंट किया गया।", "1": "डैशबोर्ड: यूजर डैशबोर्ड पर पेजिनेशन को ठीक किया गया।"}}, "v1.0.0": {"date": "18 अप्रैल 2025", "changes": {"0": "🚀 Biela.dev में आपका स्वागत है: आपका AI-पावर्ड कोडिंग कंपेनियन, सभी के लिए मुफ्त! 🚀", "1": "हम Biela.dev की शुरुआती सार्वजनिक रिलीज़ की घोषणा करते हुए रोमांचित हैं! हम अत्याधुनिक AI-असिस्टेड वेब डेवलपमेंट को सभी के लिए सुलभ बनाने में विश्वास करते हैं, यही कारण है कि हमारा प्लेटफॉर्म पहले दिन से पूरी तरह मुफ्त है।"}}}}}