@tailwind base;
@tailwind components;
@tailwind utilities;

@theme {
  /* Colors */
  --color-navy-50: #1A1D2E;
  --color-navy-100: #161926;
  --color-navy-200: #13151F;
  --color-navy-300: #0F1119;
  --color-navy-400: #0C0D14;
  --color-navy-500: #0A0B14;
  --color-navy-600: #080912;
  --color-navy-700: #06070F;
  --color-navy-800: #04050A;
  --color-navy-900: #020305;
  --color-accent: #22C55E;
  --color-accent-purple: #7C3AED;
  --color-accent-blue: #3B82F6;
  --color-accent-chatOptions: #4ADE801A;
  --color-inactive: #E5E7EB;

  /* Opacity */
  --opacity-3: 0.03;

  /* Fonts */
  --font-sans: 'Inter', sans-serif;
  --font-manrope: 'Manrope', sans-serif;

  /* Animations */
  --animation-float: floating 6s ease-in-out infinite;
  --animation-pulse-slow: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animation-gradient: gradient 8s linear infinite;
  --animation-glow: glow 2s ease-in-out infinite;
}
@layer base {
  html {
    scroll-behavior: smooth;
  }
  /* todo bg-navy didn`t exist before, make sure to use correct color; */
  body {
    @apply bg-navy-50 text-white antialiased overflow-x-hidden;
    background: #0A1730;
    font-family: 'Manrope', sans-serif;
  }
}

@font-face {
  font-family: 'Rexton Regular';
  src: url('/fonts/Rexton/Rexton Regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'Rexton-Light';
  src: url('/fonts/Rexton/Rexton-Light.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'Manrope';
  src: url('/fonts/Manrope/Manrope-Regular.ttf') format('truetype');
  font-weight: normal;
  font-size: normal;
}


/* Main background with the first image */
main {
  position: relative; /* Needed so the pseudo-element positions over it */
  background-color: #0A1730; /* Transparent background color */
  background-blend-mode: multiply;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-attachment: fixed;
  min-height: 100vh;
}
.embossed-logo {
  /*width: 100%;*/
  position: relative;
}
.base-chat {
  -webkit-backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border-radius: 0.75rem !important;
  border: 1px solid rgba(255, 255, 255, 0.05);
}
.show-prompt:after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  z-index: 1;
  padding: 1px;
  pointer-events: none;
  transition: all 0.5s;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  background: linear-gradient(125deg, transparent 0%, rgba(34, 32, 38,0) 90%, #86F49C 100%);
}
.show-prompt:before {
  content: "";
  padding: 1px;
  background: linear-gradient(300deg, transparent 0%, rgba(34, 32, 38,0) 90%, #6bff88 100%);
  position: absolute;
  inset: 0;
  border-radius: inherit;
  z-index: 1;
  pointer-events: none;
  transition: all 0.5s;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}
.main-input-area {
  background-color: rgb(0 0 0 / 3%);
  border-radius: 0.75rem;
}
.text-biela-elements-textPrimary {
  color: #FFF;
  font-family: 'Manrope', sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 300;
  line-height: 24px;
}
.settings-icons:hover {
  border-radius: 8px;
}
.action-button {
  position: relative;
  transition: all 0.2s ease;
  border-radius: 6px;
  background: transparent;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 6px;
    padding: 1px;
    background: linear-gradient(
            45deg,
            rgba(74, 222, 128, 0) 0%,
            rgba(74, 222, 128, 0.3) 50%,
            rgba(74, 222, 128, 0) 100%
    );
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover::before {
    opacity: 1;
  }

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.user-message p {
  margin: 0;
}
.action-button:hover .tooltip {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}
.tooltip {
  position: absolute;
  bottom: 130%;
  left: 50%;
  transform: translateX(-50%) translateY(4px);
  padding: 6px 8px;
  background: rgba(26, 31, 46, 0.95);
  border: 1px solid rgba(74, 222, 128, 0.1);
  border-radius: 6px;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
.embossed-logo svg {
  /*width: 325px;*/
  height: auto;
  /* Enhanced letterpress effect with multiple layers */
  filter:
    /* Primary inset shadow to create depth */
          drop-shadow(0px -1.5px 0px rgba(0, 0, 0, 0.9))
            /* Secondary inset shadow for more depth */
          drop-shadow(0px -0.5px 0px rgba(0, 0, 0, 0.7))
            /* Subtle highlight at bottom to create dimension */
          drop-shadow(0px 1px 0px rgba(255, 255, 255, 0.05));
}

/* Styling for the SVG paths to enhance letterpress effect */
.embossed-logo .cls-1 {
  /* Further brightened fill color while maintaining the embossed effect */
  fill: #1a3a6c;
  opacity: 1;
}

.biela-name {
  font-family: 'Rexton Regular','sans-serif';
}
.rexton-light {
  font-family: 'Rexton-Light','sans-serif' !important;
}

canvas {
  background-image: url("/hero-1.webp");
  background-color: rgba(0, 0, 0, 0); /* Transparent background color */
  background-blend-mode: multiply;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-attachment: fixed;
  min-height: 100vh;
}

/* Pseudo-element holds the second background image */
/*main::before {*/
/*  content: "";*/
/*  position: absolute;*/
/*  inset: 0; !* Shorthand for top:0; right:0; bottom:0; left:0; *!*/
/*  background-image: url("/hero-2.png");*/
/*  background-blend-mode: multiply;*/
/*  background-position: center;*/
/*  background-repeat: no-repeat;*/
/*  background-size: cover;*/
/*  background-attachment: fixed;*/
/*  opacity: 0; !* Start hidden *!*/
/*  transition: opacity 1s ease-in-out; !* Smooth fade transition *!*/
/*  pointer-events: none; !* Allows clicks to pass through *!*/
/*}*/

/* When the class is added, fade in the second background */
main.change-background::before {
  opacity: 1;
}
.container-textarea {
  position: relative;
  border-radius: 10px;
}
.chat-mode .container-textarea:after {
  background: linear-gradient(0deg, rgb(34, 32, 38) 70%, rgb(128, 0, 128) 80%, rgb(128, 0, 128) 100%) !important;
}
.container-textarea:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  z-index: -1;
  padding: 1px;
  inset: -1px;
  background: linear-gradient(0deg, rgba(34, 32, 38, 1) 70%, rgba(134, 244, 156, 1) 112%, rgba(134, 244, 156, 1) 100%);  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  transition: all 0.5s ease-in-out;
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.glass-card {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  contain: paint;
}
.glass-card:hover {
  border-color: #22C55E;
  background: linear-gradient(
          to bottom right,
          rgba(26, 29, 46, 0.6),
          rgba(26, 29, 46, 0.3)
  );
}
.glass-card.is-active {
  border-color: #22C55E;   /* same green you use on hover */
}

@layer components {
  .nav-link {
    @apply relative text-xl font-light transition-all duration-300 text-white/80 hover:text-white;
  }

  .nav-link::after {
    @apply content-[''] absolute left-0 right-0 -bottom-1 h-[2px];
    background: linear-gradient(90deg, #22C55E, #7C3AED);
    transform: scaleX(0);
    transition: transform 0.3s ease;
    transform-origin: center;
  }

  .nav-link:hover::after,
  .nav-link.active::after {
    transform: scaleX(1);
  }

  .glass-card {
    @apply relative bg-navy-800/20 backdrop-blur-lg border border-white/5 rounded-xl transition-all duration-300;
    background: linear-gradient(
      to bottom right,
      rgba(26, 29, 46, 0.5),
      rgba(26, 29, 46, 0.2)
    );
  }

  .glass-card::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    pointer-events: none;
  }

  .glass-card:hover {
    /*@apply border-accent/20;*/
    background: linear-gradient(
      to bottom right,
      rgba(26, 29, 46, 0.6),
      rgba(26, 29, 46, 0.3)
    );
  }

  .section-wrapper {
    @apply relative overflow-hidden;
  }

  /*.section-wrapper::before {*/
  /*  @apply content-[''] absolute inset-0;*/
  /*  background: radial-gradient(circle at top right, rgba(123, 31, 162, 0.15), transparent 50%),*/
  /*              radial-gradient(circle at bottom left, rgba(25, 118, 210, 0.15), transparent 50%);*/
  /*}*/

  .section-content {
    @apply relative z-10 container mx-auto px-4;
  }

  .accent-button {
    @apply px-6 py-3 bg-accent text-white rounded-lg
           hover:bg-accent/90 transition-all font-light
           inline-flex items-center gap-2 relative overflow-hidden;
  }

  .accent-button::before {
    @apply content-[''] absolute inset-0 opacity-0 transition-opacity duration-300;
    background: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    );
  }

  .accent-button:hover::before {
    @apply opacity-100;
  }

  .input-field {
    @apply w-full bg-black/20 rounded-lg px-4 py-3 text-white placeholder-gray-500
           focus:outline-none focus:ring-2 focus:ring-accent transition-all font-light;
  }

  .section-title {
    @apply text-4xl font-light mb-4;
    background: linear-gradient(to right, #fff, #ccc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .section-subtitle {
    @apply text-xl text-gray-400 font-light max-w-2xl mx-auto;
  }

  .gradient-border {
    @apply relative rounded-xl overflow-hidden;
  }

  .gradient-border::before {
    @apply content-[''] absolute inset-0;
    padding: 1px;
    background: linear-gradient(
      to right,
      rgba(34, 197, 94, 0.5),
      rgba(124, 58, 237, 0.5)
    );
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
  }
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
body {
  overflow: visible !important;
}
.blur-margins:after {
  content: "";
  width: 50px;
  height: 42px;
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(to right, rgba(10, 23, 48, 0) 0%, rgba(10, 23, 48, 1) 100%);
  z-index: 60;
}
.blur-margins:before {
  content: "";
  width: 50px;
  height: 42px;
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(to right, rgba(10, 23, 48, 1) 0%, rgba(10, 23, 48, 0) 100%);
  z-index: 60;
}

@keyframes gradient {
  0% { background-position: 0% center; }
  100% { background-position: 200% center; }
}

@keyframes floating {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20px); }
}

.animate-float {
  animation: floating 6s ease-in-out infinite;
}

.glow {
  position: relative;
}

.glow::after {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(90deg, #22C55E, #7C3AED);
  filter: blur(15px);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glow:hover::after {
  opacity: 0.5;
}

.animated-bg {
  position: relative;
  overflow: hidden;
}

.animated-bg::before {
  content: '';
  position: absolute;
  inset: -100%;
  background: radial-gradient(
    circle at center,
    rgba(124, 58, 237, 0.1) 0%,
    transparent 70%
  );
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.pulse-glow {
  position: relative;
}

.pulse-glow::after {
  content: '';
  position: absolute;
  inset: -20px;
  background: radial-gradient(
    circle at center,
    rgba(34, 197, 94, 0.2),
    transparent 70%
  );
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 60s linear infinite; /* Changed from 30s to 60s for slower scrolling */
}

.animate-scroll:hover {
  animation-play-state: paused;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.animate-blink {
  animation: blink 0.8s infinite;
}

/* Animation for form validation shake effect */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.animate-shake {
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}


/*Contest*/
.project-wall {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.project-wall::-webkit-scrollbar {
  width: 4px;
}

.project-wall::-webkit-scrollbar-track {
  background: transparent;
}

.project-wall::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
}

.blurred-card {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.1);
}

/* Animation keyframes */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes float-prominent {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 15px 0 rgba(68, 255, 161, 0.3);
  }
  50% {
    opacity: 0.7;
    box-shadow: 0 0 25px 0 rgba(68, 255, 161, 0.5);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping-slow {
  75%, 100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes scroll-down {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-100%);
  }
}

/* Apply animations */
.animate-float {
  animation: float 4s ease-in-out infinite;
}

.animate-float-prominent {
  animation: float-prominent 5s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 15s linear infinite;
}

.animate-ping-slow {
  animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.background-shimmer {
  animation: shimmer 5s infinite;
}

.animate-scroll-down {
  animation: scroll-down 5s linear infinite;
}

/* Trophy styling */
.trophy-container {
  perspective: 1000px;
}

.trophy {
  transition: transform 0.5s;
}

.trophy:hover {
  transform: rotateY(10deg) rotateX(5deg);
}

.blue-trophy {
  filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.6));
}

.green-trophy {
  filter: drop-shadow(0 0 15px rgba(16, 185, 129, 0.7));
}

.purple-trophy {
  filter: drop-shadow(0 0 10px rgba(139, 92, 246, 0.6));
}

.trophy-badge {
  box-shadow: 0 0 15px rgba(68, 255, 161, 0.3);
}

.trophy-badge-large {
  box-shadow: 0 0 20px rgba(68, 255, 161, 0.4);
}

body > * ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05) !important;
}

body > * ::-webkit-scrollbar-thumb {
  scrollbar-width: thin;
  width: 2px !important;
  background: linear-gradient(#00000000,#00000000,#4ade80,#4ade80,#4ade80,#00000000,#00000000) !important;
  min-height: 150px;
  cursor: grab;
  transition: all 0.2s ease-in-out;
}


body > * ::-webkit-scrollbar-thumb:active {
  cursor: grabbing;

}


/* Doar Firefox */
@-moz-document url-prefix() {
  .vertical-scroll {
    scrollbar-width: thin;
  }
}

/* Chrome, Edge, Safari */
.vertical-scroll::-webkit-scrollbar {
  width: 1px !important;
}

.vertical-scroll::-webkit-scrollbar-thumb {
  width: 1px !important;
  background: linear-gradient(
          rgba(0, 0, 0, 0),
          rgba(0, 0, 0, 0),
          #4ade80,
          #4ade80,
          #4ade80,
          rgba(0, 0, 0, 0),
          rgba(0, 0, 0, 0)
  ) !important;
}

@supports not selector(::-webkit-scrollbar) {
  body > * {
    scrollbar-color: #4ade80 transparent !important;
  }
}
