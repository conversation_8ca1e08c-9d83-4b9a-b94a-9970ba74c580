import React, { useEffect, useState } from "react";
import { ProjectDetail } from "../components/contest/ProjectDetail";
import { Winners } from "../components/contest/Winners";
import CountdownTimer from "../components/sections/hero/CountdownTimer.jsx";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useTranslation } from "react-i18next";
import { ShortEvents } from "../components/short-events";

export const ContestPage = () => {
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [isMobile, setIsMobile] = useState(false);
  const [showProjectWall, setShowProjectWall] = useState(true);
  const [user, setUser] = useState(null);
  const { t, i18n } = useTranslation();
  const [limitReached, setLimitReached] = useState(false);
  const [isNotLogin, setNotIsLogin] = useState(false);
  const [yourOwnPost, setYourOwnPost] = useState(false);
  const [notVerified, setNotVerified] = useState(false);
  const [totalProjects, setTotalProjects] = useState(0);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [winners, setWinners] = useState([]);
  const [contestEnded, setContestEnded] = useState(false);
  const { isUser } = ShortEvents();
  useEffect(() => {
    if (isUser) {
      setUser(isUser);
    }
  }, [isUser]);

  // Check if contest has ended
  useEffect(() => {
    const checkContestStatus = () => {
      const now = new Date().getTime();
      const targetDate = new Date('June 30, 2025 23:59:59 GMT+0100').getTime();
      const hasEnded = now > targetDate;

      // Manual override for testing - set to true to test winners display
      const SHOW_WINNERS_FOR_TESTING = false;

      setContestEnded(hasEnded || SHOW_WINNERS_FOR_TESTING);

      if (hasEnded || SHOW_WINNERS_FOR_TESTING) {
        fetchWinners();
      }
    };

    checkContestStatus();
    // Check every minute
    const interval = setInterval(checkContestStatus, 60000);

    return () => clearInterval(interval);
  }, []);

  const fetchWinners = async () => {
    try {
      // Use the new contest-winners endpoint
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/contest/contest-winners`,
        {
          method: "GET",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result && result.length > 0) {
        // The API already returns sorted winners, take up to 7 for places 4-10
        const formattedWinners = result.slice(0, 7).map((item, index) => ({
          id: item._id,
          title: item.projectName,
          creator: item.fullName,
          creatorId: item.userId,
          description: item.projectSummary,
          tech: item.technologies || [],
          votes: item.likesCount,
          image: item.screenshotUrl,
          projectUrl: item.projectUrl,
          // Use the actual qualification data from the API
          hasActiveReferrals: item.ownerQualification?.referrals >= 3, // Check if they have 3+ referrals
          hasLikedProject: item.ownerQualification?.like || false, // Check if they liked another project
          place: index + 4, // Start from place 4 since 1-3 are unqualified
          isQualified: true,
        }));

        setWinners(formattedWinners);
      }
    } catch (error) {
      console.error("Failed to fetch winners:", error);
      // Set empty winners array on error
      setWinners([]);
    }
  };
  const fetchProjects = async (requestedPage = 1) => {
    try {
      setLoading(true);
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/contest/projects?page=${requestedPage}&limit=10`,
        {
          method: "GET",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
        },
      );
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);

      const result = await response.json();
      setTotalProjects(result.total);
      setTotalPages(result.totalPages);

      const formatted = result.items.map((item) => ({
        id: item.id,
        title: item.projectName,
        creator: item.fullName,
        creatorId: item.userId,
        isLikedByUser: item.isLikedByUser,
        description: item.projectSummary,
        tech: item.technologies,
        votes: item.likesCount,
        imageSmall: item?.screenshoturl2,
        image: item.screenshotUrl,
        fullDescription: item.projectDescription,
        features: item.keyFeatures,
        stars: result.totalUserStars,
        projectUrl: item.projectUrl,
        tokens: item.tokens ?? 0,
        cost: item.cost ?? "$0.00",
      }));

      setProjects((prev) => {
        const existingIds = new Set(prev.map((p) => p.id));
        const filteredNew = formatted.filter((p) => !existingIds.has(p.id));
        return [...prev, ...filteredNew];
      });

      setPage(requestedPage); // update actual page
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch projects:", error);
      setLoading(false);
    }
  };
  const loadMoreProjects = () => {
    if (!loading && page + 1 <= totalPages - 1) {
      fetchProjects(page + 1);
    }
  };
  useEffect(() => {
    fetchProjects(0);
  }, []);

  const actionVote = async (projectId) => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/contest/like`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ contestProjectId: projectId }),
        },
      );

      if (!response.ok) {
        // Încearcă să obții mesajul de eroare din body-ul răspunsului
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (jsonError) {
          // Dacă nu poate fi parsată partea JSON, lasă mesajul inițial
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      if (error.message == "You cannot like your own project") {
        return "own-message";
      } else if (
        error.message ===
        "Please login and verify your account to like projects."
      ) {
        return "not-verified";
      } else if (
        error.message ===
        "You can only like one project at a time. Please unlike your current selection first."
      ) {
        return "one-project-vote";
      }
    }
  };

  // Check if the device is mobile or tablet
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleSelectProject = (project) => {
    setSelectedProject(project);
    // On mobile, switch to project detail view
    if (isMobile) {
      setShowProjectWall(false);
    }
  };

  const handleBackToProjects = () => {
    setShowProjectWall(true);
  };

  const handleVoteProject = (projectId, isVoted) => {
    if (!user) {
      setNotIsLogin(true);
      return;
    }
    actionVote(projectId).then((res) => {
      if (res.id || res._id) {
        setProjects(
          projects.map((project) => {
            if (project.id === projectId) {
              const voted = isVoted;
              return {
                ...project,
                votes: voted ? project.votes - 1 : project.votes + 1,
                isLikedByUser: !voted,
              };
            }
            return project;
          }),
        );

        if (selectedProject && selectedProject.id === projectId) {
          setSelectedProject((prev) => ({
            ...prev,
            votes: prev.isLikedByUser ? prev.votes - 1 : prev.votes + 1,
            isLikedByUser: !prev.isLikedByUser,
          }));
        }
        fetchProjects();
      } else {
        if (res === "own-message") {
          setYourOwnPost(true);
        } else if (res === "not-verified") {
          setNotVerified(true);
        } else if (res === "one-project-vote") {
          setLimitReached(true);
        }
      }
    });
  };

  const unVoteAndVote = (wantToVode, projectId) => {
    actionVote(projectId).then((res) => {
      if (res.id === "") {
        actionVote(wantToVode).then((res) => {
          setProjects(
            projects.map((project) => {
              if (project.id === wantToVode) {
                return {
                  ...project,
                  votes: project.votes + 1,
                  isLikedByUser: true,
                };
              }
              return project;
            }),
          );
          fetchProjects();
          setLimitReached(false);
        });
      } else if (res.id || res._id) {
        actionVote(wantToVode).then((res) => {
          setProjects(
            projects.map((project) => {
              if (project.isLikedByUser) {
                return {
                  ...project,
                  votes: project.votes - 1,
                  isLikedByUser: false,
                };
              }
              if (project.id === wantToVode) {
                return {
                  ...project,
                  votes: project.votes + 1,
                  isLikedByUser: true,
                };
              }
              return project;
            }),
          );
          fetchProjects();
          setLimitReached(false);
        });
      } else {
        if (res === "own-message") {
          setYourOwnPost(true);
        } else if (res === "not-verified") {
          setNotVerified(true);
        } else if (res === "one-project-vote") {
          setLimitReached(true);
        }
      }
    });
  };
  return (
    <main className="min-h-screen pt-28 px-4 flex flex-col lg:flex-row bg-[#0A1730]">
      <div className={`w-full text-center`}>
        <h1 className="rexton-light text-2xl max-sm:text-xl md:text-4xl text-white mb-8 md:mb-12 text-center">
          <span className="relative inline-flex items-center justify-center gap-2 flex-wrap">
            {t("contest.bielaDevelopment", "Vibe Coding Hackathon")}
            <span className="relative inline-block">
              {/*<span className="relative z-10">{t("contest.competition")}</span>*/}
              <span className="absolute -top-2 -right-8 z-20">
                <Sparkles className="w-6 h-6 md:w-8 md:h-8 text-biela-accent animate-pulse" />
              </span>
            </span>
          </span>
        </h1>

        <CountdownTimer />

        {contestEnded && winners.length > 0 ? (
          <Winners winners={winners} />
        ) : (
          <div className="flex flex-col items-center px-4 py-8">
          <div className="flex flex-row justify-center gap-6 md:gap-12 mb-10 md:mb-16 w-full">
            {/* 1st Place Trophy */}
            <div className="flex flex-col items-center transform hover:scale-105 transition-transform duration-300 z-10 order-2">
              <div className="relative mb-3 md:mb-5">
                <div className="w-40 h-50 md:w-64 md:h-80 max-sm:w-20 max-sm:h-40 flex items-center justify-center trophy-container">
                  <div className="trophy green-trophy animate-float-prominent">
                    <svg
                      width="140"
                      height="170"
                      viewBox="0 0 140 160"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="drop-shadow-[0_0_20px_rgba(16,185,129,0.6)] md:w-[220px] md:h-[260px]"
                    >
                      <path
                        d="M35 45C35 40 40 35 70 35C100 35 105 40 105 45V65C105 85 90 105 70 105C50 105 35 85 35 65V45Z"
                        fill="#10B981"
                      />
                      <path
                        d="M55 105V125H85V105"
                        stroke="#10B981"
                        strokeWidth="8"
                      />
                      <path d="M52 125H88" stroke="#10B981" strokeWidth="8" />
                      <rect
                        x="45"
                        y="125"
                        width="50"
                        height="10"
                        rx="2"
                        fill="#0D1A3A"
                      />
                      <rect
                        x="40"
                        y="135"
                        width="60"
                        height="10"
                        rx="2"
                        fill="#0D1A3A"
                      />
                      <circle cx="70" cy="70" r="24" fill="#A7F3D0" />
                      <text
                        x="70"
                        y="75"
                        fontFamily="Arial"
                        fontSize="22"
                        fill="white"
                        textAnchor="middle"
                        fontWeight="bold"
                      >
                        $10,000
                      </text>
                    </svg>
                  </div>
                  <div className="absolute -top-4 -right-4">
                    <Sparkles className="w-6 h-6 md:w-10 md:h-10 text-yellow-300 animate-ping-slow" />
                  </div>
                  <div className="absolute -bottom-4 -left-4">
                    <Sparkles className="w-6 h-6 md:w-10 md:h-10 text-yellow-300 animate-ping-slow" />
                  </div>
                </div>
                <div className="absolute bottom-0 w-full flex justify-center">
                  <div className="bg-biela-blue rounded-full w-12 h-12 md:w-20 md:h-20 flex items-center justify-center border-2 border-biela-dark-blue trophy-badge-large animate-pulse-slow">
                    <Star
                      fill="#10B981"
                      className="w-8 h-8 md:w-12 md:h-12 text-green-400 animate-spin-slow"
                    />
                  </div>
                </div>
              </div>
              <p className="rexton-light text-xl md:text-3xl text-green-400 tracking-wider">
                {t("contest.firstPlace")}
              </p>
            </div>

            {/* 2nd Place Trophy */}
            <div className="flex flex-col items-center transform hover:scale-105 transition-transform duration-300 order-1">
              <div className="relative mb-3 md:mb-5">
                <div className="w-32 h-40 md:w-52 md:h-64 max-sm:w-20 max-sm:h-40 flex items-center justify-center trophy-container">
                  <div className="trophy blue-trophy animate-float">
                    <svg
                      width="120"
                      height="140"
                      viewBox="0 0 120 140"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="drop-shadow-[0_0_15px_rgba(59,130,246,0.5)] md:w-[180px] md:h-[210px]"
                    >
                      <path
                        d="M30 40C30 35 35 30 60 30C85 30 90 35 90 40V55C90 75 75 90 60 90C45 90 30 75 30 55V40Z"
                        fill="#3B82F6"
                      />
                      <path
                        d="M45 90V110H75V90"
                        stroke="#3B82F6"
                        strokeWidth="8"
                      />
                      <path d="M42 110H78" stroke="#3B82F6" strokeWidth="8" />
                      <rect
                        x="35"
                        y="110"
                        width="50"
                        height="10"
                        rx="2"
                        fill="#0D1A3A"
                      />
                      <rect
                        x="30"
                        y="120"
                        width="60"
                        height="10"
                        rx="2"
                        fill="#0D1A3A"
                      />
                      <circle cx="60" cy="60" r="20" fill="#A4C8FF" />
                      <text
                        x="60"
                        y="65"
                        fontFamily="Arial"
                        fontSize="20"
                        fill="white"
                        textAnchor="middle"
                        fontWeight="bold"
                      >
                        $5,000
                      </text>
                    </svg>
                  </div>
                </div>
                <div className="absolute bottom-0 w-full flex justify-center">
                  <div className="bg-biela-blue rounded-full w-10 h-10 md:w-16 md:h-16 flex items-center justify-center border-2 border-biela-dark-blue trophy-badge animate-pulse-slow">
                    <Star
                      fill="#3B82F6"
                      className="w-6 h-6 md:w-9 md:h-9 text-blue-400 animate-spin-slow"
                    />
                    <Star
                      fill="#3B82F6"
                      className="w-6 h-6 md:w-9 md:h-9 text-blue-400 absolute opacity-75"
                    />
                  </div>
                </div>
              </div>
              <p className="rexton-light text-lg md:text-2xl text-blue-400 tracking-wider">
                {t("contest.secondPlace")}
              </p>
            </div>

            {/* 3rd Place Trophy */}
            <div className="flex flex-col items-center transform hover:scale-105 transition-transform duration-300 order-3">
              <div className="relative mb-3 md:mb-5">
                <div className="w-32 h-40 md:w-52 md:h-64 max-sm:w-20 max-sm:h-40 flex items-center justify-center trophy-container">
                  <div className="trophy purple-trophy animate-float">
                    <svg
                      width="120"
                      height="140"
                      viewBox="0 0 120 140"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="drop-shadow-[0_0_15px_rgba(139,92,246,0.5)] md:w-[180px] md:h-[210px]"
                    >
                      <path
                        d="M30 40C30 35 35 30 60 30C85 30 90 35 90 40V55C90 75 75 90 60 90C45 90 30 75 30 55V40Z"
                        fill="#8B5CF6"
                      />
                      <path
                        d="M45 90V110H75V90"
                        stroke="#8B5CF6"
                        strokeWidth="8"
                      />
                      <path d="M42 110H78" stroke="#8B5CF6" strokeWidth="8" />
                      <rect
                        x="35"
                        y="110"
                        width="50"
                        height="10"
                        rx="2"
                        fill="#0D1A3A"
                      />
                      <rect
                        x="30"
                        y="120"
                        width="60"
                        height="10"
                        rx="2"
                        fill="#0D1A3A"
                      />
                      <circle cx="60" cy="60" r="20" fill="#C4B5FD" />
                      <text
                        x="60"
                        y="65"
                        fontFamily="Arial"
                        fontSize="20"
                        fill="white"
                        textAnchor="middle"
                        fontWeight="bold"
                      >
                        $1,000
                      </text>
                    </svg>
                  </div>
                </div>
                <div className="absolute bottom-0 w-full flex justify-center">
                  <div className="bg-biela-blue rounded-full w-10 h-10 md:w-16 md:h-16 flex items-center justify-center border-2 border-biela-dark-blue trophy-badge animate-pulse-slow">
                    <Star
                      fill="#8B5CF6"
                      className="w-6 h-6 md:w-9 md:h-9 text-purple-400 animate-spin-slow"
                    />
                    <Star
                      fill="#8B5CF6"
                      className="w-6 h-6 md:w-9 md:h-9 text-purple-400 absolute opacity-50"
                    />
                    <Star
                      fill="#8B5CF6"
                      className="w-6 h-6 md:w-9 md:h-9 text-purple-400 absolute opacity-25"
                    />
                  </div>
                </div>
              </div>
              <p className="rexton-light text-lg md:text-2xl text-purple-400 tracking-wider">
                {t("contest.thirdPlace")}
              </p>
            </div>
          </div>
        </div>
        )}

        {!contestEnded && (
        <ProjectDetail
          totalProjects={totalProjects}
          unvoteAndVote={unVoteAndVote}
          limitReached={limitReached}
          loginModal={isNotLogin}
          setLoginModalClose={() => setNotIsLogin(false)}
          setLimitReaceddFalse={() => setLimitReached(false)}
          ownProject={yourOwnPost}
          notVerified={notVerified}
          setYourNotVerified={() => setNotVerified(false)}
          setYourOwnProjectModal={() => setYourOwnPost(false)}
          user={user}
          projects={projects}
          showProjectWall={showProjectWall}
          handleSelectProject={handleSelectProject}
          handleVoteProject={handleVoteProject}
          project={selectedProject}
          onVote={handleVoteProject}
          selectedProject={selectedProject}
          isMobile={isMobile}
          onBackToProjects={handleBackToProjects}
          showHeader={!isMobile} // Only show header in desktop view
          loadMoreProjects={loadMoreProjects}
        />
        )}
        <div className="py-4 text-xs text-center text-white/30 mt-4">
          {t("contest.footerText")}
        </div>
      </div>
    </main>
  );
};
