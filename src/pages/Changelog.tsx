import React, { useState } from "react";
import { Calendar, GitCommit, Zap, Bug, Plus, Users } from "lucide-react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import FeedbackModal from "../components/modals/FeedBackModal";
// Data
const changelogData = [
  {
    version: "v3.2.3",
    dateKey: "changelog.versions.v3.2.3.date",
    changes: [
      { textKey: "changelog.versions.v3.2.3.changes.0", type: "UI/UX" },
      { textKey: "changelog.versions.v3.2.3.changes.1", type: "improvement" },
      { textKey: "changelog.versions.v3.2.3.changes.2", type: "improvement" },
    ],
  },
  {
    version: "v3.2.2",
    dateKey: "changelog.versions.v3.2.2.date",
    changes: [
      { textKey: "changelog.versions.v3.2.2.changes.0", type: "UI/UX" },
      { textKey: "changelog.versions.v3.2.2.changes.1", type: "announcement" },
      { textKey: "changelog.versions.v3.2.2.changes.2", type: "improvement" },
      { textKey: "changelog.versions.v3.2.2.changes.3", type: "improvement" },
      { textKey: "changelog.versions.v3.2.2.changes.4", type: "improvement" },
    ],
  },
  {
    version: "v3.2.1",
    dateKey: "changelog.versions.v3.2.1.date",
    changes: [
      { textKey: "changelog.versions.v3.2.1.changes.0", type: "UI/UX" },
      { textKey: "changelog.versions.v3.2.1.changes.1", type: "feature" },
    ],
  },
  {
    version: "v3.2.0",
    dateKey: "changelog.versions.v3.2.0.date",
    changes: [
      { textKey: "changelog.versions.v3.2.0.changes.0", type: "feature" },
    ],
  },
  {
    version: "v3.1.6",
    dateKey: "changelog.versions.v3.1.6.date",
    changes: [
      { textKey: "changelog.versions.v3.1.6.changes.0", type: "feature" },
    ],
  },
  {
    version: "v3.1.5",
    dateKey: "changelog.versions.v3.1.5.date",
    changes: [
      { textKey: "changelog.versions.v3.1.5.changes.0", type: "bugfix" },
    ],
  },
  {
    version: "v3.1.4",
    dateKey: "changelog.versions.v3.1.4.date",
    changes: [
      { textKey: "changelog.versions.v3.1.4.changes.0", type: "bugfix" },
    ],
  },
  {
    version: "v3.1.3",
    dateKey: "changelog.versions.v3.1.3.date",
    changes: [
      { textKey: "changelog.versions.v3.1.3.changes.0", type: "improvement" },
    ],
  },
  {
    version: "v3.1.2",
    dateKey: "changelog.versions.v3.1.2.date",
    changes: [
      { textKey: "changelog.versions.v3.1.2.changes.0", type: "feature" },
      { textKey: "changelog.versions.v3.1.2.changes.1", type: "feature" },
    ],
  },
  {
    version: "v3.1.1",
    dateKey: "changelog.versions.v3.1.1.date",
    changes: [
      { textKey: "changelog.versions.v3.1.1.changes.0", type: "improvement" },
    ],
  },
  {
    version: "v3.1.0",
    dateKey: "changelog.versions.v3.1.0.date",
    changes: [
      { textKey: "changelog.versions.v3.1.0.changes.0", type: "announcement" },
      { textKey: "changelog.versions.v3.1.0.changes.1", type: "improvement" },
      { textKey: "changelog.versions.v3.1.0.changes.2", type: "UI/UX" },
      { textKey: "changelog.versions.v3.1.0.changes.3", type: "UI/UX" },
      { textKey: "changelog.versions.v3.1.0.changes.4", type: "feature" },
      { textKey: "changelog.versions.v3.1.0.changes.5", type: "feature" },
      { textKey: "changelog.versions.v3.1.0.changes.6", type: "improvement" },
    ],
  },
  {
    version: "v3.0.0",
    dateKey: "changelog.versions.v3.0.0.date",
    changes: [
      { textKey: "changelog.versions.v3.0.0.changes.0", type: "announcement" },
      { textKey: "changelog.versions.v3.0.0.changes.1", type: "UI/UX" },
      { textKey: "changelog.versions.v3.0.0.changes.2", type: "feature" },
      { textKey: "changelog.versions.v3.0.0.changes.3", type: "improvement" },
      { textKey: "changelog.versions.v3.0.0.changes.4", type: "feature" },
    ],
  },
  {
    version: "v2.1.1",
    dateKey: "changelog.versions.v2.1.1.date",
    changes: [
      { textKey: "changelog.versions.v2.1.1.changes.0", type: "feature" },
      { textKey: "changelog.versions.v2.1.1.changes.1", type: "bugfix" },
      { textKey: "changelog.versions.v2.1.1.changes.2", type: "bugfix" },
    ],
  },
  {
    version: "v2.1.0",
    dateKey: "changelog.versions.v2.1.0.date",
    changes: [
      { textKey: "changelog.versions.v2.1.0.changes.0", type: "announcement" },
      { textKey: "changelog.versions.v2.1.0.changes.1", type: "feature" },
      { textKey: "changelog.versions.v2.1.0.changes.2", type: "feature" },
      { textKey: "changelog.versions.v2.1.0.changes.3", type: "bugfix" },
      { textKey: "changelog.versions.v2.1.0.changes.4", type: "feature" },
      { textKey: "changelog.versions.v2.1.0.changes.5", type: "bugfix" },
      { textKey: "changelog.versions.v2.1.0.changes.6", type: "improvement" },
      { textKey: "changelog.versions.v2.1.0.changes.7", type: "UI/UX" },
    ],
  },
  {
    version: "v2.0.4",
    dateKey: "changelog.versions.v2.0.4.date",
    changes: [
      { textKey: "changelog.versions.v2.0.4.changes.0", type: "improvement" },
    ],
  },
  {
    version: "v2.0.3",
    dateKey: "changelog.versions.v2.0.3.date",
    changes: [
      { textKey: "changelog.versions.v2.0.3.changes.0", type: "improvement" },
      { textKey: "changelog.versions.v2.0.3.changes.1", type: "bugfix" },
    ],
  },
  {
    version: "v2.0.2",
    dateKey: "changelog.versions.v2.0.2.date",
    changes: [
      { textKey: "changelog.versions.v2.0.2.changes.0", type: "improvement" },
    ],
  },
  {
    version: "v2.0.1",
    dateKey: "changelog.versions.v2.0.1.date",
    changes: [
      { textKey: "changelog.versions.v2.0.1.changes.0", type: "bugfix" },
      { textKey: "changelog.versions.v2.0.1.changes.1", type: "UI/UX" },
      { textKey: "changelog.versions.v2.0.1.changes.2", type: "improvement" },
      { textKey: "changelog.versions.v2.0.1.changes.3", type: "improvement" },
      { textKey: "changelog.versions.v2.0.1.changes.4", type: "UI/UX" },
      { textKey: "changelog.versions.v2.0.1.changes.5", type: "UI/UX" },
      { textKey: "changelog.versions.v2.0.1.changes.6", type: "UI/UX" },
      { textKey: "changelog.versions.v2.0.1.changes.7", type: "UI/UX" },
      { textKey: "changelog.versions.v2.0.1.changes.8", type: "UI/UX" },
    ],
  },
  {
    version: "v2.0.0",
    dateKey: "changelog.versions.v2.0.0.date",
    changes: [
      { textKey: "changelog.versions.v2.0.0.changes.0", type: "announcement" },
      { textKey: "changelog.versions.v2.0.0.changes.1", type: "feature" },
      { textKey: "changelog.versions.v2.0.0.changes.2", type: "feature" },
      { textKey: "changelog.versions.v2.0.0.changes.3", type: "feature" },
      { textKey: "changelog.versions.v2.0.0.changes.4", type: "feature" },
      { textKey: "changelog.versions.v2.0.0.changes.5", type: "feature" },
    ],
  },
  {
    version: "v1.0.5",
    dateKey: "changelog.versions.v1.0.5.date",
    changes: [
      { textKey: "changelog.versions.v1.0.5.changes.0", type: "feature" },
      { textKey: "changelog.versions.v1.0.5.changes.1", type: "ui/ux" },
    ],
  },
  {
    version: "v1.0.4",
    dateKey: "changelog.versions.v1.0.4.date",
    changes: [
      { textKey: "changelog.versions.v1.0.4.changes.0", type: "bugfix" },
    ],
  },
  {
    version: "v1.0.3",
    dateKey: "changelog.versions.v1.0.3.date",
    changes: [
      { textKey: "changelog.versions.v1.0.3.changes.0", type: "announcement" },
      { textKey: "changelog.versions.v1.0.3.changes.1", type: "feature" },
      { textKey: "changelog.versions.v1.0.3.changes.2", type: "feature" },
      { textKey: "changelog.versions.v1.0.3.changes.3", type: "bugfix" },
    ],
  },
  {
    version: "v1.0.2",
    dateKey: "changelog.versions.v1.0.2.date",
    changes: [
      { textKey: "changelog.versions.v1.0.2.changes.0", type: "feature" },
      { textKey: "changelog.versions.v1.0.2.changes.1", type: "feature" },
      { textKey: "changelog.versions.v1.0.2.changes.2", type: "feature" },
    ],
  },
  {
    version: "v1.0.1",
    dateKey: "changelog.versions.v1.0.1.date",
    changes: [
      { textKey: "changelog.versions.v1.0.1.changes.0", type: "feature" },
      { textKey: "changelog.versions.v1.0.1.changes.1", type: "bugfix" },
    ],
  },
  {
    version: "v1.0.0",
    dateKey: "changelog.versions.v1.0.0.date",
    changes: [
      { textKey: "changelog.versions.v1.0.0.changes.0", type: "announcement" },
      { textKey: "changelog.versions.v1.0.0.changes.1", type: "announcement" },
    ],
  },
];

// StatsCard Component
function StatsCard({ icon: Icon, label, value }) {
  return (
    <div className="base-chat p-6 text-center hover:bg-white/15 transition-all duration-300 shadow-lg">
      <div className="flex justify-center mb-3">
        <Icon className="w-8 h-8 text-green-600 drop-shadow-md" />
      </div>
      <div className="text-2xl font-bold text-white mb-1 drop-shadow-md font-rexton-light">
        {value}
      </div>
      <div className="text-gray-200 text-sm drop-shadow-sm font-light">
        {label}
      </div>
    </div>
  );
}

// ChangelogEntry Component
function ChangelogEntry({ version, dateKey, changes }) {
  const { t } = useTranslation();

  // Extract unique types from all changes
  const extractedTypes = [...new Set(changes.map((change) => change.type))];

  const getTypeIcon = (type) => {
    switch (type.toLowerCase()) {
      case "feature":
        return <Plus className="w-5 h-5" />;
      case "improvement":
        return <Zap className="w-5 h-5" />;
      case "bugfix":
        return <Bug className="w-5 h-5" />;
      case "ui/ux":
        return <Zap className="w-5 h-5" />;
      case "announcement":
        return <GitCommit className="w-5 h-5" />;
      default:
        return <GitCommit className="w-5 h-5" />;
    }
  };

  const getTypeIconForFeature = (type) => {
    switch (type.toLowerCase()) {
      case "feature":
        return <Plus className="w-4 h-4" />;
      case "improvement":
        return <Zap className="w-4 h-4" />;
      case "bugfix":
        return <Bug className="w-4 h-4" />;
      case "ui/ux":
        return <Zap className="w-4 h-4" />;
      case "announcement":
        return <GitCommit className="w-4 h-4" />;
      default:
        return <GitCommit className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type) => {
    switch (type.toLowerCase()) {
      case "feature":
        return "text-green-600 bg-green-600/20 border-green-600/30";
      case "improvement":
        return "text-blue-400 bg-blue-400/20 border-blue-400/30";
      case "bugfix":
        return "text-orange-400 bg-orange-400/20 border-orange-400/30";
      case "ui/ux":
        return "text-purple-400 bg-purple-400/20 border-purple-400/30";
      case "announcement":
        return "text-yellow-400 bg-yellow-400/20 border-yellow-400/30";
      default:
        return "text-gray-400 bg-gray-400/20 border-gray-400/30";
    }
  };

  const getIconColor = (type) => {
    switch (type.toLowerCase()) {
      case "feature":
        return "text-green-600";
      case "improvement":
        return "text-blue-400";
      case "bugfix":
        return "text-orange-400";
      case "ui/ux":
        return "text-purple-400";
      case "announcement":
        return "text-yellow-400";
      default:
        return "text-gray-400";
    }
  };

  return (
    <div className="base-chat p-6 hover:bg-white/15 transition-all duration-300 shadow-lg">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3 flex-wrap">
          <span className="text-2xl font-bold text-white drop-shadow-md font-rexton-light">
            {version}
          </span>
          <div className="flex items-center gap-2 flex-wrap">
            {extractedTypes.map((singleType, index) => (
              <span
                key={index}
                className={`px-3 py-1 rounded-full text-sm font-medium border ${getTypeColor(singleType)} flex items-center gap-2 backdrop-blur-sm`}
              >
                {getTypeIcon(singleType)}
                {t(
                  `changelog.types.${singleType.toLowerCase()}`,
                  singleType.charAt(0).toUpperCase() + singleType.slice(1),
                )}
              </span>
            ))}
          </div>
        </div>
        <div className="flex items-center gap-2 text-gray-200">
          <Calendar className="w-4 h-4" />
          <span className="text-sm drop-shadow-md font-light">
            {t(dateKey)}
          </span>
        </div>
      </div>

      <div className="space-y-3">
        {changes.map((change, index) => (
          <div key={index} className="flex items-center gap-3 text-gray-100">
            <span className={`flex-shrink-0 ${getIconColor(change.type)}`}>
              {getTypeIconForFeature(change.type)}
            </span>
            <span className="leading-relaxed drop-shadow-sm font-light">
              {t(change.textKey)}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}

// Main Changelog Component
function Changelog() {
  const { t } = useTranslation();
  const stats = [
    {
      icon: GitCommit,
      label: t("changelog.totalReleases", "Total Releases"),
      value: "24",
    },
    {
      icon: Users,
      label: t("changelog.activeUsers", "Active Users"),
      value: "12.5K",
    },
    {
      icon: Zap,
      label: t("changelog.featuresAdded", "Features Added"),
      value: "156",
    },
    {
      icon: Bug,
      label: t("changelog.bugsFixed", "Bugs Fixed"),
      value: "89",
    },
  ];
  const [modalOpen, setModalOpen] = useState(false);
  const [modalType, setModalType] = useState("");

  const openModal = (type) => {
    setModalType(type);
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
    setModalType("");
  };

  return (
    <div className="min-h-screen bg-background-primary">
      {/* Header Section */}
      <div className="min-h-screen w-full px-4 sm:px-6 lg:px-20 pb-6 lg:pb-8 pt-[100px] md:pt-[152px]">
        <div className="flex flex-col justify-center items-start gap-3">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            style={{ letterSpacing: "-4.5px" }}
            className="text-md md:text-[30px] font-light rexton-light"
          >
            {t("changelog.title", "CHANGELOG")}
          </motion.h1>
          <motion.p
            className="text-sm md:text-xl manrope-light font-light mb-[48px]"
            style={{ color: "rgb(212, 212, 212)" }}
          >
            {t("changelog.description", "Always. Evolving. Forward")}
          </motion.p>
        </div>

        {/* Stats Section - commented out as in original */}
        {/*<div className="max-w-4xl mx-auto mb-16">*/}
        {/*  <div className="grid grid-cols-2 gap-6">*/}
        {/*    {stats.map((stat, index) => (*/}
        {/*      <StatsCard*/}
        {/*        key={index}*/}
        {/*        icon={stat.icon}*/}
        {/*        label={stat.label}*/}
        {/*        value={stat.value}*/}
        {/*      />*/}
        {/*    ))}*/}
        {/*  </div>*/}
        {/*</div>*/}

        {/* Changelog Entries */}
        <div className="mx-auto">
          <div className="space-y-8">
            {changelogData.map((entry, index) => (
              <ChangelogEntry
                key={index}
                version={entry.version}
                dateKey={entry.dateKey}
                changes={entry.changes}
              />
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-16 pt-8 border-t border-white/20">
          <p className="text-white mb-4 drop-shadow-md font-light">
            {t(
              "changelog.shortCallText",
              "Have suggestions or found a bug? We would love to hear from you!",
            )}
          </p>
          <div className="flex justify-center gap-4">
            <button
              title={t("changelog.reportIssue", "Report an Issue")}
              onClick={() => openModal("issue")}
              className="cursor-pointer text-green-600 hover:text-green-500 font-medium transition-colors duration-200 drop-shadow-md"
            >
              {t("changelog.reportIssue", "Report an Issue")}
            </button>
            <span className="text-gray-400">•</span>
            <button
              title={t("changelog.requestFeature", "Request a Feature")}
              onClick={() => openModal("feature")}
              className=" cursor-pointer text-green-600 hover:text-green-500 font-medium transition-colors duration-200 drop-shadow-md"
            >
              {t("changelog.requestFeature", "Request a Feature")}
            </button>
          </div>
        </div>
      </div>
      <FeedbackModal isOpen={modalOpen} onClose={closeModal} type={modalType} />
    </div>
  );
}

export default Changelog;
