import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";

function VideoPreview() {
  const [searchTerm, setSearchTerm] = useState("");
  const [playingVideo, setPlayingVideo] = useState(null);
  const { t } = useTranslation();

  const videos = [
    {
      id: 1,
      title: "slides.launchProject",
      description: "slides.launchProjectDescription",
      channel: "BIELA Features",
      time: "28.05.2025",
      duration: "0:44",
      isNew: true,
      videoUrl:
        "https://storage-dev.biela.dev/affiliate/resources/1750316909915-Launching-your-project-on-your-own-domain-FM.mp4",
      thumbnailUrl:
        "https://storage-dev.biela.dev/affiliate/resources/1750762298040-yt-thumbnail-(How-to-Launch-your-Project-on-Your-Own-Domain!).jpg",
    },
    {
      id: 2,
      title: "slides.deployProject",
      description: "slides.deployProjectDescription",
      channel: "BIELA Features",
      time: "23.05.2025",
      duration: "0:46",
      isNew: true,
      isMain: true,
      videoUrl:
        "https://storage-dev.biela.dev/affiliate/resources/1750316731317-How-to-Deploy-Your-Project-and-Go-Live-FM-(2).mp4",
      thumbnailUrl:
        "https://storage-dev.biela.dev/affiliate/resources/1750762412788-yt-thumbnail-(How-to-Deploy-Your-Project-and-Go-Live!).jpg",
    },
    {
      id: 3,
      title: "slides.downloadAndImport",
      description: "slides.downloadAndImportDescription",
      channel: "BIELA Features",
      time: "23.05.2025",
      duration: "1:08",
      isNew: true,
      videoUrl:
        "https://storage-dev.biela.dev/affiliate/resources/1750316816208-How-To-Import-A-Chat---Import-A-Project-FM-1.1.mp4",
      thumbnailUrl:
        "https://storage-dev.biela.dev/affiliate/resources/1750762351358-How-to-Download-and-Import-Projects-&-Chats-on-BIELA.DEV!.jpg",
    },
    {
      id: 4,
      title: "slides.shareProject",
      description: "slides.shareProjectDescription",
      channel: "BIELA Features",
      time: "22.05.2025",
      duration: "0:59",
      isNew: true,
      videoUrl:
        "https://storage-dev.biela.dev/affiliate/resources/1750316865101-How-To-Share-A-Project-FM-1.1.mp4",
      thumbnailUrl:
        "https://storage-dev.biela.dev/affiliate/resources/1750762492217-yt-thumbnail-(How-to-Share-Your-Vibe-Code-BIELA-Project!).jpg",
    },
    {
      id: 5,
      title: "slides.newHistoryFeature",
      description: "slides.newHistoryFeatureDescription",
      channel: "BIELA Features",
      time: "14.05.2025",
      duration: "0:44",
      isNew: true,
      videoUrl:
        "https://storage-dev.biela.dev/affiliate/resources/1750317066666-New-Feature-History-Tab-1.1-FM.mp4",
      thumbnailUrl:
        "https://storage-dev.biela.dev/affiliate/resources/1750660961903-History-Tab-Feature.jpg",
    },
    {
      id: 6,
      title: "slides.fixPreview",
      description: "slides.fixPreviewDescription",
      channel: "BIELA Features",
      time: "23.04.2025",
      duration: "0:55",
      isNew: true,
      videoUrl:
        "https://storage-dev.biela.dev/affiliate/resources/*************-How-To-Fix-Preview-FM.mp4",
      thumbnailUrl:
        "https://storage-dev.biela.dev/affiliate/resources/*************-yt-thumbnail-(How-to-Fix-Preview-on-biela.dev!).jpg",
    },
    {
      id: 7,
      title: "slides.signUp",
      description: "slides.signUpDescription",
      channel: "BIELA Features",
      time: "22.04.2025",
      duration: "0:57",
      isNew: true,
      videoUrl:
        "https://storage-dev.biela.dev/affiliate/resources/*************-How-To-create-Account-&-Verify-FM1.1.mp4",
      thumbnailUrl:
        "https://storage-dev.biela.dev/affiliate/resources/*************-How-To-Sign-In.jpg",
    },
    {
      id: 8,
      title: "slides.buildWebsite",
      description: "slides.buildWebsiteDescription",
      channel: "BIELA Features",
      time: "15.04.2025",
      duration: "0:59",
      isNew: true,
      videoUrl:
        "https://storage-dev.biela.dev/affiliate/resources/*************-Create-New-Project-1.1.mp4",
      thumbnailUrl:
        "https://storage-dev.biela.dev/affiliate/resources/*************-yt-thumbnail-(How-to-Build-a-Website-with-biela.dev!).jpg",
    },
  ];

  const filteredVideos = videos.filter((video) => {
    const search = searchTerm.toLowerCase();
    return (
      t(video.title, "").toLowerCase().includes(search) ||
      t(video.description, "").toLowerCase().includes(search)
    );
  });

  const handleVideoClick = (video) => {
    const isSameVideo = playingVideo?.id === video.id;
    const newPlayingVideo = isSameVideo ? null : video;
    setPlayingVideo(newPlayingVideo);

    if (!isSameVideo) {
      setTimeout(() => {
        const videoElement = document.getElementById(`video-${video.id}`);
        if (videoElement) {
          const elementRect = videoElement.getBoundingClientRect();
          const elementTop = elementRect.top + window.pageYOffset;
          const elementHeight = elementRect.height;
          const windowHeight = window.innerHeight;
          const scrollToPosition =
            elementTop - windowHeight / 2 + elementHeight / 2;

          window.scrollTo({
            top: scrollToPosition,
            behavior: "smooth",
          });
        }

        // Adaugă autoplay la iframe
        const iframe = document.querySelector(`#video-${video.id} iframe`);
        if (iframe) {
          const src = new URL(iframe.src);
          src.searchParams.set("autoplay", "1");
          iframe.src = src.toString();
        }
      }, 300);
    }
  };

  // Reorganize videos to ensure proper grid layout
  const organizeVideosForGrid = () => {
    if (!playingVideo) {
      return filteredVideos;
    }

    const playingVideoData = playingVideo
      ? filteredVideos.find((video) => video.id === playingVideo.id)
      : null;

    if (!playingVideoData) return filteredVideos;

    const otherVideos = filteredVideos.filter(
      (video) => video.id !== playingVideo.id,
    );

    return [playingVideoData, ...otherVideos];
  };

  const organizedVideos = organizeVideosForGrid();
  const isVideoPlaying = (video) => playingVideo?.id === video.id;
  const isMainVideo = (video, index) => {
    // If a video is playing, only that video should be main
    // Otherwise, use the first video or the one marked as main
    return playingVideo ? isVideoPlaying(video) : video.isMain || index === 0;
  };

  const mainVideo =
    organizedVideos.find((v) => isVideoPlaying(v)) ||
    organizedVideos[0] ||
    null;
  const sideVideos = organizedVideos
    .filter((v) => v.id !== mainVideo?.id)
    .slice(0, 2);

  useEffect(() => {
    const defaultVideo = videos[1];
    setPlayingVideo(defaultVideo);
  }, []);

  return (
    <div className="min-h-screen w-full px-4 sm:px-6 lg:px-20 pb-6 lg:pb-8 pt-[100px] md:pt-[152px]">
      {/* Header Section */}
      <div className={"flex justify-between items-center flex-wrap gap-4"}>
        <div className="flex flex-col justify-center items-start gap-3">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            style={{ letterSpacing: "-4.5px" }}
            className="text-md md:text-[30px] font-light mx-auto rexton-light"
          >
            {t("HowBielaWorks", "How BIELA Works")}
          </motion.h1>
          <motion.p
            className="text-sm md:text-xl manrope-light font-light mb-[48px]"
            style={{ color: "rgb(212, 212, 212)" }}
          >
            {t("WatchPromptLaunch", "Watch. Prompt. Launch.")}
          </motion.p>
        </div>

        <div className={"flex justify-center"}>
          <div className="relative w-full lg:w-96 mb-[48px]">
            <input
              type="text"
              placeholder={t("SearchVideos", "Search on library...")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-[#19263d] text-white px-4 py-3 pl-11 rounded-lg focus:outline-none placeholder-white font-light text-sm"
              style={{
                fontFamily: "Manrope, sans-serif",
                color: "#fff !important",
              }}
            />
            <svg
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white"
            >
              <circle
                cx="11"
                cy="11"
                r="8"
                stroke="currentColor"
                strokeWidth="2"
              />
              <path
                d="M21 21l-4.35-4.35"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Newspaper-Style Video Grid */}
      <div className="w-full">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 auto-rows-min transition-all duration-500 ease-in-out">
          {filteredVideos.map((video, index) => (
            <div
              key={video.id}
              id={`video-${video.id}`}
              onClick={() => {
                if (!isVideoPlaying(video)) handleVideoClick(video);
              }}
              className={`rounded-xl overflow-hidden cursor-pointer transition-all duration-300 ease-in-out ${
                isMainVideo(video, index)
                  ? "sm:col-span-2 lg:col-span-2 lg:row-span-2"
                  : "col-span-1"
              } ${
                isVideoPlaying(video)
                  ? "bg-white/10 border border-white/30"
                  : "bg-white/3 border border-transparent"
              }`}
            >
              <div
                className={`relative w-full flex items-center justify-center transition-all duration-500 ${
                  isMainVideo(video, index)
                    ? "h-48 sm:h-64 lg:h-[500px]"
                    : "h-40 sm:h-48"
                } ${isVideoPlaying(video) ? "bg-transparent" : "bg-white/5"}`}
              >
                {isVideoPlaying(video) ? (
                  <video
                    className="w-full h-full object-cover rounded-t-xl"
                    src={video.videoUrl}
                    controls
                    controlsList="nodownload noremoteplayback noplaybackrate"
                    disablePictureInPicture
                    autoPlay
                    onLoadStart={() => console.log("Video loading started")}
                    onError={(e) => console.error("Video error:", e)}
                    style={{
                      background: "transparent",
                    }}
                  />
                ) : (
                  <>
                    <img
                      src={video.thumbnailUrl}
                      alt={t(video.title)}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.style.display = "none";
                        e.target.nextSibling.style.display = "flex";
                      }}
                    />
                    <div
                      className="absolute inset-0 flex items-center justify-center"
                      style={{ display: "none" }}
                    >
                      <svg
                        width={isMainVideo(video, index) ? "80" : "48"}
                        height={isMainVideo(video, index) ? "80" : "48"}
                        viewBox="0 0 24 24"
                        fill="none"
                        className="text-white/60 transition-all duration-300 hover:text-white/80"
                      >
                        <polygon points="5,3 19,12 5,21" fill="currentColor" />
                      </svg>
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <svg
                        width={isMainVideo(video, index) ? "64" : "40"}
                        height={isMainVideo(video, index) ? "64" : "40"}
                        viewBox="0 0 24 24"
                        fill="none"
                        className="text-white/60 transition-colors duration-300 hover:text-white/80 sm:w-12 sm:h-12 lg:w-16 lg:h-16"
                      >
                        <polygon points="5,3 19,12 5,21" fill="currentColor" />
                      </svg>
                    </div>
                  </>
                )}
                {!isVideoPlaying(video) && (
                  <span className="absolute bottom-3 right-3 text-white text-xs font-medium">
                    {video.duration}
                  </span>
                )}

                {video.isNew && (
                  <span
                    className="absolute top-2 left-2 sm:top-3 sm:left-3 flex justify-center items-center gap-2 h-8 sm:h-[38px] px-2 sm:px-4 rounded-full border border-white/30"
                    style={{
                      fontFamily: "Manrope, sans-serif",
                      fontSize: "12px",
                      fontWeight: "300",
                      lineHeight: "20px",
                      color: "rgba(255, 255, 255, 0.8)",
                      background: "transparent",
                    }}
                  >
                    {t("NewReleased", "NEW RELEASED")}
                  </span>
                )}
              </div>

              <div
                className={`${isMainVideo(video, index) ? "p-4 lg:p-6" : "p-3 lg:p-4"} transition-all duration-300`}
              >
                <h3
                  className={`text-white mb-3 leading-tight ${isMainVideo(video, index) ? "sm:text-lg lg:text-[20px]" : "sm:text-base lg:text-[16px]"}`}
                  style={{
                    fontFamily: "Manrope, sans-serif",
                    fontSize: isMainVideo(video, index) ? "18px" : "14px",
                    fontWeight: isMainVideo(video, index) ? "500" : "500",
                    lineHeight: "1.3",
                  }}
                >
                  {t(video.title)}
                </h3>
                <p
                  className={`text-white/70 mb-4 ${isMainVideo(video, index) ? "sm:text-base lg:text-[16px]" : "sm:text-sm lg:text-[14px] line-clamp-2 sm:line-clamp-3"}`}
                  style={{
                    fontFamily: "Manrope, sans-serif",
                    fontSize: isMainVideo(video, index) ? "14px" : "12px",
                    lineHeight: "1.4",
                  }}
                >
                  {t(video.description)}
                </p>
                <div
                  className={`text-white/60 flex items-center gap-2 ${
                    isMainVideo(video, index) ? "text-xs sm:text-sm" : "text-xs"
                  } ${
                    isVideoPlaying(video) ? "text-white/60" : "text-white/60"
                  }`}
                >
                  <span>{video.time}</span>
                  {isVideoPlaying(video) && (
                    <>
                      <span className="text-white/40">•</span>
                      <span className="text-white/60 font-medium">
                        {t("Playing", "Playing")}
                      </span>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {filteredVideos.length === 0 && (
        <div className="text-center py-12 lg:py-16">
          <div className="text-white/50 mb-4">
            <svg
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              className="mx-auto mb-4 lg:w-16 lg:h-16"
            >
              <circle
                cx="11"
                cy="11"
                r="8"
                stroke="currentColor"
                strokeWidth="2"
              />
              <path
                d="M21 21l-4.35-4.35"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <h3 className="text-lg lg:text-xl font-semibold text-white/70 mb-2">
            {t("NoVideosFound", "No videos found.")}
          </h3>
          <p className="text-sm lg:text-base text-white/50">
            {t(
              "TryAdjustingYourSearchTerms",
              "Try adjusting your search terms",
            )}
          </p>
        </div>
      )}
    </div>
  );
}

export default VideoPreview;
