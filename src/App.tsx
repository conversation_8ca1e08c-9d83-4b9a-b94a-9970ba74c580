import React, { lazy, Suspense, useEffect, useState } from "react";
import { Routes, Route, useParams, useLocation } from "react-router-dom";
import TopNavbar from "./components/navigation/TopNavbar";
import HeroSection from "./components/sections/HeroSection";
import Terms from "./pages/Terms";
import Privacy from "./pages/Privacy";
import VideoPreview from "./pages/VideoPreview";
import { useTranslation } from "react-i18next";
import { useInView } from "react-intersection-observer";
import { Helmet } from "react-helmet-async";
import CanonicalLink from "./components/navigation/CanonicalLink";
import CompetitionTerms from "./pages/CompetitionTerms";
import { ContestPage } from "./pages/Contest";
import { SpecificProjectContest } from "./pages/SpecificProjectContest";
import { ContestRedirector } from "./components/useContestRedirect";
import ConsentBanner from "./components/consentbanner/ConsentBanner.jsx";
import { UserProvider } from "./lib/context/userContext";
import Changelog from "./pages/Changelog";

const LightFooter = lazy(
  () => import("./components/navigation/LightFooter.jsx"),
);
const VideoGallery = lazy(
  () => import("./components/sections/VideoGallery.jsx"),
);
const JoinOurNetwork = lazy(
  () => import("./components/sections/JoinNetwork.jsx"),
);
const Billing = lazy(
  () => import("./components/sections/billing-cards/Billing.jsx"),
);
const FAQ = lazy(() => import("./components/sections/FAQ.jsx"));
const Partners = lazy(() => import("./components/sections/Partners.jsx"));

function ReferralRedirector() {
  const { search } = useLocation();
  useEffect(() => {
    const params = new URLSearchParams(search);
    const ref = params.get("referral");
    if (ref) {
      window.location.replace(
        `${import.meta.env.VITE_IDE_URL}/register?referral=${encodeURIComponent(ref)}`,
      );
    }
  }, [search]);
  return null;
}

const LanguageWrapper = ({ children }) => {
  const { i18n } = useTranslation();
  const { lang } = useParams();

  useEffect(() => {
    const newLang = lang || "en"; // Default to English if no lang is provided
    if (i18n.language !== newLang) {
      i18n.changeLanguage(newLang);
    }
  }, [lang, i18n]);
  return children;
};

const AppContent = () => {
  return (
    // <MaintenanceProvider children={undefined}>
    <UserProvider>
      <App />
    </UserProvider>
    // </MaintenanceProvider>
  );
};

const App = () => {
  return (
    <>
      <ReferralRedirector />
      <div className="min-h-screen">
        <TopNavbar />
        <CanonicalLink />
        <Suspense fallback={<div></div>}>
          <Routes>
            {/* Default English Route */}
            <Route path="/" element={<MainLayout />} />

            {/* Language-based routes (excluding English) */}
            <Route
              path="/:lang"
              element={
                <LanguageWrapper>
                  <MainLayout />
                </LanguageWrapper>
              }
            />
            <Route
              path="/:lang/terms"
              element={
                <LanguageWrapper>
                  <TermsLayout />
                </LanguageWrapper>
              }
            />
            <Route
              path="/:lang/privacy"
              element={
                <LanguageWrapper>
                  <PrivacyLayout />
                </LanguageWrapper>
              }
            />
            <Route
              path="/:lang/explore"
              element={
                <LanguageWrapper>
                  <VideoLayout />
                </LanguageWrapper>
              }
            />

            <Route
              path="/:lang/changelog"
              element={
                <LanguageWrapper>
                  <ChangeLogLayout />
                </LanguageWrapper>
              }
            />
            <Route
              path="/:lang/hackathon"
              element={
                <LanguageWrapper>
                  <ContestLayout />
                </LanguageWrapper>
              }
            />
            <Route path="/hackathon/:id" element={<SpecificProjectContest />} />
            <Route
              path="/:lang/biela-development-competition-terms"
              element={
                <LanguageWrapper>
                  <CompetitionTermsLayout />
                </LanguageWrapper>
              }
            />

            <Route path="/contest" element={<ContestRedirector />} />
            <Route path="/contest/:id" element={<ContestRedirector />} />
            <Route path="/affiliates" element={<JoinOurNetworkLayout />} />
            <Route path="/changelog" element={<ChangeLogLayout />} />
            <Route
              path="/:lang/affiliates"
              element={<JoinOurNetworkLayout />}
            />
            <Route path="/:lang/contest" element={<ContestRedirector />} />
            <Route path="/:lang/contest/:id" element={<ContestRedirector />} />

            {/* English routes without prefix */}
            <Route path="/hackathon" element={<ContestLayout />} />
            <Route
              path="/biela-development-competition-terms"
              element={
                <LanguageWrapper>
                  <CompetitionTermsLayout />
                </LanguageWrapper>
              }
            />
            <Route path="/terms" element={<TermsLayout />} />
            <Route path="/privacy" element={<PrivacyLayout />} />
            <Route path="/explore" element={<VideoLayout />} />
          </Routes>
        </Suspense>
        <ConsentBanner />
      </div>
    </>
  );
};

const MainLayout = () => {
  const { t, i18n } = useTranslation();
  const [componentKey, setComponentKey] = useState(0);

  const { ref, inView } = useInView({ triggerOnce: false, threshold: 0 });

  useEffect(() => {
    if (inView) {
      setComponentKey((prevKey) => prevKey + 1);
    }
  }, [inView]);

  const faqItems = [
    {
      key: "whatAreTokensQuestion",
      answer: ["whatAreTokensAnswer1", "whatAreTokensAnswer2"],
      createdAt: "2025-06-01",
    },
    {
      key: "freePlanTokensQuestion",
      answer: ["freePlanTokensAnswer"],
      createdAt: "2025-06-01",
    },
    {
      key: "outOfTokensQuestion",
      answer: ["outOfTokensAnswer"],
      isTrans: true,
      createdAt: "2025-06-01",
    },
    {
      key: "changePlanQuestion",
      answer: ["changePlanAnswer"],
      isTrans: true,
      createdAt: "2025-06-01",
    },
    {
      key: "rolloverQuestion",
      answer: ["rolloverAnswer"],
      createdAt: "2025-06-01",
    },
  ];
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqItems.map((item) => ({
      "@type": "Question",
      name: t(item.key),
      acceptedAnswer: {
        "@type": "Answer",
        text: item.answer.map((ans) => t(ans)).join("<br>"),
      },
    })),
  };
  return (
    <>
      <Helmet>
        <title>{i18n.t("meta.home.title")}</title>
        <meta name="description" content={i18n.t("meta.home.description")} />
        <meta property="og:title" content={i18n.t("meta.home.title")} />
        <meta
          property="og:description"
          content={i18n.t("meta.home.description")}
        />
        <link rel="preload" as="image" href="/live-mic.svg" />
        <script type="application/ld+json">{JSON.stringify(faqSchema)}</script>
      </Helmet>
      <main>
        <HeroSection />
        <VideoGallery />

        <Billing />
        <FAQ />
        <Partners />
        <LightFooter />
      </main>
    </>
  );
};

const ContestLayout = () => {
  const { i18n } = useTranslation();
  return (
    <>
      <Helmet>
        <meta name="description" content={i18n.t("meta.contest.description")} />
        <meta property="og:title" content={i18n.t("meta.contest.title")} />
        <meta
          property="og:description"
          content={i18n.t("meta.contest.description")}
        />
        <title>{i18n.t("meta.contest.title")}</title>
      </Helmet>
      <ContestPage />
      <LightFooter />
    </>
  );
};

const ChangeLogLayout = () => {
  const { i18n } = useTranslation();
  return (
    <>
      <Helmet>
        <meta
          name="description"
          content={i18n.t("meta.changelogSection.description")}
        />
        <meta property="og:title" content={i18n.t("meta.changelogSection.title")} />
        <meta
          property="og:description"
          content={i18n.t("meta.changelogSection.description")}
        />
        <title>{i18n.t("meta.changelogSection.title")}</title>
      </Helmet>
      <Changelog />
      <LightFooter />
    </>
  );
};

const JoinOurNetworkLayout = () => {
  const { i18n } = useTranslation();
  return (
    <>
      <Helmet>
        {/*<meta name="description" content={i18n.t("meta.contest.description")} />*/}
        {/*<meta property="og:title" content={i18n.t("meta.contest.title")} />*/}
        {/*<meta*/}
        {/*  property="og:description"*/}
        {/*  content={i18n.t("meta.contest.description")}*/}
        {/*/>*/}
        {/*<title>{i18n.t("meta.contest.title")}</title>*/}
      </Helmet>
      <JoinOurNetwork />
      <LightFooter />
    </>
  );
};

const TermsLayout = () => {
  const { i18n } = useTranslation();
  return (
    <>
      <Helmet>
        <meta name="description" content={i18n.t("meta.terms.description")} />
        <meta property="og:title" content={i18n.t("meta.terms.title")} />
        <meta
          property="og:description"
          content={i18n.t("meta.terms.description")}
        />
        <title>{i18n.t("meta.terms.title")}</title>
      </Helmet>
      <Terms />
      <LightFooter />
    </>
  );
};

const CompetitionTermsLayout = () => {
  const { i18n } = useTranslation();
  return (
    <>
      <Helmet>
        <meta
          name="description"
          content={i18n.t("meta.competitionterms.description")}
        />
        <meta
          property="og:title"
          content={i18n.t("meta.competitionterms.title")}
        />
        <meta
          property="og:description"
          content={i18n.t("meta.competitionterms.description")}
        />
        <title>{i18n.t("meta.competitionterms.title")}</title>
      </Helmet>
      <CompetitionTerms />
      <LightFooter />
    </>
  );
};
const PrivacyLayout = () => {
  const { i18n } = useTranslation();
  return (
    <>
      <Helmet>
        <meta name="description" content={i18n.t("meta.privacy.description")} />
        <meta property="og:title" content={i18n.t("meta.privacy.title")} />
        <meta
          property="og:description"
          content={i18n.t("meta.privacy.description")}
        />
        <title>{i18n.t("meta.privacy.title")}</title>
      </Helmet>
      <Privacy />
      <LightFooter />
    </>
  );
};

const VideoLayout = () => {
  const { t } = useTranslation();

  const ogImage = "https://biela.dev/assets/og-image-how-to.jpg";
  return (
    <>
      <Helmet>
        <title>{t("meta.howTo.title")}</title>
        <meta name="description" content={t("meta.howTo.description")} />
        <meta name="keywords" content={t("meta.howTo.keywords")} />

        {/* Open Graph */}
        <meta property="og:type" content="article" />
        <meta property="og:title" content={t("meta.howTo.ogTitle")} />
        <meta
          property="og:description"
          content={t("meta.howTo.ogDescription")}
        />
        <meta property="og:image" content={ogImage} />

        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={t("meta.howTo.twitterTitle")} />
        <meta
          name="twitter:description"
          content={t("meta.howTo.twitterDescription")}
        />
        <meta name="twitter:image" content={ogImage} />
      </Helmet>
      <main>
        <VideoPreview />
        <LightFooter />
      </main>
    </>
  );
};

export default AppContent;
