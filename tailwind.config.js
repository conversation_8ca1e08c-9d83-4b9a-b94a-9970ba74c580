/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        rexton: ["Rexton-Light", "sans-serif"],
        manrope: ["Manrope", "sans-serif"],
      },
      colors: {
        "biela-dark-blue": "#0A1023",
        "biela-blue": "#0D1A3A",
        "biela-teal": "#0A514B",
        "biela-green": "#14B072",
        "biela-accent": "#44FFA1",
        dark: {
          50: "#f8fafc",
          100: "#f1f5f9",
          200: "#e2e8f0",
          300: "#cbd5e1",
          400: "#94a3b8",
          500: "#64748b",
          600: "#475569",
          700: "#334155",
          800: "#1e293b",
          900: "#0f172a",
          950: "#0a1628",
        },
        primary: {
          50: "#f0fdf4",
          100: "#dcfce7",
          200: "#bbf7d0",
          300: "#86efac",
          400: "#4ade80",
          500: "#22c55e",
          600: "#16a34a",
          700: "#15803d",
          800: "#166534",
          900: "#14532d",
        },
        navy: {
          50: "#1A1D2E",
          100: "#161926",
          200: "#13151F",
          300: "#0F1119",
          400: "#0C0D14",
          500: "#0A0B14",
          600: "#080912",
          700: "#06070F",
          800: "#04050A",
          900: "#020305",
        },
      },
      backgroundImage: {
        "gradient-biela":
          "linear-gradient(to right, #0A1023, #0D1A3A, #0A514B, #14B072)",
      },
      animation: {
        "pulse-slow": "pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "spin-slow": "spin 15s linear infinite",
        "ping-slow": "ping 3s cubic-bezier(0, 0, 0.2, 1) infinite",
      },
      dropShadow: {
        "glow-blue": "0 0 10px rgba(59, 130, 246, 0.5)",
        "glow-green": "0 0 15px rgba(16, 185, 129, 0.6)",
        "glow-purple": "0 0 10px rgba(139, 92, 246, 0.5)",
        "glow-accent": "0 0 10px rgba(68, 255, 161, 0.7)",
      },
    },
  },
  plugins: [],
};
