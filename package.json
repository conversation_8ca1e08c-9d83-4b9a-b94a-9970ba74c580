{"name": "dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node ./api/index", "build": "npm run build:client && npm run build:server", "build:client": "vite build --outDir dist/client", "build:server": "vite build --ssr src/entry-server.jsx --outDir dist/server", "preview": "cross-env NODE_ENV=production node ./api/index"}, "dependencies": {"@react-pdf/renderer": "^4.3.0", "@types/lodash": "^4.17.17", "chalk": "^5.4.1", "compression": "^1.8.0", "dat.gui": "^0.7.9", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "framer-motion": "^10.18.0", "i18n": "^0.15.1", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-fs-backend": "^2.6.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.488.0", "prettier": "^3.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-i18next": "^15.4.1", "react-icons": "^5.0.0", "react-intersection-observer": "^9.16.0", "react-phone-number-input": "^3.4.12", "react-router-dom": "^6.30.0", "react-router-hash-link": "^2.4.3", "recharts": "^2.15.2", "sirv": "^3.0.1", "sprintf-js": "^1.1.3", "vite-plugin-svgr": "^4.3.0", "zustand": "^5.0.3"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "postcss": "^8.5.6", "sass-embedded": "^1.89.2", "tailwindcss": "^3.4.3", "vite": "^6.2.5"}}